{"version": 3, "file": "FunctionsClient.js", "sourceRoot": "", "sources": ["../../src/FunctionsClient.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AACvC,OAAO,EAEL,mBAAmB,EACnB,kBAAkB,EAClB,mBAAmB,EAGnB,cAAc,GACf,MAAM,SAAS,CAAA;AAEhB,MAAM,OAAO,eAAe;IAM1B,YACE,GAAW,EACX,EACE,OAAO,GAAG,EAAE,EACZ,WAAW,EACX,MAAM,GAAG,cAAc,CAAC,GAAG,MAKzB,EAAE;QAEN,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,WAAW,CAAC,CAAA;IACxC,CAAC;IAED;;;OAGG;IACH,OAAO,CAAC,KAAa;QACnB,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,UAAU,KAAK,EAAE,CAAA;IAChD,CAAC;IAED;;;;OAIG;IACG,MAAM,CACV,YAAoB,EACpB,UAAiC,EAAE;;;YAEnC,IAAI;gBACF,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,OAAO,CAAA;gBACvD,IAAI,QAAQ,GAA2B,EAAE,CAAA;gBACzC,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAA;gBACxB,IAAI,CAAC,MAAM,EAAE;oBACX,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;iBACrB;gBACD,8CAA8C;gBAC9C,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,YAAY,EAAE,CAAC,CAAA;gBAClD,IAAI,MAAM,IAAI,MAAM,KAAK,KAAK,EAAE;oBAC9B,QAAQ,CAAC,UAAU,CAAC,GAAG,MAAM,CAAA;oBAC7B,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAA;iBACpD;gBACD,IAAI,IAAS,CAAA;gBACb,IACE,YAAY;oBACZ,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EACzF;oBACA,IACE,CAAC,OAAO,IAAI,KAAK,WAAW,IAAI,YAAY,YAAY,IAAI,CAAC;wBAC7D,YAAY,YAAY,WAAW,EACnC;wBACA,2CAA2C;wBAC3C,8EAA8E;wBAC9E,QAAQ,CAAC,cAAc,CAAC,GAAG,0BAA0B,CAAA;wBACrD,IAAI,GAAG,YAAY,CAAA;qBACpB;yBAAM,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;wBAC3C,eAAe;wBACf,QAAQ,CAAC,cAAc,CAAC,GAAG,YAAY,CAAA;wBACvC,IAAI,GAAG,YAAY,CAAA;qBACpB;yBAAM,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,YAAY,YAAY,QAAQ,EAAE;wBAC9E,iCAAiC;wBACjC,0DAA0D;wBAC1D,IAAI,GAAG,YAAY,CAAA;qBACpB;yBAAM;wBACL,+BAA+B;wBAC/B,QAAQ,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAA;wBAC7C,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;qBACpC;iBACF;gBAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE;oBAChD,MAAM,EAAE,MAAM,IAAI,MAAM;oBACxB,qCAAqC;oBACrC,0BAA0B;oBAC1B,0BAA0B;oBAC1B,iCAAiC;oBACjC,OAAO,gDAAO,QAAQ,GAAK,IAAI,CAAC,OAAO,GAAK,OAAO,CAAE;oBACrD,IAAI;iBACL,CAAC,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,EAAE;oBACtB,MAAM,IAAI,mBAAmB,CAAC,UAAU,CAAC,CAAA;gBAC3C,CAAC,CAAC,CAAA;gBAEF,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;gBAC1D,IAAI,YAAY,IAAI,YAAY,KAAK,MAAM,EAAE;oBAC3C,MAAM,IAAI,mBAAmB,CAAC,QAAQ,CAAC,CAAA;iBACxC;gBAED,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;oBAChB,MAAM,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAA;iBACvC;gBAED,IAAI,YAAY,GAAG,CAAC,MAAA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,mCAAI,YAAY,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;gBAC9F,IAAI,IAAS,CAAA;gBACb,IAAI,YAAY,KAAK,kBAAkB,EAAE;oBACvC,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAA;iBAC7B;qBAAM,IAAI,YAAY,KAAK,0BAA0B,EAAE;oBACtD,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAA;iBAC7B;qBAAM,IAAI,YAAY,KAAK,mBAAmB,EAAE;oBAC/C,IAAI,GAAG,QAAQ,CAAA;iBAChB;qBAAM,IAAI,YAAY,KAAK,qBAAqB,EAAE;oBACjD,IAAI,GAAG,MAAM,QAAQ,CAAC,QAAQ,EAAE,CAAA;iBACjC;qBAAM;oBACL,kBAAkB;oBAClB,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAA;iBAC7B;gBAED,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAA;aACvC;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO;oBACL,IAAI,EAAE,IAAI;oBACV,KAAK;oBACL,QAAQ,EACN,KAAK,YAAY,kBAAkB,IAAI,KAAK,YAAY,mBAAmB;wBACzE,CAAC,CAAC,KAAK,CAAC,OAAO;wBACf,CAAC,CAAC,SAAS;iBAChB,CAAA;aACF;;KACF;CACF"}