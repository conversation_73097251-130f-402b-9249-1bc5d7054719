# 🧪 Руководство по Тестированию - Сайт Altius

## 🎯 Что Тестируем

**Сайт бадминтонного клуба "Altius"** - современный веб-сайт с публичной частью и административной панелью.

## 🌐 Публичная Часть Сайта

### 📄 **Основные Страницы**
- **Главная** (`/`) - презентация клуба, карточки залов
- **О клубе** (`/about`) - информация о клубе
- **Услуги** (`/services`) - описание услуг
- **Контакты** (`/contact`) - контактная информация
- **Блог** (`/blog`) - новости и события

### 🏟️ **Страницы Залов** (`/halls/[id]`)
**Что проверить:**
- ✅ Отображение информации о зале (название, описание, цены)
- ✅ Галерея изображений с возможностью полноэкранного просмотра
- ✅ Список характеристик и удобств
- ✅ Корректное отображение на мобильных устройствах

### 📝 **Блог и События** (`/blog`, `/blog/[slug]`)
**Что проверить:**
- ✅ Список всех постов с превью
- ✅ Фильтрация по категориям (Все/Посты/События)
- ✅ Детальные страницы постов
- ✅ **Слайдеры изображений** в постах (если есть)
- ✅ Полноэкранный просмотр изображений
- ✅ Навигация между изображениями (стрелки, миниатюры)
- ✅ Счетчик просмотров

## 🛠️ Административная Панель

### 🔐 **Доступ к Админке**
- **URL**: `/admin`
- **Что проверить**: доступность интерфейса

### 🏟️ **Управление Залами** (`/admin/halls`)
**Что проверить:**
- ✅ Список всех залов
- ✅ Создание нового зала (`/admin/halls/new`)
- ✅ Редактирование существующего зала (`/admin/halls/[id]`)
- ✅ **Загрузка изображений** (drag & drop)
- ✅ Сохранение изменений
- ✅ Активация/деактивация залов

### 📝 **Управление Постами** (`/admin/posts`)
**Что проверить:**
- ✅ Список всех постов
- ✅ Создание нового поста (`/admin/posts/new`)
- ✅ Редактирование существующего поста (`/admin/posts/[id]`)
- ✅ **Система галерей** - загрузка множественных изображений
- ✅ **Управление слайдерами** - добавление `<div data-slider="gallery"></div>` в контент
- ✅ Изменение статуса (черновик/опубликовано)
- ✅ Автогенерация URL slug

## 🖼️ Особое Внимание - Слайдеры Изображений

### **Как Работает:**
1. В админке создается пост с галереей изображений
2. В контент добавляется `<div data-slider="gallery"></div>`
3. На публичной странице плейсхолдер заменяется слайдером

### **Что Тестировать:**
- ✅ Загрузка изображений в галерею (админка)
- ✅ Отображение слайдера на публичной странице
- ✅ Навигация стрелками влево/вправо
- ✅ Клик по миниатюрам
- ✅ Полноэкранный режим
- ✅ Закрытие полноэкранного режима (ESC, клик вне)
- ✅ Touch-навигация на мобильных

## 📱 Адаптивность

**Проверить на устройствах:**
- 📱 **Мобильные** (320px - 768px)
- 📟 **Планшеты** (768px - 1024px)
- 💻 **Десктоп** (1024px+)

**Что проверить:**
- ✅ Корректное отображение всех элементов
- ✅ Читаемость текста
- ✅ Удобство навигации
- ✅ Работа слайдеров на touch-устройствах

## 🔍 Основные Сценарии Тестирования

### **Сценарий 1: Просмотр Зала**
1. Перейти на главную страницу
2. Кликнуть на карточку зала
3. Проверить отображение информации
4. Открыть галерею изображений
5. Проверить полноэкранный режим

### **Сценарий 2: Чтение Поста с Слайдером**
1. Перейти в блог (`/blog`)
2. Выбрать пост с изображениями
3. Найти слайдер в тексте поста
4. Проверить навигацию по изображениям
5. Открыть полноэкранный режим

### **Сценарий 3: Создание Поста (Админка)**
1. Перейти в админку (`/admin/posts`)
2. Создать новый пост
3. Загрузить изображения в галерею
4. Добавить `<div data-slider="gallery"></div>` в контент
5. Сохранить и опубликовать
6. Проверить на публичной странице

### **Сценарий 4: Управление Залом (Админка)**
1. Перейти в управление залами (`/admin/halls`)
2. Редактировать существующий зал
3. Загрузить новые изображения
4. Изменить характеристики
5. Сохранить изменения
6. Проверить на публичной странице

## ⚠️ Возможные Проблемы

### **Изображения**
- Не загружаются изображения → проверить настройки Supabase
- Слайдер не работает → проверить наличие изображений в галерее
- Ошибки в консоли → проверить переменные окружения

### **Админка**
- Не сохраняются изменения → проверить подключение к базе данных
- Ошибки загрузки файлов → проверить настройки Storage

### **Производительность**
- Медленная загрузка → проверить размер изображений
- Проблемы на мобильных → проверить адаптивность

## 🎯 Критерии Успешного Тестирования

### **Обязательно Работает:**
- ✅ Все страницы загружаются без ошибок
- ✅ Слайдеры изображений функционируют
- ✅ Админка позволяет создавать/редактировать контент
- ✅ Сайт корректно отображается на всех устройствах

### **Желательно Проверить:**
- ✅ SEO метаданные в заголовках страниц
- ✅ Скорость загрузки страниц
- ✅ Отсутствие ошибок в консоли браузера
- ✅ Корректная работа форм

## 📞 Обратная Связь

**При обнаружении проблем укажите:**
- 🌐 **URL страницы** где возникла проблема
- 📱 **Устройство и браузер** (Chrome/Safari/Firefox)
- 📸 **Скриншот** проблемы
- 📝 **Шаги для воспроизведения**

---

**Удачного тестирования! 🚀**
