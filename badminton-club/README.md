# 🏸 Altius - Сайт Бадминтонного Клуба

Современный веб-сайт для бадминтонного клуба "Altius" в Кишиневе с полной системой управления контентом и административной панелью.

## 🎯 Описание Проекта

**Altius** - это профессиональный веб-сайт бадминтонного клуба, созданный с использованием современных технологий. Сайт предоставляет полную информацию о клубе, его услугах, залах и событиях, а также включает мощную административную панель для управления контентом.

## ✨ Основные Возможности

### 🏠 **Публичная Часть Сайта**

#### 📄 **Информационные Страницы**
- **Главная страница** - презентация клуба с активными залами
- **О клубе** - история, миссия и команда Altius
- **Услуги** - детальное описание предлагаемых услуг
- **Контакты** - адрес, телефоны, карта проезда

#### 🏟️ **Система Залов**
- **Каталог залов** - обзор всех доступных залов
- **Детальные страницы залов** с:
  - Профессиональными фотографиями
  - Техническими характеристиками
  - Списком удобств и оборудования
  - Ценами и расписанием работы
  - Галереей изображений с слайдером

#### 📝 **Блог и События**
- **Новости клуба** - актуальная информация
- **Анонсы событий** - турниры, мастер-классы, соревнования
- **Фильтрация контента** по категориям (новости/события)
- **Детальные страницы постов** с:
  - Полноэкранным просмотром изображений
  - Слайдерами для множественных фото
  - Информацией о событиях (дата, место)
  - Системой тегов
  - Счетчиком просмотров

### 🛠️ **Административная Панель**

#### 🏟️ **Управление Залами**
- **Создание и редактирование** залов
- **Загрузка изображений** с drag & drop функциональностью
- **Управление характеристиками** и удобствами
- **Настройка цен** и расписания
- **Активация/деактивация** залов

#### 📝 **Управление Контентом**
- **Создание постов и событий** через удобный редактор
- **Система галерей** с множественными изображениями
- **Слайдеры изображений** в постах
- **Управление статусами** (черновик/опубликовано)
- **SEO оптимизация** с мета-описаниями
- **Система тегов** для категоризации

#### 📊 **Аналитика и Статистика**
- **Счетчики просмотров** постов
- **Статистика залов** и бронирований
- **Обзор активности** в админ-панели

## 🛠️ Технологический Стек

### **Frontend**
- **Next.js 15** - React фреймворк с SSR/SSG
- **TypeScript** - типизированный JavaScript
- **Tailwind CSS** - utility-first CSS фреймворк
- **Lucide React** - современные иконки

### **Backend & База Данных**
- **Supabase** - Backend-as-a-Service платформа
- **PostgreSQL** - реляционная база данных
- **Row Level Security (RLS)** - безопасность на уровне строк
- **Real-time subscriptions** - обновления в реальном времени

### **Файловое Хранилище**
- **Supabase Storage** - облачное хранилище файлов
- **Автоматическая оптимизация** изображений
- **CDN доставка** контента

### **Деплой и Хостинг**
- **Vercel** - платформа для деплоя Next.js приложений
- **Автоматический CI/CD** из GitHub
- **Edge функции** для оптимальной производительности

## 📁 Структура Проекта

```
badminton-club/
├── src/
│   ├── app/                    # App Router страницы
│   │   ├── admin/             # Административная панель
│   │   │   ├── halls/         # Управление залами
│   │   │   └── posts/         # Управление постами
│   │   ├── blog/              # Блог и события
│   │   ├── halls/             # Страницы залов
│   │   └── ...                # Другие страницы
│   ├── components/            # React компоненты
│   │   ├── Header.tsx         # Навигация
│   │   ├── Footer.tsx         # Подвал сайта
│   │   ├── MediaGallery.tsx   # Галерея изображений
│   │   ├── PostImageSlider.tsx # Слайдер для постов
│   │   └── ...                # Другие компоненты
│   ├── lib/                   # Утилиты и конфигурация
│   │   └── supabase.ts        # Клиент Supabase
│   └── types/                 # TypeScript типы
├── public/                    # Статические файлы
├── supabase-schema.sql        # Схема базы данных
└── ...                        # Конфигурационные файлы
```

## 🗄️ База Данных

### **Таблицы**

#### **halls** - Залы
- Информация о залах (название, описание, цены)
- Характеристики и удобства
- Галереи изображений и видео
- Статус активности

#### **posts** - Посты и События
- Заголовки и контент (поддержка HTML)
- Категории (новости/события)
- Галереи изображений для слайдеров
- SEO метаданные и теги
- Информация о событиях (дата, место)

#### **bookings** - Бронирования (готово к расширению)
- Система бронирования залов
- Пользовательские данные
- Временные слоты и статусы

### **Storage Buckets**
- **hall-images** - изображения залов
- **post-images** - изображения для постов и событий

## 🎨 Дизайн и UX

### **Цветовая Схема Altius**
- **Синий** (`altius-blue`) - основной цвет бренда
- **Лайм** (`altius-lime`) - акцентный цвет
- **Оранжевый** (`altius-orange`) - цвет событий

### **Responsive Дизайн**
- **Мобильная оптимизация** для всех устройств
- **Touch-friendly** интерфейс
- **Адаптивные изображения** и галереи

### **Пользовательский Опыт**
- **Быстрая навигация** с плавными переходами
- **Интуитивная админ-панель**
- **Drag & drop** загрузка файлов
- **Полноэкранные галереи** изображений

## 🚀 Установка и Запуск

### **Требования**
- Node.js 18+
- npm или yarn
- Аккаунт Supabase

### **Локальная Разработка**

1. **Клонирование репозитория**
```bash
git clone https://github.com/YR2607/altius-badminton-club.git
cd altius-badminton-club
```

2. **Установка зависимостей**
```bash
npm install
```

3. **Настройка переменных окружения**
```bash
cp .env.example .env.local
# Отредактируйте .env.local с вашими Supabase ключами
```

4. **Запуск в режиме разработки**
```bash
npm run dev
```

5. **Открыть в браузере**
```
http://localhost:3000
```

### **Продакшн Деплой**

Подробные инструкции в файле `DEPLOYMENT.md`

## 🔧 Настройка Supabase

1. **Создайте проект** в Supabase
2. **Выполните SQL схему** из `supabase-schema.sql`
3. **Создайте storage buckets**: `hall-images`, `post-images`
4. **Настройте RLS политики** (включены в схему)

## 📱 Основные Страницы

- **/** - Главная страница
- **/about** - О клубе
- **/services** - Услуги
- **/contact** - Контакты
- **/blog** - Блог и события
- **/halls/[id]** - Страница зала
- **/admin** - Административная панель
- **/admin/halls** - Управление залами
- **/admin/posts** - Управление постами

## 🎯 Особенности Реализации

### **Слайдеры Изображений**
- Поддержка множественных изображений в постах
- Автоматическая замена плейсхолдеров `<div data-slider="gallery"></div>`
- Полноэкранный просмотр с навигацией

### **Система Управления Контентом**
- WYSIWYG редактор с поддержкой HTML
- Автогенерация URL slug с транслитерацией
- Система тегов и категорий

### **Безопасность**
- Row Level Security (RLS) в Supabase
- Валидация данных на клиенте и сервере
- Безопасная загрузка файлов

## 🚀 Функциональность

### **Для Посетителей**
1. **Просмотр информации** о клубе и услугах
2. **Изучение залов** с детальными характеристиками
3. **Чтение новостей** и анонсов событий
4. **Просмотр галерей** изображений
5. **Контактная информация** и способы связи

### **Для Администраторов**
1. **Управление залами** - создание, редактирование, загрузка фото
2. **Управление контентом** - создание постов и событий
3. **Система галерей** - множественные изображения и слайдеры
4. **Аналитика** - просмотры постов и статистика
5. **SEO оптимизация** - мета-теги и описания

## 🎨 Дизайн-Система

### **Компоненты**
- **Header/Footer** - навигация и контактная информация
- **HallCard** - карточки залов с основной информацией
- **MediaGallery** - галерея изображений с полноэкранным просмотром
- **PostImageSlider** - слайдер для множественных изображений
- **ImageUploader** - drag & drop загрузка файлов

### **Цветовая Палитра**
```css
--altius-blue: #2563eb    /* Основной синий */
--altius-lime: #84cc16    /* Акцентный лайм */
--altius-orange: #ea580c  /* Цвет событий */
```

## 📊 Аналитика и Метрики

- **Счетчики просмотров** для постов
- **Статистика залов** и их популярность
- **Отслеживание активности** в админ-панели
- **SEO метрики** для поисковой оптимизации

## 🔮 Планы Развития

### **Ближайшие Обновления**
- **Система бронирования** залов онлайн
- **Интеграция с календарем** Google/Outlook
- **Система уведомлений** email/SMS
- **Онлайн оплата** через банковские карты

### **Долгосрочные Цели**
- **Мобильное приложение** для iOS/Android
- **Система лояльности** для постоянных клиентов
- **Интеграция с соцсетями** для автопостинга
- **Многоязычность** (румынский, английский)

## 👥 Команда Разработки

Проект разработан для бадминтонного клуба "Altius" с использованием современных веб-технологий и лучших практик разработки.

## 📞 Поддержка

Для вопросов по техническим аспектам или предложений по улучшению создайте issue в репозитории GitHub.

## 📄 Лицензия

Этот проект создан специально для бадминтонного клуба "Altius" в Кишиневе.

---

**🏸 Добро пожаловать в Altius - где каждая игра становится победой!**
