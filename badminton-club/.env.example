# Supabase Configuration
# Copy this file to .env.local and replace with your actual Supabase credentials

# Project URL (from Supabase Dashboard → Settings → API)
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co

# Anon public key (from Supabase Dashboard → Settings → API)
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here

# For production deployment (Vercel), add these same variables to:
# Vercel Dashboard → Project Settings → Environment Variables
