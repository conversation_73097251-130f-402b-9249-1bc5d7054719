[{"name": "hot-reloader", "duration": 41, "timestamp": 4837975278335, "id": 3, "tags": {"version": "15.3.5"}, "startTime": 1753263221513, "traceId": "e42126594713b970"}, {"name": "setup-dev-bundler", "duration": 453163, "timestamp": 4837975127310, "id": 2, "parentId": 1, "tags": {}, "startTime": 1753263221362, "traceId": "e42126594713b970"}, {"name": "run-instrumentation-hook", "duration": 25, "timestamp": 4837975608518, "id": 4, "parentId": 1, "tags": {}, "startTime": 1753263221843, "traceId": "e42126594713b970"}, {"name": "start-dev-server", "duration": 925674, "timestamp": 4837974692572, "id": 1, "tags": {"cpus": "8", "platform": "darwin", "memory.freeMem": "80248832", "memory.totalMem": "17179869184", "memory.heapSizeLimit": "8640266240", "memory.rss": "300957696", "memory.heapTotal": "113836032", "memory.heapUsed": "86334960"}, "startTime": 1753263220927, "traceId": "e42126594713b970"}, {"name": "compile-path", "duration": 1724567, "timestamp": 4838025204293, "id": 7, "tags": {"trigger": "/admin/halls"}, "startTime": 1753263271439, "traceId": "e42126594713b970"}, {"name": "ensure-page", "duration": 1725508, "timestamp": 4838025203732, "id": 6, "parentId": 3, "tags": {"inputPage": "/admin/halls/page"}, "startTime": 1753263271439, "traceId": "e42126594713b970"}]