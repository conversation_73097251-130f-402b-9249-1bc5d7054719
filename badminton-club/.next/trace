[{"name": "hot-reloader", "duration": 37, "timestamp": 4838870570450, "id": 3, "tags": {"version": "15.3.5"}, "startTime": 1753264116788, "traceId": "b4fcc16ec985058c"}, {"name": "setup-dev-bundler", "duration": 475069, "timestamp": 4838870421058, "id": 2, "parentId": 1, "tags": {}, "startTime": 1753264116639, "traceId": "b4fcc16ec985058c"}, {"name": "run-instrumentation-hook", "duration": 13, "timestamp": 4838870919849, "id": 4, "parentId": 1, "tags": {}, "startTime": 1753264117137, "traceId": "b4fcc16ec985058c"}, {"name": "start-dev-server", "duration": 936510, "timestamp": 4838869988737, "id": 1, "tags": {"cpus": "8", "platform": "darwin", "memory.freeMem": "183091200", "memory.totalMem": "17179869184", "memory.heapSizeLimit": "8640266240", "memory.rss": "302268416", "memory.heapTotal": "113836032", "memory.heapUsed": "85864592"}, "startTime": 1753264116206, "traceId": "b4fcc16ec985058c"}, {"name": "compile-path", "duration": 1983226, "timestamp": 4838919113483, "id": 7, "tags": {"trigger": "/admin/halls"}, "startTime": 1753264165331, "traceId": "b4fcc16ec985058c"}, {"name": "ensure-page", "duration": 1984137, "timestamp": 4838919113006, "id": 6, "parentId": 3, "tags": {"inputPage": "/admin/halls/page"}, "startTime": 1753264165330, "traceId": "b4fcc16ec985058c"}]