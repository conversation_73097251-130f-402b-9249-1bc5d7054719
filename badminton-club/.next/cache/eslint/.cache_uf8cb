[{"/Users/<USER>/Altius/badminton-club/src/app/about/page.tsx": "1", "/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx": "2", "/Users/<USER>/Altius/badminton-club/src/app/contact/page.tsx": "3", "/Users/<USER>/Altius/badminton-club/src/app/halls/[id]/page.tsx": "4", "/Users/<USER>/Altius/badminton-club/src/app/layout.tsx": "5", "/Users/<USER>/Altius/badminton-club/src/app/page.tsx": "6", "/Users/<USER>/Altius/badminton-club/src/app/services/page.tsx": "7", "/Users/<USER>/Altius/badminton-club/src/components/BookingCalendar.tsx": "8", "/Users/<USER>/Altius/badminton-club/src/components/BookingForm.tsx": "9", "/Users/<USER>/Altius/badminton-club/src/components/Footer.tsx": "10", "/Users/<USER>/Altius/badminton-club/src/components/HallCard.tsx": "11", "/Users/<USER>/Altius/badminton-club/src/components/Header.tsx": "12", "/Users/<USER>/Altius/badminton-club/src/components/MediaGallery.tsx": "13", "/Users/<USER>/Altius/badminton-club/src/lib/supabase.ts": "14", "/Users/<USER>/Altius/badminton-club/src/app/admin/halls/page.tsx": "15", "/Users/<USER>/Altius/badminton-club/src/app/api/halls/[id]/route.ts": "16", "/Users/<USER>/Altius/badminton-club/src/app/api/halls/route.ts": "17", "/Users/<USER>/Altius/badminton-club/src/components/HallEditor.tsx": "18", "/Users/<USER>/Altius/badminton-club/src/components/ImageUploader.tsx": "19"}, {"size": 11132, "mtime": 1752228326324, "results": "20", "hashOfConfig": "21"}, {"size": 16269, "mtime": 1753260841277, "results": "22", "hashOfConfig": "21"}, {"size": 13397, "mtime": 1752228297024, "results": "23", "hashOfConfig": "21"}, {"size": 16847, "mtime": 1753262057925, "results": "24", "hashOfConfig": "21"}, {"size": 926, "mtime": 1752226642359, "results": "25", "hashOfConfig": "21"}, {"size": 14123, "mtime": 1753261002073, "results": "26", "hashOfConfig": "21"}, {"size": 12622, "mtime": 1752229304457, "results": "27", "hashOfConfig": "21"}, {"size": 5355, "mtime": 1752229349224, "results": "28", "hashOfConfig": "21"}, {"size": 9541, "mtime": 1752229400416, "results": "29", "hashOfConfig": "21"}, {"size": 2813, "mtime": 1752228903422, "results": "30", "hashOfConfig": "21"}, {"size": 3768, "mtime": 1752229373701, "results": "31", "hashOfConfig": "21"}, {"size": 5781, "mtime": 1752228857908, "results": "32", "hashOfConfig": "21"}, {"size": 8671, "mtime": 1752227403667, "results": "33", "hashOfConfig": "21"}, {"size": 3170, "mtime": 1752227049928, "results": "34", "hashOfConfig": "21"}, {"size": 14949, "mtime": 1753261244067, "results": "35", "hashOfConfig": "21"}, {"size": 2025, "mtime": 1753261405853, "results": "36", "hashOfConfig": "21"}, {"size": 1174, "mtime": 1753260869487, "results": "37", "hashOfConfig": "21"}, {"size": 12440, "mtime": 1753261278814, "results": "38", "hashOfConfig": "21"}, {"size": 6833, "mtime": 1753261121172, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "a3qxxj", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Altius/badminton-club/src/app/about/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/contact/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/halls/[id]/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/layout.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/services/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/BookingCalendar.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/BookingForm.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/Footer.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/HallCard.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/Header.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/MediaGallery.tsx", ["97", "98", "99"], [], "/Users/<USER>/Altius/badminton-club/src/lib/supabase.ts", [], [], "/Users/<USER>/Altius/badminton-club/src/app/admin/halls/page.tsx", ["100"], [], "/Users/<USER>/Altius/badminton-club/src/app/api/halls/[id]/route.ts", [], [], "/Users/<USER>/Altius/badminton-club/src/app/api/halls/route.ts", [], [], "/Users/<USER>/Altius/badminton-club/src/components/HallEditor.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/ImageUploader.tsx", ["101"], [], {"ruleId": "102", "severity": 1, "message": "103", "line": 73, "column": 15, "nodeType": "104", "endLine": 78, "endColumn": 17}, {"ruleId": "102", "severity": 1, "message": "103", "line": 113, "column": 19, "nodeType": "104", "endLine": 117, "endColumn": 21}, {"ruleId": "102", "severity": 1, "message": "103", "line": 157, "column": 15, "nodeType": "104", "endLine": 161, "endColumn": 17}, {"ruleId": "102", "severity": 1, "message": "103", "line": 376, "column": 25, "nodeType": "104", "endLine": 381, "endColumn": 27}, {"ruleId": "102", "severity": 1, "message": "103", "line": 178, "column": 17, "nodeType": "104", "endLine": 182, "endColumn": 19}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement"]