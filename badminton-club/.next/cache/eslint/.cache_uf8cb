[{"/Users/<USER>/Altius/badminton-club/src/app/about/page.tsx": "1", "/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx": "2", "/Users/<USER>/Altius/badminton-club/src/app/contact/page.tsx": "3", "/Users/<USER>/Altius/badminton-club/src/app/halls/[id]/page.tsx": "4", "/Users/<USER>/Altius/badminton-club/src/app/layout.tsx": "5", "/Users/<USER>/Altius/badminton-club/src/app/page.tsx": "6", "/Users/<USER>/Altius/badminton-club/src/app/services/page.tsx": "7", "/Users/<USER>/Altius/badminton-club/src/components/BookingCalendar.tsx": "8", "/Users/<USER>/Altius/badminton-club/src/components/BookingForm.tsx": "9", "/Users/<USER>/Altius/badminton-club/src/components/Footer.tsx": "10", "/Users/<USER>/Altius/badminton-club/src/components/HallCard.tsx": "11", "/Users/<USER>/Altius/badminton-club/src/components/Header.tsx": "12", "/Users/<USER>/Altius/badminton-club/src/components/MediaGallery.tsx": "13", "/Users/<USER>/Altius/badminton-club/src/lib/supabase.ts": "14"}, {"size": 11132, "mtime": 1752228326324, "results": "15", "hashOfConfig": "16"}, {"size": 14340, "mtime": 1752226967447, "results": "17", "hashOfConfig": "16"}, {"size": 13397, "mtime": 1752228297024, "results": "18", "hashOfConfig": "16"}, {"size": 16725, "mtime": 1752227539761, "results": "19", "hashOfConfig": "16"}, {"size": 926, "mtime": 1752226642359, "results": "20", "hashOfConfig": "16"}, {"size": 12837, "mtime": 1752228795109, "results": "21", "hashOfConfig": "16"}, {"size": 12637, "mtime": 1752228356732, "results": "22", "hashOfConfig": "16"}, {"size": 5315, "mtime": 1752226434050, "results": "23", "hashOfConfig": "16"}, {"size": 9562, "mtime": 1752226473154, "results": "24", "hashOfConfig": "16"}, {"size": 2813, "mtime": 1752228903422, "results": "25", "hashOfConfig": "16"}, {"size": 3777, "mtime": 1752228970462, "results": "26", "hashOfConfig": "16"}, {"size": 5781, "mtime": 1752228857908, "results": "27", "hashOfConfig": "16"}, {"size": 8671, "mtime": 1752227403667, "results": "28", "hashOfConfig": "16"}, {"size": 3170, "mtime": 1752227049928, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "a3qxxj", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Altius/badminton-club/src/app/about/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx", ["72", "73", "74"], [], "/Users/<USER>/Altius/badminton-club/src/app/contact/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/halls/[id]/page.tsx", ["75"], [], "/Users/<USER>/Altius/badminton-club/src/app/layout.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/services/page.tsx", ["76"], [], "/Users/<USER>/Altius/badminton-club/src/components/BookingCalendar.tsx", ["77", "78"], [], "/Users/<USER>/Altius/badminton-club/src/components/BookingForm.tsx", ["79"], [], "/Users/<USER>/Altius/badminton-club/src/components/Footer.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/HallCard.tsx", ["80"], [], "/Users/<USER>/Altius/badminton-club/src/components/Header.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/MediaGallery.tsx", ["81", "82", "83"], [], "/Users/<USER>/Altius/badminton-club/src/lib/supabase.ts", [], [], {"ruleId": "84", "severity": 2, "message": "85", "line": 14, "column": 3, "nodeType": null, "messageId": "86", "endLine": 14, "endColumn": 7}, {"ruleId": "84", "severity": 2, "message": "87", "line": 35, "column": 10, "nodeType": null, "messageId": "86", "endLine": 35, "endColumn": 22}, {"ruleId": "84", "severity": 2, "message": "88", "line": 35, "column": 24, "nodeType": null, "messageId": "86", "endLine": 35, "endColumn": 39}, {"ruleId": "89", "severity": 2, "message": "90", "line": 219, "column": 38, "nodeType": "91", "messageId": "92", "endLine": 219, "endColumn": 41, "suggestions": "93"}, {"ruleId": "84", "severity": 2, "message": "94", "line": 10, "column": 3, "nodeType": null, "messageId": "86", "endLine": 10, "endColumn": 8}, {"ruleId": "84", "severity": 2, "message": "95", "line": 8, "column": 11, "nodeType": null, "messageId": "86", "endLine": 8, "endColumn": 19}, {"ruleId": "84", "severity": 2, "message": "96", "line": 49, "column": 11, "nodeType": null, "messageId": "86", "endLine": 49, "endColumn": 14}, {"ruleId": "84", "severity": 2, "message": "97", "line": 42, "column": 29, "nodeType": null, "messageId": "86", "endLine": 42, "endColumn": 33}, {"ruleId": "84", "severity": 2, "message": "98", "line": 19, "column": 3, "nodeType": null, "messageId": "86", "endLine": 19, "endColumn": 8}, {"ruleId": "99", "severity": 1, "message": "100", "line": 73, "column": 15, "nodeType": "101", "endLine": 78, "endColumn": 17}, {"ruleId": "99", "severity": 1, "message": "100", "line": 113, "column": 19, "nodeType": "101", "endLine": 117, "endColumn": 21}, {"ruleId": "99", "severity": 1, "message": "100", "line": 157, "column": 15, "nodeType": "101", "endLine": 161, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'Edit' is defined but never used.", "unusedVar", "'selectedDate' is assigned a value but never used.", "'setSelectedDate' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["102", "103"], "'Clock' is defined but never used.", "'TimeSlot' is defined but never used.", "'key' is assigned a value but never used.", "'data' is defined but never used.", "'image' is defined but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", {"messageId": "104", "fix": "105", "desc": "106"}, {"messageId": "107", "fix": "108", "desc": "109"}, "suggestUnknown", {"range": "110", "text": "111"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "112", "text": "113"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", [6530, 6533], "unknown", [6530, 6533], "never"]