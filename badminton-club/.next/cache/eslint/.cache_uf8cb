[{"/Users/<USER>/Altius/badminton-club/src/app/about/page.tsx": "1", "/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx": "2", "/Users/<USER>/Altius/badminton-club/src/app/contact/page.tsx": "3", "/Users/<USER>/Altius/badminton-club/src/app/halls/[id]/page.tsx": "4", "/Users/<USER>/Altius/badminton-club/src/app/layout.tsx": "5", "/Users/<USER>/Altius/badminton-club/src/app/page.tsx": "6", "/Users/<USER>/Altius/badminton-club/src/app/services/page.tsx": "7", "/Users/<USER>/Altius/badminton-club/src/components/BookingCalendar.tsx": "8", "/Users/<USER>/Altius/badminton-club/src/components/BookingForm.tsx": "9", "/Users/<USER>/Altius/badminton-club/src/components/Footer.tsx": "10", "/Users/<USER>/Altius/badminton-club/src/components/HallCard.tsx": "11", "/Users/<USER>/Altius/badminton-club/src/components/Header.tsx": "12", "/Users/<USER>/Altius/badminton-club/src/components/MediaGallery.tsx": "13", "/Users/<USER>/Altius/badminton-club/src/lib/supabase.ts": "14", "/Users/<USER>/Altius/badminton-club/src/app/admin/halls/page.tsx": "15", "/Users/<USER>/Altius/badminton-club/src/app/api/halls/[id]/route.ts": "16", "/Users/<USER>/Altius/badminton-club/src/app/api/halls/route.ts": "17", "/Users/<USER>/Altius/badminton-club/src/components/HallEditor.tsx": "18", "/Users/<USER>/Altius/badminton-club/src/components/ImageUploader.tsx": "19", "/Users/<USER>/Altius/badminton-club/src/app/admin/halls/[id]/page.tsx": "20"}, {"size": 11132, "mtime": 1752228326324, "results": "21", "hashOfConfig": "22"}, {"size": 16269, "mtime": 1753260841277, "results": "23", "hashOfConfig": "22"}, {"size": 13397, "mtime": 1752228297024, "results": "24", "hashOfConfig": "22"}, {"size": 16847, "mtime": 1753262057925, "results": "25", "hashOfConfig": "22"}, {"size": 926, "mtime": 1752226642359, "results": "26", "hashOfConfig": "22"}, {"size": 14123, "mtime": 1753261002073, "results": "27", "hashOfConfig": "22"}, {"size": 12622, "mtime": 1752229304457, "results": "28", "hashOfConfig": "22"}, {"size": 5355, "mtime": 1752229349224, "results": "29", "hashOfConfig": "22"}, {"size": 9541, "mtime": 1752229400416, "results": "30", "hashOfConfig": "22"}, {"size": 2813, "mtime": 1752228903422, "results": "31", "hashOfConfig": "22"}, {"size": 3768, "mtime": 1752229373701, "results": "32", "hashOfConfig": "22"}, {"size": 5781, "mtime": 1752228857908, "results": "33", "hashOfConfig": "22"}, {"size": 8671, "mtime": 1752227403667, "results": "34", "hashOfConfig": "22"}, {"size": 3170, "mtime": 1752227049928, "results": "35", "hashOfConfig": "22"}, {"size": 9247, "mtime": 1753262838565, "results": "36", "hashOfConfig": "22"}, {"size": 2025, "mtime": 1753261405853, "results": "37", "hashOfConfig": "22"}, {"size": 1174, "mtime": 1753260869487, "results": "38", "hashOfConfig": "22"}, {"size": 12440, "mtime": 1753261278814, "results": "39", "hashOfConfig": "22"}, {"size": 6833, "mtime": 1753261121172, "results": "40", "hashOfConfig": "22"}, {"size": 25678, "mtime": 1753262949467, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "a3qxxj", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Altius/badminton-club/src/app/about/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/contact/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/halls/[id]/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/layout.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/services/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/BookingCalendar.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/BookingForm.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/Footer.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/HallCard.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/Header.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/MediaGallery.tsx", ["102", "103", "104"], [], "/Users/<USER>/Altius/badminton-club/src/lib/supabase.ts", [], [], "/Users/<USER>/Altius/badminton-club/src/app/admin/halls/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/api/halls/[id]/route.ts", [], [], "/Users/<USER>/Altius/badminton-club/src/app/api/halls/route.ts", [], [], "/Users/<USER>/Altius/badminton-club/src/components/HallEditor.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/ImageUploader.tsx", ["105"], [], "/Users/<USER>/Altius/badminton-club/src/app/admin/halls/[id]/page.tsx", ["106"], [], {"ruleId": "107", "severity": 1, "message": "108", "line": 73, "column": 15, "nodeType": "109", "endLine": 78, "endColumn": 17}, {"ruleId": "107", "severity": 1, "message": "108", "line": 113, "column": 19, "nodeType": "109", "endLine": 117, "endColumn": 21}, {"ruleId": "107", "severity": 1, "message": "108", "line": 157, "column": 15, "nodeType": "109", "endLine": 161, "endColumn": 17}, {"ruleId": "107", "severity": 1, "message": "108", "line": 178, "column": 17, "nodeType": "109", "endLine": 182, "endColumn": 19}, {"ruleId": "110", "severity": 1, "message": "111", "line": 52, "column": 6, "nodeType": "112", "endLine": 52, "endColumn": 14, "suggestions": "113"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchHall'. Either include it or remove the dependency array.", "ArrayExpression", ["114"], {"desc": "115", "fix": "116"}, "Update the dependencies array to be: [fetchHall, hallId]", {"range": "117", "text": "118"}, [1329, 1337], "[fetch<PERSON><PERSON>, hallId]"]