[{"/Users/<USER>/Altius/badminton-club/src/app/about/page.tsx": "1", "/Users/<USER>/Altius/badminton-club/src/app/admin/halls/[id]/page.tsx": "2", "/Users/<USER>/Altius/badminton-club/src/app/admin/halls/page.tsx": "3", "/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx": "4", "/Users/<USER>/Altius/badminton-club/src/app/admin/posts/[id]/page.tsx": "5", "/Users/<USER>/Altius/badminton-club/src/app/admin/posts/new/page.tsx": "6", "/Users/<USER>/Altius/badminton-club/src/app/admin/posts/page.tsx": "7", "/Users/<USER>/Altius/badminton-club/src/app/api/halls/[id]/route.ts": "8", "/Users/<USER>/Altius/badminton-club/src/app/api/halls/route.ts": "9", "/Users/<USER>/Altius/badminton-club/src/app/blog/[slug]/page.tsx": "10", "/Users/<USER>/Altius/badminton-club/src/app/blog/page.tsx": "11", "/Users/<USER>/Altius/badminton-club/src/app/contact/page.tsx": "12", "/Users/<USER>/Altius/badminton-club/src/app/halls/[id]/page.tsx": "13", "/Users/<USER>/Altius/badminton-club/src/app/layout.tsx": "14", "/Users/<USER>/Altius/badminton-club/src/app/page.tsx": "15", "/Users/<USER>/Altius/badminton-club/src/app/services/page.tsx": "16", "/Users/<USER>/Altius/badminton-club/src/components/BookingCalendar.tsx": "17", "/Users/<USER>/Altius/badminton-club/src/components/BookingForm.tsx": "18", "/Users/<USER>/Altius/badminton-club/src/components/Footer.tsx": "19", "/Users/<USER>/Altius/badminton-club/src/components/HallCard.tsx": "20", "/Users/<USER>/Altius/badminton-club/src/components/HallEditor.tsx": "21", "/Users/<USER>/Altius/badminton-club/src/components/Header.tsx": "22", "/Users/<USER>/Altius/badminton-club/src/components/ImageUploader.tsx": "23", "/Users/<USER>/Altius/badminton-club/src/components/MediaGallery.tsx": "24", "/Users/<USER>/Altius/badminton-club/src/components/PostGalleryManager.tsx": "25", "/Users/<USER>/Altius/badminton-club/src/components/PostImageSlider.tsx": "26", "/Users/<USER>/Altius/badminton-club/src/components/PostImageUploader.tsx": "27", "/Users/<USER>/Altius/badminton-club/src/lib/supabase.ts": "28", "/Users/<USER>/Altius/badminton-club/src/types/index.ts": "29"}, {"size": 11132, "mtime": 1752228326324, "results": "30", "hashOfConfig": "31"}, {"size": 24001, "mtime": 1753436378543, "results": "32", "hashOfConfig": "31"}, {"size": 10539, "mtime": 1753435349883, "results": "33", "hashOfConfig": "31"}, {"size": 17975, "mtime": 1753615721760, "results": "34", "hashOfConfig": "31"}, {"size": 18969, "mtime": 1753693335204, "results": "35", "hashOfConfig": "31"}, {"size": 14084, "mtime": 1753692700844, "results": "36", "hashOfConfig": "31"}, {"size": 13216, "mtime": 1753693264424, "results": "37", "hashOfConfig": "31"}, {"size": 2025, "mtime": 1753261405853, "results": "38", "hashOfConfig": "31"}, {"size": 1174, "mtime": 1753260869487, "results": "39", "hashOfConfig": "31"}, {"size": 13250, "mtime": 1753693480528, "results": "40", "hashOfConfig": "31"}, {"size": 9646, "mtime": 1753615498838, "results": "41", "hashOfConfig": "31"}, {"size": 13397, "mtime": 1752228297024, "results": "42", "hashOfConfig": "31"}, {"size": 11874, "mtime": 1753436074600, "results": "43", "hashOfConfig": "31"}, {"size": 926, "mtime": 1752226642359, "results": "44", "hashOfConfig": "31"}, {"size": 13047, "mtime": 1753435840855, "results": "45", "hashOfConfig": "31"}, {"size": 12622, "mtime": 1752229304457, "results": "46", "hashOfConfig": "31"}, {"size": 5355, "mtime": 1752229349224, "results": "47", "hashOfConfig": "31"}, {"size": 9541, "mtime": 1752229400416, "results": "48", "hashOfConfig": "31"}, {"size": 2813, "mtime": 1752228903422, "results": "49", "hashOfConfig": "31"}, {"size": 3768, "mtime": 1752229373701, "results": "50", "hashOfConfig": "31"}, {"size": 12440, "mtime": 1753261278814, "results": "51", "hashOfConfig": "31"}, {"size": 6460, "mtime": 1753615577258, "results": "52", "hashOfConfig": "31"}, {"size": 6833, "mtime": 1753261121172, "results": "53", "hashOfConfig": "31"}, {"size": 8671, "mtime": 1752227403667, "results": "54", "hashOfConfig": "31"}, {"size": 9260, "mtime": 1753692644093, "results": "55", "hashOfConfig": "31"}, {"size": 7295, "mtime": 1753692568842, "results": "56", "hashOfConfig": "31"}, {"size": 5402, "mtime": 1753691929180, "results": "57", "hashOfConfig": "31"}, {"size": 3904, "mtime": 1753347544386, "results": "58", "hashOfConfig": "31"}, {"size": 1604, "mtime": 1753692600177, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "11lzlvd", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Altius/badminton-club/src/app/about/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/admin/halls/[id]/page.tsx", [], ["147"], "/Users/<USER>/Altius/badminton-club/src/app/admin/halls/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/admin/posts/[id]/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/admin/posts/new/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/admin/posts/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/api/halls/[id]/route.ts", [], [], "/Users/<USER>/Altius/badminton-club/src/app/api/halls/route.ts", [], [], "/Users/<USER>/Altius/badminton-club/src/app/blog/[slug]/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/blog/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/contact/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/halls/[id]/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/layout.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/services/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/BookingCalendar.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/BookingForm.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/Footer.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/HallCard.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/HallEditor.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/Header.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/ImageUploader.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/MediaGallery.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/PostGalleryManager.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/PostImageSlider.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/PostImageUploader.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/lib/supabase.ts", [], [], "/Users/<USER>/Altius/badminton-club/src/types/index.ts", [], [], {"ruleId": "148", "severity": 1, "message": "149", "line": 52, "column": 6, "nodeType": "150", "endLine": 52, "endColumn": 14, "suggestions": "151", "suppressions": "152"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchHall'. Either include it or remove the dependency array.", "ArrayExpression", ["153"], ["154"], {"desc": "155", "fix": "156"}, {"kind": "157", "justification": "158"}, "Update the dependencies array to be: [fetchHall, hallId]", {"range": "159", "text": "160"}, "directive", "", [1329, 1337], "[fetch<PERSON><PERSON>, hallId]"]