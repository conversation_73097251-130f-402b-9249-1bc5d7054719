[{"/Users/<USER>/Altius/badminton-club/src/app/about/page.tsx": "1", "/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx": "2", "/Users/<USER>/Altius/badminton-club/src/app/contact/page.tsx": "3", "/Users/<USER>/Altius/badminton-club/src/app/halls/[id]/page.tsx": "4", "/Users/<USER>/Altius/badminton-club/src/app/layout.tsx": "5", "/Users/<USER>/Altius/badminton-club/src/app/page.tsx": "6", "/Users/<USER>/Altius/badminton-club/src/app/services/page.tsx": "7", "/Users/<USER>/Altius/badminton-club/src/components/BookingCalendar.tsx": "8", "/Users/<USER>/Altius/badminton-club/src/components/BookingForm.tsx": "9", "/Users/<USER>/Altius/badminton-club/src/components/Footer.tsx": "10", "/Users/<USER>/Altius/badminton-club/src/components/HallCard.tsx": "11", "/Users/<USER>/Altius/badminton-club/src/components/Header.tsx": "12", "/Users/<USER>/Altius/badminton-club/src/components/MediaGallery.tsx": "13", "/Users/<USER>/Altius/badminton-club/src/lib/supabase.ts": "14"}, {"size": 11132, "mtime": 1752228326324, "results": "15", "hashOfConfig": "16"}, {"size": 14264, "mtime": 1752229242948, "results": "17", "hashOfConfig": "16"}, {"size": 13397, "mtime": 1752228297024, "results": "18", "hashOfConfig": "16"}, {"size": 16826, "mtime": 1752229285044, "results": "19", "hashOfConfig": "16"}, {"size": 926, "mtime": 1752226642359, "results": "20", "hashOfConfig": "16"}, {"size": 12837, "mtime": 1752228795109, "results": "21", "hashOfConfig": "16"}, {"size": 12622, "mtime": 1752229304457, "results": "22", "hashOfConfig": "16"}, {"size": 5355, "mtime": 1752229349224, "results": "23", "hashOfConfig": "16"}, {"size": 9541, "mtime": 1752229400416, "results": "24", "hashOfConfig": "16"}, {"size": 2813, "mtime": 1752228903422, "results": "25", "hashOfConfig": "16"}, {"size": 3768, "mtime": 1752229373701, "results": "26", "hashOfConfig": "16"}, {"size": 5781, "mtime": 1752228857908, "results": "27", "hashOfConfig": "16"}, {"size": 8671, "mtime": 1752227403667, "results": "28", "hashOfConfig": "16"}, {"size": 3170, "mtime": 1752227049928, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "a3qxxj", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Altius/badminton-club/src/app/about/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/contact/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/halls/[id]/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/layout.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/app/services/page.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/BookingCalendar.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/BookingForm.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/Footer.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/HallCard.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/Header.tsx", [], [], "/Users/<USER>/Altius/badminton-club/src/components/MediaGallery.tsx", ["72", "73", "74"], [], "/Users/<USER>/Altius/badminton-club/src/lib/supabase.ts", [], [], {"ruleId": "75", "severity": 1, "message": "76", "line": 73, "column": 15, "nodeType": "77", "endLine": 78, "endColumn": 17}, {"ruleId": "75", "severity": 1, "message": "76", "line": 113, "column": 19, "nodeType": "77", "endLine": 117, "endColumn": 21}, {"ruleId": "75", "severity": 1, "message": "76", "line": 157, "column": 15, "nodeType": "77", "endLine": 161, "endColumn": 17}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement"]