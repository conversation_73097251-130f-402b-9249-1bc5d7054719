{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Menu, X, Phone, MapPin } from 'lucide-react';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl font-bold text-blue-600\">\n              🏸 BadmintonClub\n            </div>\n            <div className=\"ml-2 text-sm text-gray-600 hidden sm:block\">\n              Кишинев\n            </div>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <a href=\"#home\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Главная\n            </a>\n            <a href=\"#halls\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Залы\n            </a>\n            <a href=\"#booking\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Бронирование\n            </a>\n            <a href=\"#contact\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Контакты\n            </a>\n            <a href=\"/admin\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Админ\n            </a>\n          </nav>\n\n          {/* Contact Info */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Phone className=\"w-4 h-4 mr-1\" />\n              +373 XX XXX XXX\n            </div>\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <MapPin className=\"w-4 h-4 mr-1\" />\n              Кишинев\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {isMenuOpen ? (\n              <X className=\"w-6 h-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"w-6 h-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <a href=\"#home\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Главная\n              </a>\n              <a href=\"#halls\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Залы\n              </a>\n              <a href=\"#booking\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Бронирование\n              </a>\n              <a href=\"#contact\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Контакты\n              </a>\n              <a href=\"/admin\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Админ\n              </a>\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center text-sm text-gray-600 mb-2\">\n                  <Phone className=\"w-4 h-4 mr-1\" />\n                  +373 XX XXX XXX\n                </div>\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <MapPin className=\"w-4 h-4 mr-1\" />\n                  Кишинев\n                </div>\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAmC;;;;;;8CAGlD,6LAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAM9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,MAAK;oCAAQ,WAAU;8CAAsD;;;;;;8CAGhF,6LAAC;oCAAE,MAAK;oCAAS,WAAU;8CAAsD;;;;;;8CAGjF,6LAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAsD;;;;;;8CAGnF,6LAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAsD;;;;;;8CAGnF,6LAAC;oCAAE,MAAK;oCAAS,WAAU;8CAAsD;;;;;;;;;;;;sCAMnF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE7B,2BACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,MAAK;gCAAQ,WAAU;0CAAsD;;;;;;0CAGhF,6LAAC;gCAAE,MAAK;gCAAS,WAAU;0CAAsD;;;;;;0CAGjF,6LAAC;gCAAE,MAAK;gCAAW,WAAU;0CAAsD;;;;;;0CAGnF,6LAAC;gCAAE,MAAK;gCAAW,WAAU;0CAAsD;;;;;;0CAGnF,6LAAC;gCAAE,MAAK;gCAAS,WAAU;0CAAsD;;;;;;0CAGjF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GAhGwB;KAAA", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Footer.tsx"], "sourcesContent": ["import { Phone, MapPin, Clock, Mail } from 'lucide-react';\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {/* Company Info */}\n          <div>\n            <div className=\"text-2xl font-bold text-blue-400 mb-4\">\n              🏸 BadmintonClub\n            </div>\n            <p className=\"text-gray-300 mb-4\">\n              Современный бадминтонный клуб в Кишиневе с профессиональными кортами \n              и удобной системой бронирования.\n            </p>\n            <div className=\"flex items-center text-gray-300 mb-2\">\n              <MapPin className=\"w-4 h-4 mr-2\" />\n              Кишинев, Молдова\n            </div>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Контакты</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center text-gray-300\">\n                <Phone className=\"w-4 h-4 mr-2\" />\n                +373 XX XXX XXX\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <Mail className=\"w-4 h-4 mr-2\" />\n                <EMAIL>\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <MapPin className=\"w-4 h-4 mr-2\" />\n                ул. Примерная, 123, Кишинев\n              </div>\n            </div>\n          </div>\n\n          {/* Working Hours */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Режим работы</h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-gray-300\">\n                <Clock className=\"w-4 h-4 mr-2\" />\n                <div>\n                  <div>Пн-Пт: 06:00 - 23:00</div>\n                  <div>Сб-Вс: 08:00 - 22:00</div>\n                </div>\n              </div>\n            </div>\n            \n            {/* Halls Info */}\n            <div className=\"mt-6\">\n              <h4 className=\"font-semibold mb-2\">Наши залы:</h4>\n              <div className=\"text-sm text-gray-300 space-y-1\">\n                <div>Зал 1: 3 корта</div>\n                <div>Зал 2: 7 кортов</div>\n                <div>Зал 3: 7 кортов</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n          <p>&copy; 2024 BadmintonClub Кишинев. Все права защищены.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAwC;;;;;;8CAGvD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOzC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;;kEACC,6LAAC;kEAAI;;;;;;kEACL,6LAAC;kEAAI;;;;;;;;;;;;;;;;;;;;;;;8CAMX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb;KAtEwB", "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/HallCard.tsx"], "sourcesContent": ["import { Users, MapPin, Calendar, Eye } from 'lucide-react';\nimport Link from 'next/link';\n\ninterface HallCardProps {\n  id: number;\n  name: string;\n  courts: number;\n  image: string;\n  description: string;\n  features: string[];\n  pricePerHour: number;\n  onBookClick: (hallId: number) => void;\n}\n\nexport default function HallCard({\n  id,\n  name,\n  courts,\n  image,\n  description,\n  features,\n  pricePerHour,\n  onBookClick\n}: HallCardProps) {\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\">\n      {/* Image */}\n      <div className=\"relative h-64 bg-gradient-to-br from-blue-500 to-blue-700\">\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"text-center text-white\">\n            <div className=\"text-6xl mb-2\">🏸</div>\n            <div className=\"text-xl font-semibold\">{name}</div>\n          </div>\n        </div>\n        <div className=\"absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1\">\n          <div className=\"flex items-center text-sm font-semibold text-gray-700\">\n            <Users className=\"w-4 h-4 mr-1\" />\n            {courts} кортов\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-6\">\n        <h3 className=\"text-xl font-bold text-gray-900 mb-2\">{name}</h3>\n        <p className=\"text-gray-600 mb-4\">{description}</p>\n\n        {/* Features */}\n        <div className=\"mb-4\">\n          <h4 className=\"text-sm font-semibold text-gray-700 mb-2\">Особенности:</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            {features.map((feature, index) => (\n              <li key={index} className=\"flex items-center\">\n                <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full mr-2\"></div>\n                {feature}\n              </li>\n            ))}\n          </ul>\n        </div>\n\n        {/* Price and Location */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"text-lg font-bold text-blue-600\">\n            {pricePerHour} лей/час\n          </div>\n          <div className=\"flex items-center text-sm text-gray-500\">\n            <MapPin className=\"w-4 h-4 mr-1\" />\n            Кишинев\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"space-y-2\">\n          <Link\n            href={`/halls/${id}`}\n            className=\"w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center\"\n          >\n            <Eye className=\"w-4 h-4 mr-2\" />\n            Подробнее\n          </Link>\n          <button\n            onClick={() => onBookClick(id)}\n            className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center\"\n          >\n            <Calendar className=\"w-4 h-4 mr-2\" />\n            Забронировать\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AACA;;;;AAae,SAAS,SAAS,EAC/B,EAAE,EACF,IAAI,EACJ,MAAM,EACN,KAAK,EACL,WAAW,EACX,QAAQ,EACR,YAAY,EACZ,WAAW,EACG;IACd,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAI,WAAU;8CAAyB;;;;;;;;;;;;;;;;;kCAG5C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAChB;gCAAO;;;;;;;;;;;;;;;;;;0BAMd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;kCAGnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAG,WAAU;0CACX,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAI,WAAU;;;;;;4CACd;;uCAFM;;;;;;;;;;;;;;;;kCASf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ;oCAAa;;;;;;;0CAEhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,OAAO,EAAE,IAAI;gCACpB,WAAU;;kDAEV,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGlC,6LAAC;gCACC,SAAS,IAAM,YAAY;gCAC3B,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;KA7EwB", "debugId": null}}, {"offset": {"line": 847, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/BookingCalendar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { format, addDays, startOfWeek, isSameDay, isToday } from 'date-fns';\nimport { ru } from 'date-fns/locale';\nimport { ChevronLeft, ChevronRight, Clock } from 'lucide-react';\n\ninterface TimeSlot {\n  time: string;\n  available: boolean;\n  court?: number;\n}\n\ninterface BookingCalendarProps {\n  hallId: number;\n  onSlotSelect: (date: Date, time: string, court: number) => void;\n}\n\nexport default function BookingCalendar({ hallId, onSlotSelect }: BookingCalendarProps) {\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [currentWeek, setCurrentWeek] = useState(startOfWeek(new Date(), { weekStartsOn: 1 }));\n\n  // Генерируем временные слоты с 6:00 до 23:00\n  const timeSlots: string[] = [];\n  for (let hour = 6; hour <= 22; hour++) {\n    timeSlots.push(`${hour.toString().padStart(2, '0')}:00`);\n    timeSlots.push(`${hour.toString().padStart(2, '0')}:30`);\n  }\n\n  // Получаем количество кортов для зала\n  const getCourtCount = (hallId: number) => {\n    switch (hallId) {\n      case 1: return 3;\n      case 2: return 7;\n      case 3: return 7;\n      default: return 3;\n    }\n  };\n\n  const courtCount = getCourtCount(hallId);\n\n  // Генерируем дни недели\n  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(currentWeek, i));\n\n  // Симуляция занятых слотов (в реальном приложении это будет из API)\n  const isSlotBooked = (date: Date, time: string, court: number) => {\n    // Случайная логика для демонстрации\n    const dateStr = format(date, 'yyyy-MM-dd');\n    const key = `${dateStr}-${time}-${court}`;\n    return Math.random() > 0.7; // 30% слотов заняты\n  };\n\n  const goToPreviousWeek = () => {\n    setCurrentWeek(addDays(currentWeek, -7));\n  };\n\n  const goToNextWeek = () => {\n    setCurrentWeek(addDays(currentWeek, 7));\n  };\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg p-6\">\n      <div className=\"mb-6\">\n        <h3 className=\"text-xl font-bold text-gray-900 mb-2\">\n          Выберите дату и время\n        </h3>\n        <p className=\"text-gray-600\">\n          Зал {hallId} • {courtCount} кортов\n        </p>\n      </div>\n\n      {/* Week Navigation */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <button\n          onClick={goToPreviousWeek}\n          className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n        >\n          <ChevronLeft className=\"w-5 h-5\" />\n        </button>\n        \n        <div className=\"text-lg font-semibold\">\n          {format(currentWeek, 'MMMM yyyy', { locale: ru })}\n        </div>\n        \n        <button\n          onClick={goToNextWeek}\n          className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n        >\n          <ChevronRight className=\"w-5 h-5\" />\n        </button>\n      </div>\n\n      {/* Days of Week */}\n      <div className=\"grid grid-cols-7 gap-2 mb-4\">\n        {weekDays.map((day, index) => (\n          <button\n            key={index}\n            onClick={() => setSelectedDate(day)}\n            className={`p-3 text-center rounded-lg transition-colors ${\n              isSameDay(day, selectedDate)\n                ? 'bg-blue-600 text-white'\n                : isToday(day)\n                ? 'bg-blue-100 text-blue-600'\n                : 'hover:bg-gray-100'\n            }`}\n          >\n            <div className=\"text-xs text-gray-500 mb-1\">\n              {format(day, 'EEE', { locale: ru })}\n            </div>\n            <div className=\"font-semibold\">\n              {format(day, 'd')}\n            </div>\n          </button>\n        ))}\n      </div>\n\n      {/* Time Slots */}\n      <div className=\"max-h-96 overflow-y-auto\">\n        <div className=\"space-y-2\">\n          {timeSlots.map((time) => (\n            <div key={time} className=\"border rounded-lg p-3\">\n              <div className=\"flex items-center mb-2\">\n                <Clock className=\"w-4 h-4 mr-2 text-gray-500\" />\n                <span className=\"font-medium\">{time}</span>\n              </div>\n              \n              <div className=\"grid grid-cols-3 gap-2\">\n                {Array.from({ length: courtCount }, (_, courtIndex) => {\n                  const court = courtIndex + 1;\n                  const isBooked = isSlotBooked(selectedDate, time, court);\n                  \n                  return (\n                    <button\n                      key={court}\n                      onClick={() => !isBooked && onSlotSelect(selectedDate, time, court)}\n                      disabled={isBooked}\n                      className={`p-2 text-sm rounded transition-colors ${\n                        isBooked\n                          ? 'bg-red-100 text-red-600 cursor-not-allowed'\n                          : 'bg-green-100 text-green-600 hover:bg-green-200'\n                      }`}\n                    >\n                      Корт {court}\n                      {isBooked && (\n                        <div className=\"text-xs\">Занят</div>\n                      )}\n                    </button>\n                  );\n                })}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;AALA;;;;;AAkBe,SAAS,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAwB;;IACpF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,cAAW,AAAD,EAAE,IAAI,QAAQ;QAAE,cAAc;IAAE;IAEzF,6CAA6C;IAC7C,MAAM,YAAsB,EAAE;IAC9B,IAAK,IAAI,OAAO,GAAG,QAAQ,IAAI,OAAQ;QACrC,UAAU,IAAI,CAAC,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;QACvD,UAAU,IAAI,CAAC,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;IACzD;IAEA,sCAAsC;IACtC,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,cAAc;IAEjC,wBAAwB;IACxB,MAAM,WAAW,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAE,GAAG,CAAC,GAAG,IAAM,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAE1E,oEAAoE;IACpE,MAAM,eAAe,CAAC,MAAY,MAAc;QAC9C,oCAAoC;QACpC,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QAC7B,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,KAAK,CAAC,EAAE,OAAO;QACzC,OAAO,KAAK,MAAM,KAAK,KAAK,oBAAoB;IAClD;IAEA,MAAM,mBAAmB;QACvB,eAAe,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,aAAa,CAAC;IACvC;IAEA,MAAM,eAAe;QACnB,eAAe,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IACtC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuC;;;;;;kCAGrD,6LAAC;wBAAE,WAAU;;4BAAgB;4BACtB;4BAAO;4BAAI;4BAAW;;;;;;;;;;;;;0BAK/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAGzB,6LAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,aAAa,aAAa;4BAAE,QAAQ,8IAAA,CAAA,KAAE;wBAAC;;;;;;kCAGjD,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAK5B,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,6LAAC;wBAEC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,6CAA6C,EACvD,CAAA,GAAA,2IAAA,CAAA,YAAS,AAAD,EAAE,KAAK,gBACX,2BACA,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OACR,8BACA,qBACJ;;0CAEF,6LAAC;gCAAI,WAAU;0CACZ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,OAAO;oCAAE,QAAQ,8IAAA,CAAA,KAAE;gCAAC;;;;;;0CAEnC,6LAAC;gCAAI,WAAU;0CACZ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,KAAK;;;;;;;uBAdV;;;;;;;;;;0BAqBX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;4BAAe,WAAU;;8CACxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAGjC,6LAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAW,GAAG,CAAC,GAAG;wCACtC,MAAM,QAAQ,aAAa;wCAC3B,MAAM,WAAW,aAAa,cAAc,MAAM;wCAElD,qBACE,6LAAC;4CAEC,SAAS,IAAM,CAAC,YAAY,aAAa,cAAc,MAAM;4CAC7D,UAAU;4CACV,WAAW,CAAC,sCAAsC,EAChD,WACI,+CACA,kDACJ;;gDACH;gDACO;gDACL,0BACC,6LAAC;oDAAI,WAAU;8DAAU;;;;;;;2CAXtB;;;;;oCAeX;;;;;;;2BA5BM;;;;;;;;;;;;;;;;;;;;;AAoCtB;GA1IwB;KAAA", "debugId": null}}, {"offset": {"line": 1130, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/BookingForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { format } from 'date-fns';\nimport { ru } from 'date-fns/locale';\nimport { User, Phone, Calendar, Clock, MapPin, CreditCard, QrCode } from 'lucide-react';\n\nconst bookingSchema = z.object({\n  name: z.string().min(2, 'Имя должно содержать минимум 2 символа'),\n  phone: z.string().min(8, 'Введите корректный номер телефона'),\n  email: z.string().email('Введите корректный email').optional().or(z.literal('')),\n});\n\ntype BookingFormData = z.infer<typeof bookingSchema>;\n\ninterface BookingFormProps {\n  hallId: number;\n  date: Date;\n  time: string;\n  court: number;\n  onSubmit: (data: BookingFormData & { hallId: number; date: Date; time: string; court: number }) => void;\n  onCancel: () => void;\n}\n\nexport default function BookingForm({ hallId, date, time, court, onSubmit, onCancel }: BookingFormProps) {\n  const [showPayment, setShowPayment] = useState(false);\n  const [paymentMethod, setPaymentMethod] = useState<'qr' | 'transfer'>('qr');\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting }\n  } = useForm<BookingFormData>({\n    resolver: zodResolver(bookingSchema)\n  });\n\n  const pricePerHour = 150; // лей за час\n\n  const handleFormSubmit = (data: BookingFormData) => {\n    setShowPayment(true);\n  };\n\n  const handlePaymentConfirm = () => {\n    const formData = new FormData(document.querySelector('form') as HTMLFormElement);\n    const data = {\n      name: formData.get('name') as string,\n      phone: formData.get('phone') as string,\n      email: formData.get('email') as string,\n      hallId,\n      date,\n      time,\n      court\n    };\n    onSubmit(data);\n  };\n\n  if (showPayment) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-lg p-6\">\n        <h3 className=\"text-xl font-bold text-gray-900 mb-6\">Оплата бронирования</h3>\n        \n        {/* Booking Summary */}\n        <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n          <h4 className=\"font-semibold mb-2\">Детали бронирования:</h4>\n          <div className=\"space-y-1 text-sm text-gray-600\">\n            <div>Зал {hallId}, Корт {court}</div>\n            <div>{format(date, 'dd MMMM yyyy', { locale: ru })}</div>\n            <div>{time}</div>\n            <div className=\"font-semibold text-lg text-blue-600 mt-2\">\n              Стоимость: {pricePerHour} лей\n            </div>\n          </div>\n        </div>\n\n        {/* Payment Method Selection */}\n        <div className=\"mb-6\">\n          <h4 className=\"font-semibold mb-3\">Способ оплаты:</h4>\n          <div className=\"grid grid-cols-2 gap-3\">\n            <button\n              onClick={() => setPaymentMethod('qr')}\n              className={`p-3 border rounded-lg flex items-center justify-center ${\n                paymentMethod === 'qr' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'\n              }`}\n            >\n              <QrCode className=\"w-5 h-5 mr-2\" />\n              QR-код\n            </button>\n            <button\n              onClick={() => setPaymentMethod('transfer')}\n              className={`p-3 border rounded-lg flex items-center justify-center ${\n                paymentMethod === 'transfer' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'\n              }`}\n            >\n              <CreditCard className=\"w-5 h-5 mr-2\" />\n              Перевод\n            </button>\n          </div>\n        </div>\n\n        {/* Payment Details */}\n        {paymentMethod === 'qr' && (\n          <div className=\"text-center mb-6\">\n            <div className=\"w-48 h-48 bg-gray-200 rounded-lg mx-auto mb-4 flex items-center justify-center\">\n              <QrCode className=\"w-16 h-16 text-gray-400\" />\n            </div>\n            <p className=\"text-sm text-gray-600\">\n              Отсканируйте QR-код для оплаты через банковское приложение\n            </p>\n          </div>\n        )}\n\n        {paymentMethod === 'transfer' && (\n          <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n            <h4 className=\"font-semibold mb-3\">Реквизиты для перевода:</h4>\n            <div className=\"space-y-2 text-sm\">\n              <div><strong>Получатель:</strong> BadmintonClub SRL</div>\n              <div><strong>Банк:</strong> Moldova Agroindbank</div>\n              <div><strong>IBAN:</strong> ************************</div>\n              <div><strong>Сумма:</strong> {pricePerHour} лей</div>\n              <div><strong>Назначение:</strong> Бронирование зал {hallId}, корт {court}, {format(date, 'dd.MM.yyyy')} {time}</div>\n            </div>\n          </div>\n        )}\n\n        {/* Action Buttons */}\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={onCancel}\n            className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            Отмена\n          </button>\n          <button\n            onClick={handlePaymentConfirm}\n            className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Подтвердить оплату\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg p-6\">\n      <h3 className=\"text-xl font-bold text-gray-900 mb-6\">Форма бронирования</h3>\n      \n      {/* Booking Details */}\n      <div className=\"bg-blue-50 rounded-lg p-4 mb-6\">\n        <div className=\"flex items-center text-blue-800 mb-2\">\n          <MapPin className=\"w-4 h-4 mr-2\" />\n          <span className=\"font-semibold\">Зал {hallId}, Корт {court}</span>\n        </div>\n        <div className=\"flex items-center text-blue-700 mb-1\">\n          <Calendar className=\"w-4 h-4 mr-2\" />\n          <span>{format(date, 'dd MMMM yyyy', { locale: ru })}</span>\n        </div>\n        <div className=\"flex items-center text-blue-700\">\n          <Clock className=\"w-4 h-4 mr-2\" />\n          <span>{time}</span>\n        </div>\n      </div>\n\n      <form onSubmit={handleSubmit(handleFormSubmit)} className=\"space-y-4\">\n        {/* Name Field */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Имя *\n          </label>\n          <div className=\"relative\">\n            <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n            <input\n              {...register('name')}\n              type=\"text\"\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"Введите ваше имя\"\n            />\n          </div>\n          {errors.name && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.name.message}</p>\n          )}\n        </div>\n\n        {/* Phone Field */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Телефон *\n          </label>\n          <div className=\"relative\">\n            <Phone className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n            <input\n              {...register('phone')}\n              type=\"tel\"\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"+373 XX XXX XXX\"\n            />\n          </div>\n          {errors.phone && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.phone.message}</p>\n          )}\n        </div>\n\n        {/* Email Field (Optional) */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Email (необязательно)\n          </label>\n          <input\n            {...register('email')}\n            type=\"email\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            placeholder=\"<EMAIL>\"\n          />\n          {errors.email && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n          )}\n        </div>\n\n        {/* Price Info */}\n        <div className=\"bg-gray-50 rounded-lg p-3\">\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">Стоимость (1 час):</span>\n            <span className=\"text-lg font-bold text-blue-600\">{pricePerHour} лей</span>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex space-x-3 pt-4\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            Отмена\n          </button>\n          <button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\"\n          >\n            {isSubmitting ? 'Обработка...' : 'Продолжить к оплате'}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAUA,MAAM,gBAAgB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,4BAA4B,QAAQ,GAAG,EAAE,CAAC,gLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;AAC9E;AAae,SAAS,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAoB;;IACrG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAEtE,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACpC,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,eAAe,KAAK,aAAa;IAEvC,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,MAAM,uBAAuB;QAC3B,MAAM,WAAW,IAAI,SAAS,SAAS,aAAa,CAAC;QACrD,MAAM,OAAO;YACX,MAAM,SAAS,GAAG,CAAC;YACnB,OAAO,SAAS,GAAG,CAAC;YACpB,OAAO,SAAS,GAAG,CAAC;YACpB;YACA;YACA;YACA;QACF;QACA,SAAS;IACX;IAEA,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAuC;;;;;;8BAGrD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;wCAAI;wCAAK;wCAAO;wCAAQ;;;;;;;8CACzB,6LAAC;8CAAK,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,gBAAgB;wCAAE,QAAQ,8IAAA,CAAA,KAAE;oCAAC;;;;;;8CAChD,6LAAC;8CAAK;;;;;;8CACN,6LAAC;oCAAI,WAAU;;wCAA2C;wCAC5C;wCAAa;;;;;;;;;;;;;;;;;;;8BAM/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAW,CAAC,uDAAuD,EACjE,kBAAkB,OAAO,+BAA+B,mBACxD;;sDAEF,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAW,CAAC,uDAAuD,EACjE,kBAAkB,aAAa,+BAA+B,mBAC9D;;sDAEF,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;gBAO5C,kBAAkB,sBACjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;gBAMxC,kBAAkB,4BACjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDAAI,6LAAC;sDAAO;;;;;;wCAAoB;;;;;;;8CACjC,6LAAC;;sDAAI,6LAAC;sDAAO;;;;;;wCAAc;;;;;;;8CAC3B,6LAAC;;sDAAI,6LAAC;sDAAO;;;;;;wCAAc;;;;;;;8CAC3B,6LAAC;;sDAAI,6LAAC;sDAAO;;;;;;wCAAe;wCAAE;wCAAa;;;;;;;8CAC3C,6LAAC;;sDAAI,6LAAC;sDAAO;;;;;;wCAAoB;wCAAmB;wCAAO;wCAAQ;wCAAM;wCAAG,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;wCAAc;wCAAE;;;;;;;;;;;;;;;;;;;8BAM/G,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAuC;;;;;;0BAGrD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAK,WAAU;;oCAAgB;oCAAK;oCAAO;oCAAQ;;;;;;;;;;;;;kCAEtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;0CAAM,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,gBAAgB;oCAAE,QAAQ,8IAAA,CAAA,KAAE;gCAAC;;;;;;;;;;;;kCAEnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;0CAAM;;;;;;;;;;;;;;;;;;0BAIX,6LAAC;gBAAK,UAAU,aAAa;gBAAmB,WAAU;;kCAExD,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCACE,GAAG,SAAS,OAAO;wCACpB,MAAK;wCACL,WAAU;wCACV,aAAY;;;;;;;;;;;;4BAGf,OAAO,IAAI,kBACV,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;kCAKjE,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCACE,GAAG,SAAS,QAAQ;wCACrB,MAAK;wCACL,WAAU;wCACV,aAAY;;;;;;;;;;;;4BAGf,OAAO,KAAK,kBACX,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAKlE,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACE,GAAG,SAAS,QAAQ;gCACrB,MAAK;gCACL,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,KAAK,kBACX,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAKlE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;oCAAK,WAAU;;wCAAmC;wCAAa;;;;;;;;;;;;;;;;;;kCAKpE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,eAAe,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAM7C;GA9NwB;;QAQlB,iKAAA,CAAA,UAAO;;;KARW", "debugId": null}}, {"offset": {"line": 1835, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport HallCard from '@/components/HallCard';\nimport BookingCalendar from '@/components/BookingCalendar';\nimport BookingForm from '@/components/BookingForm';\nimport { Star, Users, Clock, MapPin, Phone, Mail, CheckCircle } from 'lucide-react';\n\ninterface BookingData {\n  name: string;\n  phone: string;\n  email?: string;\n  hallId: number;\n  date: Date;\n  time: string;\n  court: number;\n}\n\nexport default function Home() {\n  const [selectedHall, setSelectedHall] = useState<number | null>(null);\n  const [selectedSlot, setSelectedSlot] = useState<{\n    date: Date;\n    time: string;\n    court: number;\n  } | null>(null);\n  const [bookingComplete, setBookingComplete] = useState(false);\n\n  const halls = [\n    {\n      id: 1,\n      name: 'Зал 1',\n      courts: 3,\n      image: '/hall1.jpg',\n      description: 'Уютный зал с профессиональными кортами для игры в бадминтон',\n      features: [\n        'Профессиональное покрытие',\n        'Отличное освещение',\n        'Кондиционирование воздуха',\n        'Раздевалки с душем'\n      ],\n      pricePerHour: 150\n    },\n    {\n      id: 2,\n      name: 'Зал 2',\n      courts: 7,\n      image: '/hall2.jpg',\n      description: 'Большой зал с семью кортами для турниров и тренировок',\n      features: [\n        'Турнирные корты',\n        'Трибуны для зрителей',\n        'Профессиональная разметка',\n        'Система вентиляции',\n        'Звуковая система'\n      ],\n      pricePerHour: 180\n    },\n    {\n      id: 3,\n      name: 'Зал 3',\n      courts: 7,\n      image: '/hall3.jpg',\n      description: 'Современный зал с новейшим оборудованием',\n      features: [\n        'Новейшее покрытие',\n        'LED освещение',\n        'Климат-контроль',\n        'VIP раздевалки',\n        'Зона отдыха'\n      ],\n      pricePerHour: 200\n    }\n  ];\n\n  const handleHallSelect = (hallId: number) => {\n    setSelectedHall(hallId);\n    setSelectedSlot(null);\n  };\n\n  const handleSlotSelect = (date: Date, time: string, court: number) => {\n    setSelectedSlot({ date, time, court });\n  };\n\n  const handleBookingSubmit = (data: BookingData) => {\n    console.log('Booking submitted:', data);\n    setBookingComplete(true);\n    // Здесь будет отправка данных на сервер\n  };\n\n  const handleBookingCancel = () => {\n    setSelectedSlot(null);\n    setSelectedHall(null);\n  };\n\n  const resetBooking = () => {\n    setBookingComplete(false);\n    setSelectedSlot(null);\n    setSelectedHall(null);\n  };\n\n  if (bookingComplete) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"bg-white rounded-xl shadow-lg p-8 text-center\">\n            <CheckCircle className=\"w-16 h-16 text-green-500 mx-auto mb-4\" />\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">\n              Бронирование успешно!\n            </h2>\n            <p className=\"text-gray-600 mb-6\">\n              Ваша заявка принята. Мы свяжемся с вами в ближайшее время для подтверждения.\n            </p>\n            <button\n              onClick={resetBooking}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors\"\n            >\n              Сделать новое бронирование\n            </button>\n          </div>\n        </main>\n        <Footer />\n      </div>\n    );\n  }\n\n  // Booking flow\n  if (selectedSlot && selectedHall) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <BookingForm\n            hallId={selectedHall}\n            date={selectedSlot.date}\n            time={selectedSlot.time}\n            court={selectedSlot.court}\n            onSubmit={handleBookingSubmit}\n            onCancel={handleBookingCancel}\n          />\n        </main>\n        <Footer />\n      </div>\n    );\n  }\n\n  if (selectedHall) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <main className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"mb-6\">\n            <button\n              onClick={() => setSelectedHall(null)}\n              className=\"text-blue-600 hover:text-blue-700 font-medium\"\n            >\n              ← Назад к выбору зала\n            </button>\n          </div>\n          <BookingCalendar\n            hallId={selectedHall}\n            onSlotSelect={handleSlotSelect}\n          />\n        </main>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n\n      {/* Hero Section */}\n      <section id=\"home\" className=\"bg-gradient-to-br from-blue-600 to-blue-800 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              Бадминтонный клуб\n              <span className=\"block text-blue-200\">в Кишиневе</span>\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto\">\n              Современные залы с профессиональными кортами.\n              Удобная система онлайн бронирования. 17 кортов в 3 залах.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href=\"#halls\"\n                className=\"bg-white text-blue-600 font-semibold py-3 px-8 rounded-lg hover:bg-blue-50 transition-colors\"\n              >\n                Посмотреть залы\n              </a>\n              <a\n                href=\"#booking\"\n                className=\"border-2 border-white text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:text-blue-600 transition-colors\"\n              >\n                Забронировать\n              </a>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Почему выбирают нас\n            </h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Мы предлагаем лучшие условия для игры в бадминтон в Кишиневе\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Users className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">17 кортов</h3>\n              <p className=\"text-gray-600\">\n                3 современных зала с профессиональными кортами для игры и тренировок\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Clock className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Удобное время</h3>\n              <p className=\"text-gray-600\">\n                Работаем с 6:00 до 23:00 в будни и с 8:00 до 22:00 в выходные\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Star className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Высокое качество</h3>\n              <p className=\"text-gray-600\">\n                Профессиональное покрытие, отличное освещение и климат-контроль\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Halls Section */}\n      <section id=\"halls\" className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Наши залы\n            </h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Выберите подходящий зал для вашей игры\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {halls.map((hall) => (\n              <HallCard\n                key={hall.id}\n                {...hall}\n                onBookClick={handleHallSelect}\n              />\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Section */}\n      <section id=\"contact\" className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Контакты\n            </h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Свяжитесь с нами для получения дополнительной информации\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-12\">\n            {/* Contact Info */}\n            <div>\n              <h3 className=\"text-xl font-semibold mb-6\">Как нас найти</h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-start\">\n                  <MapPin className=\"w-5 h-5 text-blue-600 mt-1 mr-3\" />\n                  <div>\n                    <div className=\"font-medium\">Адрес</div>\n                    <div className=\"text-gray-600\">ул. Примерная, 123, Кишинев, Молдова</div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start\">\n                  <Phone className=\"w-5 h-5 text-blue-600 mt-1 mr-3\" />\n                  <div>\n                    <div className=\"font-medium\">Телефон</div>\n                    <div className=\"text-gray-600\">+373 XX XXX XXX</div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start\">\n                  <Mail className=\"w-5 h-5 text-blue-600 mt-1 mr-3\" />\n                  <div>\n                    <div className=\"font-medium\">Email</div>\n                    <div className=\"text-gray-600\"><EMAIL></div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start\">\n                  <Clock className=\"w-5 h-5 text-blue-600 mt-1 mr-3\" />\n                  <div>\n                    <div className=\"font-medium\">Режим работы</div>\n                    <div className=\"text-gray-600\">\n                      <div>Пн-Пт: 06:00 - 23:00</div>\n                      <div>Сб-Вс: 08:00 - 22:00</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Map Placeholder */}\n            <div>\n              <h3 className=\"text-xl font-semibold mb-6\">Расположение</h3>\n              <div className=\"bg-gray-200 rounded-lg h-64 flex items-center justify-center\">\n                <div className=\"text-center text-gray-500\">\n                  <MapPin className=\"w-12 h-12 mx-auto mb-2\" />\n                  <div>Карта будет здесь</div>\n                  <div className=\"text-sm\">Кишинев, Молдова</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAoBe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAIrC;IACV,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,cAAc;QAChB;QACA;YACE,IAAI;YAC<PERSON>,MAAM;YACN,QAAQ;YACR,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,cAAc;QAChB;KACD;IAED,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAC,MAAY,MAAc;QAClD,gBAAgB;YAAE;YAAM;YAAM;QAAM;IACtC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,sBAAsB;QAClC,mBAAmB;IACnB,wCAAwC;IAC1C;IAEA,MAAM,sBAAsB;QAC1B,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,eAAe;QACnB,mBAAmB;QACnB,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,IAAI,iBAAiB;QACnB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAK,WAAU;8BACd,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAKL,6LAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;IAGb;IAEA,eAAe;IACf,IAAI,gBAAgB,cAAc;QAChC,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAK,WAAU;8BACd,cAAA,6LAAC,oIAAA,CAAA,UAAW;wBACV,QAAQ;wBACR,MAAM,aAAa,IAAI;wBACvB,MAAM,aAAa,IAAI;wBACvB,OAAO,aAAa,KAAK;wBACzB,UAAU;wBACV,UAAU;;;;;;;;;;;8BAGd,6LAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;IAGb;IAEA,IAAI,cAAc;QAChB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAK,WAAU;;sCACd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;;;;;;sCAIH,6LAAC,wIAAA,CAAA,UAAe;4BACd,QAAQ;4BACR,cAAc;;;;;;;;;;;;8BAGlB,6LAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;IAGb;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;0BAGP,6LAAC;gBAAQ,IAAG;gBAAO,WAAU;0BAC3B,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAsC;kDAElD,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAExC,6LAAC;gCAAE,WAAU;0CAA2D;;;;;;0CAIxE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,6LAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,6LAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,iIAAA,CAAA,UAAQ;oCAEN,GAAG,IAAI;oCACR,aAAa;mCAFR,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0BAUtB,6LAAC;gBAAQ,IAAG;gBAAU,WAAU;0BAC9B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAc;;;;;;8EAC7B,6LAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;8DAInC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAc;;;;;;8EAC7B,6LAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;8DAInC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAc;;;;;;8EAC7B,6LAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;8DAInC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAc;;;;;;8EAC7B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAI;;;;;;sFACL,6LAAC;sFAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQf,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;kEAAI;;;;;;kEACL,6LAAC;wDAAI,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrC,6LAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GAvUwB;KAAA", "debugId": null}}]}