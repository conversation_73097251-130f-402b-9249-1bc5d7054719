{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { Menu, X, Phone, MapPin } from 'lucide-react';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleNavigation = (href: string) => {\n    if (href.startsWith('#')) {\n      // Якорная ссылка - переходим на главную страницу если не на ней\n      if (pathname !== '/') {\n        router.push('/' + href);\n      } else {\n        // Если уже на главной странице, просто скроллим\n        const element = document.querySelector(href);\n        if (element) {\n          element.scrollIntoView({ behavior: 'smooth' });\n        }\n      }\n    } else {\n      // Обычная ссылка\n      router.push(href);\n    }\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <div className=\"text-2xl font-bold text-altius-blue\">\n              🏸 Altius\n            </div>\n            <div className=\"ml-2 text-sm text-gray-600 hidden sm:block\">\n              Кишинев\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <button\n              onClick={() => handleNavigation('/')}\n              className=\"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer\"\n            >\n              Главная\n            </button>\n            <button\n              onClick={() => handleNavigation('/about')}\n              className=\"text-gray-700 hover:text-altius-lime transition-colors cursor-pointer\"\n            >\n              О нас\n            </button>\n            <button\n              onClick={() => handleNavigation('#halls')}\n              className=\"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer\"\n            >\n              Залы\n            </button>\n            <button\n              onClick={() => handleNavigation('/services')}\n              className=\"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer\"\n            >\n              Услуги\n            </button>\n            <button\n              onClick={() => handleNavigation('/contact')}\n              className=\"text-gray-700 hover:text-altius-lime transition-colors cursor-pointer\"\n            >\n              Контакты\n            </button>\n            <button\n              onClick={() => handleNavigation('/blog')}\n              className=\"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer\"\n            >\n              Блог\n            </button>\n            <Link href=\"/admin\" className=\"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer\">\n              Админ\n            </Link>\n          </nav>\n\n          {/* Contact Info */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Phone className=\"w-4 h-4 mr-1\" />\n              +373 XX XXX XXX\n            </div>\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <MapPin className=\"w-4 h-4 mr-1\" />\n              Кишинев\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden cursor-pointer\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {isMenuOpen ? (\n              <X className=\"w-6 h-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"w-6 h-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <button\n                onClick={() => handleNavigation('/')}\n                className=\"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer\"\n              >\n                Главная\n              </button>\n              <button\n                onClick={() => handleNavigation('/about')}\n                className=\"text-gray-700 hover:text-altius-lime transition-colors text-left cursor-pointer\"\n              >\n                О нас\n              </button>\n              <button\n                onClick={() => handleNavigation('#halls')}\n                className=\"text-gray-700 hover:text-altius-orange transition-colors text-left cursor-pointer\"\n              >\n                Залы\n              </button>\n              <button\n                onClick={() => handleNavigation('/services')}\n                className=\"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer\"\n              >\n                Услуги\n              </button>\n              <button\n                onClick={() => handleNavigation('/contact')}\n                className=\"text-gray-700 hover:text-altius-lime transition-colors text-left cursor-pointer\"\n              >\n                Контакты\n              </button>\n              <button\n                onClick={() => handleNavigation('/blog')}\n                className=\"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer\"\n              >\n                Блог\n              </button>\n              <Link href=\"/admin\" className=\"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer\">\n                Админ\n              </Link>\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center text-sm text-gray-600 mb-2\">\n                  <Phone className=\"w-4 h-4 mr-1\" />\n                  +373 XX XXX XXX\n                </div>\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <MapPin className=\"w-4 h-4 mr-1\" />\n                  Кишинев\n                </div>\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,mBAAmB,CAAC;QACxB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,gEAAgE;YAChE,IAAI,aAAa,KAAK;gBACpB,OAAO,IAAI,CAAC,MAAM;YACpB,OAAO;gBACL,gDAAgD;gBAChD,MAAM,UAAU,SAAS,aAAa,CAAC;gBACvC,IAAI,SAAS;oBACX,QAAQ,cAAc,CAAC;wBAAE,UAAU;oBAAS;gBAC9C;YACF;QACF,OAAO;YACL,iBAAiB;YACjB,OAAO,IAAI,CAAC;QACd;QACA,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CAAsC;;;;;;8CAGrD,6LAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAM9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA0E;;;;;;;;;;;;sCAM1G,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE7B,2BACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAA0E;;;;;;0CAGxG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GArKwB;;QAEP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHN", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Footer.tsx"], "sourcesContent": ["import { Phone, MapPin, Clock, Mail } from 'lucide-react';\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {/* Company Info */}\n          <div>\n            <div className=\"text-2xl font-bold text-altius-lime mb-4\">\n              🏸 Altius\n            </div>\n            <p className=\"text-gray-300 mb-4\">\n              Современный бадминтонный клуб в Кишиневе с профессиональными кортами\n              и удобной системой бронирования.\n            </p>\n            <div className=\"flex items-center text-gray-300 mb-2\">\n              <MapPin className=\"w-4 h-4 mr-2\" />\n              Кишинев, Молдова\n            </div>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Контакты</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center text-gray-300\">\n                <Phone className=\"w-4 h-4 mr-2\" />\n                +373 XX XXX XXX\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <Mail className=\"w-4 h-4 mr-2\" />\n                <EMAIL>\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <MapPin className=\"w-4 h-4 mr-2\" />\n                ул. Примерная, 123, Кишинев\n              </div>\n            </div>\n          </div>\n\n          {/* Working Hours */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Режим работы</h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-gray-300\">\n                <Clock className=\"w-4 h-4 mr-2\" />\n                <div>\n                  <div>Пн-Пт: 06:00 - 23:00</div>\n                  <div>Сб-Вс: 08:00 - 22:00</div>\n                </div>\n              </div>\n            </div>\n            \n            {/* Halls Info */}\n            <div className=\"mt-6\">\n              <h4 className=\"font-semibold mb-2\">Наши залы:</h4>\n              <div className=\"text-sm text-gray-300 space-y-1\">\n                <div>Зал 1: 3 корта</div>\n                <div>Зал 2: 7 кортов</div>\n                <div>Зал 3: 7 кортов</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n          <p>&copy; 2024 Altius Кишинев. Все права защищены.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAA2C;;;;;;8CAG1D,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOzC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;;kEACC,6LAAC;kEAAI;;;;;;kEACL,6LAAC;kEAAI;;;;;;;;;;;;;;;;;;;;;;;8CAMX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb;KAtEwB", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\n// Get environment variables\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';\n\n// Check if Supabase is properly configured\nconst isSupabaseConfigured =\n  supabaseUrl &&\n  supabaseAnonKey &&\n  supabaseUrl.includes('supabase.co') &&\n  supabaseAnonKey.length > 100; // JWT tokens are long\n\n// Log configuration status for debugging\nconsole.log('Supabase Configuration:', {\n  url: supabaseUrl ? `${supabaseUrl.substring(0, 30)}...` : 'NOT SET',\n  keyLength: supabaseAnonKey.length,\n  isConfigured: isSupabaseConfigured\n});\n\n// Always use the provided values, even if they might be invalid\n// This way we get proper error messages instead of dummy URLs\nexport const supabase = createClient(\n  supabaseUrl || 'https://placeholder.supabase.co',\n  supabaseAnonKey || 'placeholder-key'\n);\n\n// Helper function to check if Supabase is available\nexport const isSupabaseAvailable = () => isSupabaseConfigured;\n\n// Database types\nexport interface Booking {\n  id: string;\n  name: string;\n  phone: string;\n  email?: string;\n  hall_id: number;\n  court: number;\n  date: string;\n  time: string;\n  status: 'pending' | 'confirmed' | 'cancelled';\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Hall {\n  id: number;\n  name: string;\n  courts_count: number;\n  price_per_hour: number;\n  description: string;\n  features: string[];\n  created_at: string;\n}\n\n// Booking functions\nexport const bookingService = {\n  // Get all bookings\n  async getBookings() {\n    const { data, error } = await supabase\n      .from('bookings')\n      .select('*')\n      .order('date', { ascending: true });\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Get bookings for a specific date and hall\n  async getBookingsByDateAndHall(date: string, hallId?: number) {\n    let query = supabase\n      .from('bookings')\n      .select('*')\n      .eq('date', date);\n    \n    if (hallId) {\n      query = query.eq('hall_id', hallId);\n    }\n    \n    const { data, error } = await query;\n    if (error) throw error;\n    return data;\n  },\n\n  // Create a new booking\n  async createBooking(booking: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) {\n    const { data, error } = await supabase\n      .from('bookings')\n      .insert([booking])\n      .select()\n      .single();\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Update booking status\n  async updateBookingStatus(id: string, status: 'pending' | 'confirmed' | 'cancelled') {\n    const { data, error } = await supabase\n      .from('bookings')\n      .update({ status, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single();\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Delete a booking\n  async deleteBooking(id: string) {\n    const { error } = await supabase\n      .from('bookings')\n      .delete()\n      .eq('id', id);\n    \n    if (error) throw error;\n  },\n\n  // Check if a slot is available\n  async isSlotAvailable(hallId: number, court: number, date: string, time: string) {\n    const { data, error } = await supabase\n      .from('bookings')\n      .select('id')\n      .eq('hall_id', hallId)\n      .eq('court', court)\n      .eq('date', date)\n      .eq('time', time)\n      .neq('status', 'cancelled');\n    \n    if (error) throw error;\n    return data.length === 0;\n  }\n};\n\n// Hall functions\nexport const hallService = {\n  // Get all halls\n  async getHalls() {\n    const { data, error } = await supabase\n      .from('halls')\n      .select('*')\n      .order('id', { ascending: true });\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Get a specific hall\n  async getHall(id: number) {\n    const { data, error } = await supabase\n      .from('halls')\n      .select('*')\n      .eq('id', id)\n      .single();\n    \n    if (error) throw error;\n    return data;\n  }\n};\n"], "names": [], "mappings": ";;;;;;AAGoB;AAHpB;;AAEA,4BAA4B;AAC5B,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AAErE,2CAA2C;AAC3C,MAAM,uBACJ,eACA,mBACA,YAAY,QAAQ,CAAC,kBACrB,gBAAgB,MAAM,GAAG,KAAK,sBAAsB;AAEtD,yCAAyC;AACzC,QAAQ,GAAG,CAAC,2BAA2B;IACrC,KAAK,uCAAc,GAAG,YAAY,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC;IACvD,WAAW,gBAAgB,MAAM;IACjC,cAAc;AAChB;AAIO,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EACjC,eAAe,mCACf,mBAAmB;AAId,MAAM,sBAAsB,IAAM;AA4BlC,MAAM,iBAAiB;IAC5B,mBAAmB;IACnB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK;QAEnC,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,4CAA4C;IAC5C,MAAM,0BAAyB,IAAY,EAAE,MAAe;QAC1D,IAAI,QAAQ,SACT,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ;QAEd,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,WAAW;QAC9B;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAC9B,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,uBAAuB;IACvB,MAAM,eAAc,OAA0D;QAC5E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAC;SAAQ,EAChB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,wBAAwB;IACxB,MAAM,qBAAoB,EAAU,EAAE,MAA6C;QACjF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAE;YAAQ,YAAY,IAAI,OAAO,WAAW;QAAG,GACtD,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,mBAAmB;IACnB,MAAM,eAAc,EAAU;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,+BAA+B;IAC/B,MAAM,iBAAgB,MAAc,EAAE,KAAa,EAAE,IAAY,EAAE,IAAY;QAC7E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,SAAS,OACZ,EAAE,CAAC,QAAQ,MACX,EAAE,CAAC,QAAQ,MACX,GAAG,CAAC,UAAU;QAEjB,IAAI,OAAO,MAAM;QACjB,OAAO,KAAK,MAAM,KAAK;IACzB;AACF;AAGO,MAAM,cAAc;IACzB,gBAAgB;IAChB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,MAAM;YAAE,WAAW;QAAK;QAEjC,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,SAAQ,EAAU;QACtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/app/admin/posts/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport { supabase } from '@/lib/supabase';\nimport { Post } from '@/types';\nimport { \n  Plus, \n  Edit, \n  Eye,\n  EyeOff,\n  Trash2,\n  Calendar,\n  MapPin,\n  Tag,\n  User,\n  Clock\n} from 'lucide-react';\n\nexport default function AdminPostsPage() {\n  const [posts, setPosts] = useState<Post[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filter, setFilter] = useState<'all' | 'post' | 'event' | 'draft' | 'published'>('all');\n\n  useEffect(() => {\n    fetchPosts();\n  }, []);\n\n  const fetchPosts = async () => {\n    console.log('Loading posts for admin...');\n    setLoading(true);\n\n    try {\n      const { data, error } = await supabase\n        .from('posts')\n        .select('*')\n        .order('created_at', { ascending: false });\n\n      if (error) {\n        console.error('Supabase error:', error);\n        throw error;\n      }\n\n      if (data) {\n        console.log('Loaded posts from Supabase:', data.length);\n        setPosts(data);\n      } else {\n        console.log('No posts found');\n        setPosts([]);\n      }\n    } catch (error) {\n      console.error('Error loading posts:', error);\n      setPosts([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const togglePostStatus = async (post: Post) => {\n    const newStatus = post.status === 'published' ? 'draft' : 'published';\n    \n    try {\n      const { error } = await supabase\n        .from('posts')\n        .update({ status: newStatus })\n        .eq('id', post.id);\n\n      if (error) {\n        console.error('Error updating post status:', error);\n        alert('Ошибка при изменении статуса поста');\n        return;\n      }\n\n      // Update local state\n      setPosts(posts.map(p => \n        p.id === post.id ? { ...p, status: newStatus } : p\n      ));\n    } catch (error) {\n      console.error('Error updating post status:', error);\n      alert('Ошибка при изменении статуса поста');\n    }\n  };\n\n  const deletePost = async (post: Post) => {\n    if (!confirm(`Вы уверены, что хотите удалить пост \"${post.title}\"?`)) {\n      return;\n    }\n\n    try {\n      const { error } = await supabase\n        .from('posts')\n        .delete()\n        .eq('id', post.id);\n\n      if (error) {\n        console.error('Error deleting post:', error);\n        alert('Ошибка при удалении поста');\n        return;\n      }\n\n      // Update local state\n      setPosts(posts.filter(p => p.id !== post.id));\n    } catch (error) {\n      console.error('Error deleting post:', error);\n      alert('Ошибка при удалении поста');\n    }\n  };\n\n  const filteredPosts = posts.filter(post => {\n    if (filter === 'all') return true;\n    if (filter === 'post' || filter === 'event') return post.category === filter;\n    if (filter === 'draft' || filter === 'published') return post.status === filter;\n    return true;\n  });\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('ru-RU', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const formatEventDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('ru-RU', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Header */}\n        <div className=\"flex justify-between items-center mb-8\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">Управление постами</h1>\n            <p className=\"text-gray-600 mt-2\">Создавайте и редактируйте новости и события</p>\n          </div>\n          <Link\n            href=\"/admin/posts/new\"\n            className=\"bg-altius-blue text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center\"\n          >\n            <Plus className=\"w-5 h-5 mr-2\" />\n            Создать пост\n          </Link>\n        </div>\n\n        {/* Filters */}\n        <div className=\"bg-white rounded-lg shadow-md p-4 mb-8\">\n          <div className=\"flex flex-wrap gap-2\">\n            {[\n              { key: 'all', label: 'Все', color: 'gray' },\n              { key: 'post', label: 'Новости', color: 'lime' },\n              { key: 'event', label: 'События', color: 'orange' },\n              { key: 'published', label: 'Опубликованные', color: 'green' },\n              { key: 'draft', label: 'Черновики', color: 'yellow' }\n            ].map(({ key, label, color }) => (\n              <button\n                key={key}\n                onClick={() => setFilter(key as any)}\n                className={`px-4 py-2 rounded-lg font-medium transition-colors ${\n                  filter === key\n                    ? `bg-${color === 'gray' ? 'gray' : color === 'lime' ? 'altius-lime' : color === 'orange' ? 'altius-orange' : color === 'green' ? 'green' : 'yellow'}-${color === 'gray' ? '800' : '500'} text-white`\n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                }`}\n              >\n                {label}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Loading State */}\n        {loading && (\n          <div className=\"text-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-altius-blue mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Загрузка постов...</p>\n          </div>\n        )}\n\n        {/* Posts List */}\n        {!loading && (\n          <div className=\"space-y-4\">\n            {filteredPosts.map((post) => (\n              <div\n                key={post.id}\n                className=\"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow\"\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    {/* Header */}\n                    <div className=\"flex items-center space-x-3 mb-3\">\n                      <span\n                        className={`px-3 py-1 rounded-full text-xs font-medium ${\n                          post.category === 'event'\n                            ? 'bg-altius-orange text-white'\n                            : 'bg-altius-lime text-white'\n                        }`}\n                      >\n                        {post.category === 'event' ? 'Событие' : 'Новость'}\n                      </span>\n                      <span\n                        className={`px-3 py-1 rounded-full text-xs font-medium ${\n                          post.status === 'published'\n                            ? 'bg-green-100 text-green-800'\n                            : post.status === 'draft'\n                            ? 'bg-yellow-100 text-yellow-800'\n                            : 'bg-gray-100 text-gray-800'\n                        }`}\n                      >\n                        {post.status === 'published' ? 'Опубликован' : \n                         post.status === 'draft' ? 'Черновик' : 'Архив'}\n                      </span>\n                      <div className=\"flex items-center text-gray-500 text-sm\">\n                        <Eye className=\"w-4 h-4 mr-1\" />\n                        {post.views_count}\n                      </div>\n                    </div>\n\n                    {/* Title and Excerpt */}\n                    <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                      {post.title}\n                    </h3>\n                    {post.excerpt && (\n                      <p className=\"text-gray-600 mb-3 line-clamp-2\">\n                        {post.excerpt}\n                      </p>\n                    )}\n\n                    {/* Event Details */}\n                    {post.category === 'event' && (\n                      <div className=\"flex items-center space-x-4 mb-3 text-sm text-gray-600\">\n                        {post.event_date && (\n                          <div className=\"flex items-center\">\n                            <Calendar className=\"w-4 h-4 mr-1 text-altius-orange\" />\n                            {formatEventDate(post.event_date)}\n                          </div>\n                        )}\n                        {post.event_location && (\n                          <div className=\"flex items-center\">\n                            <MapPin className=\"w-4 h-4 mr-1 text-altius-orange\" />\n                            {post.event_location}\n                          </div>\n                        )}\n                      </div>\n                    )}\n\n                    {/* Tags */}\n                    {post.tags && post.tags.length > 0 && (\n                      <div className=\"flex flex-wrap gap-1 mb-3\">\n                        {post.tags.slice(0, 3).map((tag, index) => (\n                          <span\n                            key={index}\n                            className=\"inline-flex items-center px-2 py-1 rounded-md bg-gray-100 text-gray-600 text-xs\"\n                          >\n                            <Tag className=\"w-3 h-3 mr-1\" />\n                            {tag}\n                          </span>\n                        ))}\n                        {post.tags.length > 3 && (\n                          <span className=\"text-xs text-gray-500\">\n                            +{post.tags.length - 3} еще\n                          </span>\n                        )}\n                      </div>\n                    )}\n\n                    {/* Meta Info */}\n                    <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                      <div className=\"flex items-center\">\n                        <User className=\"w-4 h-4 mr-1\" />\n                        {post.author_name}\n                      </div>\n                      <div className=\"flex items-center\">\n                        <Clock className=\"w-4 h-4 mr-1\" />\n                        {formatDate(post.created_at)}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Actions */}\n                  <div className=\"flex items-center space-x-2 ml-4\">\n                    <Link\n                      href={`/blog/${post.slug}`}\n                      target=\"_blank\"\n                      className=\"p-2 text-gray-400 hover:text-altius-blue transition-colors\"\n                      title=\"Просмотреть\"\n                    >\n                      <Eye className=\"w-5 h-5\" />\n                    </Link>\n                    <Link\n                      href={`/admin/posts/${post.id}`}\n                      className=\"p-2 text-gray-400 hover:text-altius-lime transition-colors\"\n                      title=\"Редактировать\"\n                    >\n                      <Edit className=\"w-5 h-5\" />\n                    </Link>\n                    <button\n                      onClick={() => togglePostStatus(post)}\n                      className=\"p-2 text-gray-400 hover:text-yellow-600 transition-colors\"\n                      title={post.status === 'published' ? 'Снять с публикации' : 'Опубликовать'}\n                    >\n                      {post.status === 'published' ? (\n                        <EyeOff className=\"w-5 h-5\" />\n                      ) : (\n                        <Eye className=\"w-5 h-5\" />\n                      )}\n                    </button>\n                    <button\n                      onClick={() => deletePost(post)}\n                      className=\"p-2 text-gray-400 hover:text-red-600 transition-colors\"\n                      title=\"Удалить\"\n                    >\n                      <Trash2 className=\"w-5 h-5\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n\n        {/* Empty State */}\n        {!loading && filteredPosts.length === 0 && (\n          <div className=\"text-center py-12\">\n            <h3 className=\"text-xl font-medium text-gray-900 mb-2\">\n              Нет постов\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              {filter === 'all' \n                ? 'Создайте первый пост для вашего блога'\n                : `Нет постов в категории \"${filter}\"`\n              }\n            </p>\n            <Link\n              href=\"/admin/posts/new\"\n              className=\"bg-altius-blue text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center\"\n            >\n              <Plus className=\"w-5 h-5 mr-2\" />\n              Создать пост\n            </Link>\n          </div>\n        )}\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;AAqBe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoD;IAEvF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC;QACZ,WAAW;QAEX,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,mBAAmB;gBACjC,MAAM;YACR;YAEA,IAAI,MAAM;gBACR,QAAQ,GAAG,CAAC,+BAA+B,KAAK,MAAM;gBACtD,SAAS;YACX,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,SAAS,EAAE;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS,EAAE;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,YAAY,KAAK,MAAM,KAAK,cAAc,UAAU;QAE1D,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;gBAAE,QAAQ;YAAU,GAC3B,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM;gBACN;YACF;YAEA,qBAAqB;YACrB,SAAS,MAAM,GAAG,CAAC,CAAA,IACjB,EAAE,EAAE,KAAK,KAAK,EAAE,GAAG;oBAAE,GAAG,CAAC;oBAAE,QAAQ;gBAAU,IAAI;QAErD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI,CAAC,QAAQ,CAAC,qCAAqC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG;YACpE;QACF;QAEA,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,MAAM;gBACN;YACF;YAEA,qBAAqB;YACrB,SAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,IAAI,WAAW,OAAO,OAAO;QAC7B,IAAI,WAAW,UAAU,WAAW,SAAS,OAAO,KAAK,QAAQ,KAAK;QACtE,IAAI,WAAW,WAAW,WAAW,aAAa,OAAO,KAAK,MAAM,KAAK;QACzE,OAAO;IACT;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAEpC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMrC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,KAAK;oCAAO,OAAO;oCAAO,OAAO;gCAAO;gCAC1C;oCAAE,KAAK;oCAAQ,OAAO;oCAAW,OAAO;gCAAO;gCAC/C;oCAAE,KAAK;oCAAS,OAAO;oCAAW,OAAO;gCAAS;gCAClD;oCAAE,KAAK;oCAAa,OAAO;oCAAkB,OAAO;gCAAQ;gCAC5D;oCAAE,KAAK;oCAAS,OAAO;oCAAa,OAAO;gCAAS;6BACrD,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,iBAC1B,6LAAC;oCAEC,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAC,mDAAmD,EAC7D,WAAW,MACP,CAAC,GAAG,EAAE,UAAU,SAAS,SAAS,UAAU,SAAS,gBAAgB,UAAU,WAAW,kBAAkB,UAAU,UAAU,UAAU,SAAS,CAAC,EAAE,UAAU,SAAS,QAAQ,MAAM,WAAW,CAAC,GACnM,+CACJ;8CAED;mCARI;;;;;;;;;;;;;;;oBAeZ,yBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;oBAKhC,CAAC,yBACA,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;gCAEC,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,WAAW,CAAC,2CAA2C,EACrD,KAAK,QAAQ,KAAK,UACd,gCACA,6BACJ;sEAED,KAAK,QAAQ,KAAK,UAAU,YAAY;;;;;;sEAE3C,6LAAC;4DACC,WAAW,CAAC,2CAA2C,EACrD,KAAK,MAAM,KAAK,cACZ,gCACA,KAAK,MAAM,KAAK,UAChB,kCACA,6BACJ;sEAED,KAAK,MAAM,KAAK,cAAc,gBAC9B,KAAK,MAAM,KAAK,UAAU,aAAa;;;;;;sEAE1C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEACd,KAAK,WAAW;;;;;;;;;;;;;8DAKrB,6LAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;gDAEZ,KAAK,OAAO,kBACX,6LAAC;oDAAE,WAAU;8DACV,KAAK,OAAO;;;;;;gDAKhB,KAAK,QAAQ,KAAK,yBACjB,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,UAAU,kBACd,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,gBAAgB,KAAK,UAAU;;;;;;;wDAGnC,KAAK,cAAc,kBAClB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,KAAK,cAAc;;;;;;;;;;;;;gDAO3B,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,6LAAC;gEAEC,WAAU;;kFAEV,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEACd;;+DAJI;;;;;wDAOR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;4DAAK,WAAU;;gEAAwB;gEACpC,KAAK,IAAI,CAAC,MAAM,GAAG;gEAAE;;;;;;;;;;;;;8DAO/B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEACf,KAAK,WAAW;;;;;;;sEAEnB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,WAAW,KAAK,UAAU;;;;;;;;;;;;;;;;;;;sDAMjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;oDAC1B,QAAO;oDACP,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEjB,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;oDAC/B,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6LAAC;oDACC,SAAS,IAAM,iBAAiB;oDAChC,WAAU;oDACV,OAAO,KAAK,MAAM,KAAK,cAAc,uBAAuB;8DAE3D,KAAK,MAAM,KAAK,4BACf,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAElB,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAGnB,6LAAC;oDACC,SAAS,IAAM,WAAW;oDAC1B,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BAhInB,KAAK,EAAE;;;;;;;;;;oBA0InB,CAAC,WAAW,cAAc,MAAM,KAAK,mBACpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,6LAAC;gCAAE,WAAU;0CACV,WAAW,QACR,0CACA,CAAC,wBAAwB,EAAE,OAAO,CAAC,CAAC;;;;;;0CAG1C,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOzC,6LAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GAjVwB;KAAA", "debugId": null}}]}