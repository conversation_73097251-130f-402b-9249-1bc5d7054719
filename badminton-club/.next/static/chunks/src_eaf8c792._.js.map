{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { Menu, X, Phone, MapPin } from 'lucide-react';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleNavigation = (href: string) => {\n    if (href.startsWith('#')) {\n      // Якорная ссылка - переходим на главную страницу если не на ней\n      if (pathname !== '/') {\n        router.push('/' + href);\n      } else {\n        // Если уже на главной странице, просто скроллим\n        const element = document.querySelector(href);\n        if (element) {\n          element.scrollIntoView({ behavior: 'smooth' });\n        }\n      }\n    } else {\n      // Обычная ссылка\n      router.push(href);\n    }\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <div className=\"text-2xl font-bold text-altius-blue\">\n              🏸 Altius\n            </div>\n            <div className=\"ml-2 text-sm text-gray-600 hidden sm:block\">\n              Кишинев\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <button\n              onClick={() => handleNavigation('/')}\n              className=\"text-gray-700 hover:text-altius-blue transition-colors\"\n            >\n              Главная\n            </button>\n            <button\n              onClick={() => handleNavigation('/about')}\n              className=\"text-gray-700 hover:text-altius-lime transition-colors\"\n            >\n              О нас\n            </button>\n            <button\n              onClick={() => handleNavigation('#halls')}\n              className=\"text-gray-700 hover:text-altius-orange transition-colors\"\n            >\n              Залы\n            </button>\n            <button\n              onClick={() => handleNavigation('/services')}\n              className=\"text-gray-700 hover:text-altius-blue transition-colors\"\n            >\n              Услуги\n            </button>\n            <button\n              onClick={() => handleNavigation('/contact')}\n              className=\"text-gray-700 hover:text-altius-lime transition-colors\"\n            >\n              Контакты\n            </button>\n            <Link href=\"/admin\" className=\"text-gray-700 hover:text-altius-orange transition-colors\">\n              Админ\n            </Link>\n          </nav>\n\n          {/* Contact Info */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Phone className=\"w-4 h-4 mr-1\" />\n              +373 XX XXX XXX\n            </div>\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <MapPin className=\"w-4 h-4 mr-1\" />\n              Кишинев\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {isMenuOpen ? (\n              <X className=\"w-6 h-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"w-6 h-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <button\n                onClick={() => handleNavigation('/')}\n                className=\"text-gray-700 hover:text-altius-blue transition-colors text-left\"\n              >\n                Главная\n              </button>\n              <button\n                onClick={() => handleNavigation('/about')}\n                className=\"text-gray-700 hover:text-altius-lime transition-colors text-left\"\n              >\n                О нас\n              </button>\n              <button\n                onClick={() => handleNavigation('#halls')}\n                className=\"text-gray-700 hover:text-altius-orange transition-colors text-left\"\n              >\n                Залы\n              </button>\n              <button\n                onClick={() => handleNavigation('/services')}\n                className=\"text-gray-700 hover:text-altius-blue transition-colors text-left\"\n              >\n                Услуги\n              </button>\n              <button\n                onClick={() => handleNavigation('/contact')}\n                className=\"text-gray-700 hover:text-altius-lime transition-colors text-left\"\n              >\n                Контакты\n              </button>\n              <Link href=\"/admin\" className=\"text-gray-700 hover:text-altius-orange transition-colors\">\n                Админ\n              </Link>\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center text-sm text-gray-600 mb-2\">\n                  <Phone className=\"w-4 h-4 mr-1\" />\n                  +373 XX XXX XXX\n                </div>\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <MapPin className=\"w-4 h-4 mr-1\" />\n                  Кишинев\n                </div>\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,mBAAmB,CAAC;QACxB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,gEAAgE;YAChE,IAAI,aAAa,KAAK;gBACpB,OAAO,IAAI,CAAC,MAAM;YACpB,OAAO;gBACL,gDAAgD;gBAChD,MAAM,UAAU,SAAS,aAAa,CAAC;gBACvC,IAAI,SAAS;oBACX,QAAQ,cAAc,CAAC;wBAAE,UAAU;oBAAS;gBAC9C;YACF;QACF,OAAO;YACL,iBAAiB;YACjB,OAAO,IAAI,CAAC;QACd;QACA,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CAAsC;;;;;;8CAGrD,6LAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAM9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA2D;;;;;;;;;;;;sCAM3F,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE7B,2BACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAA2D;;;;;;0CAGzF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GAzJwB;;QAEP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHN", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Footer.tsx"], "sourcesContent": ["import { Phone, MapPin, Clock, Mail } from 'lucide-react';\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {/* Company Info */}\n          <div>\n            <div className=\"text-2xl font-bold text-altius-lime mb-4\">\n              🏸 Altius\n            </div>\n            <p className=\"text-gray-300 mb-4\">\n              Современный бадминтонный клуб в Кишиневе с профессиональными кортами\n              и удобной системой бронирования.\n            </p>\n            <div className=\"flex items-center text-gray-300 mb-2\">\n              <MapPin className=\"w-4 h-4 mr-2\" />\n              Кишинев, Молдова\n            </div>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Контакты</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center text-gray-300\">\n                <Phone className=\"w-4 h-4 mr-2\" />\n                +373 XX XXX XXX\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <Mail className=\"w-4 h-4 mr-2\" />\n                <EMAIL>\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <MapPin className=\"w-4 h-4 mr-2\" />\n                ул. Примерная, 123, Кишинев\n              </div>\n            </div>\n          </div>\n\n          {/* Working Hours */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Режим работы</h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-gray-300\">\n                <Clock className=\"w-4 h-4 mr-2\" />\n                <div>\n                  <div>Пн-Пт: 06:00 - 23:00</div>\n                  <div>Сб-Вс: 08:00 - 22:00</div>\n                </div>\n              </div>\n            </div>\n            \n            {/* Halls Info */}\n            <div className=\"mt-6\">\n              <h4 className=\"font-semibold mb-2\">Наши залы:</h4>\n              <div className=\"text-sm text-gray-300 space-y-1\">\n                <div>Зал 1: 3 корта</div>\n                <div>Зал 2: 7 кортов</div>\n                <div>Зал 3: 7 кортов</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n          <p>&copy; 2024 Altius Кишинев. Все права защищены.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAA2C;;;;;;8CAG1D,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOzC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;;kEACC,6LAAC;kEAAI;;;;;;kEACL,6LAAC;kEAAI;;;;;;;;;;;;;;;;;;;;;;;8CAMX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb;KAtEwB", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\n// Get environment variables\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';\n\n// Check if Supabase is properly configured\nconst isSupabaseConfigured =\n  supabaseUrl &&\n  supabaseAnonKey &&\n  supabaseUrl.includes('supabase.co') &&\n  supabaseAnonKey.length > 100; // JWT tokens are long\n\n// Log configuration status for debugging\nconsole.log('Supabase Configuration:', {\n  url: supabaseUrl ? `${supabaseUrl.substring(0, 30)}...` : 'NOT SET',\n  keyLength: supabaseAnonKey.length,\n  isConfigured: isSupabaseConfigured\n});\n\n// Always use the provided values, even if they might be invalid\n// This way we get proper error messages instead of dummy URLs\nexport const supabase = createClient(\n  supabaseUrl || 'https://placeholder.supabase.co',\n  supabaseAnonKey || 'placeholder-key'\n);\n\n// Helper function to check if Supabase is available\nexport const isSupabaseAvailable = () => isSupabaseConfigured;\n\n// Database types\nexport interface Booking {\n  id: string;\n  name: string;\n  phone: string;\n  email?: string;\n  hall_id: number;\n  court: number;\n  date: string;\n  time: string;\n  status: 'pending' | 'confirmed' | 'cancelled';\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Hall {\n  id: number;\n  name: string;\n  courts_count: number;\n  price_per_hour: number;\n  description: string;\n  features: string[];\n  created_at: string;\n}\n\n// Booking functions\nexport const bookingService = {\n  // Get all bookings\n  async getBookings() {\n    const { data, error } = await supabase\n      .from('bookings')\n      .select('*')\n      .order('date', { ascending: true });\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Get bookings for a specific date and hall\n  async getBookingsByDateAndHall(date: string, hallId?: number) {\n    let query = supabase\n      .from('bookings')\n      .select('*')\n      .eq('date', date);\n    \n    if (hallId) {\n      query = query.eq('hall_id', hallId);\n    }\n    \n    const { data, error } = await query;\n    if (error) throw error;\n    return data;\n  },\n\n  // Create a new booking\n  async createBooking(booking: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) {\n    const { data, error } = await supabase\n      .from('bookings')\n      .insert([booking])\n      .select()\n      .single();\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Update booking status\n  async updateBookingStatus(id: string, status: 'pending' | 'confirmed' | 'cancelled') {\n    const { data, error } = await supabase\n      .from('bookings')\n      .update({ status, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single();\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Delete a booking\n  async deleteBooking(id: string) {\n    const { error } = await supabase\n      .from('bookings')\n      .delete()\n      .eq('id', id);\n    \n    if (error) throw error;\n  },\n\n  // Check if a slot is available\n  async isSlotAvailable(hallId: number, court: number, date: string, time: string) {\n    const { data, error } = await supabase\n      .from('bookings')\n      .select('id')\n      .eq('hall_id', hallId)\n      .eq('court', court)\n      .eq('date', date)\n      .eq('time', time)\n      .neq('status', 'cancelled');\n    \n    if (error) throw error;\n    return data.length === 0;\n  }\n};\n\n// Hall functions\nexport const hallService = {\n  // Get all halls\n  async getHalls() {\n    const { data, error } = await supabase\n      .from('halls')\n      .select('*')\n      .order('id', { ascending: true });\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Get a specific hall\n  async getHall(id: number) {\n    const { data, error } = await supabase\n      .from('halls')\n      .select('*')\n      .eq('id', id)\n      .single();\n    \n    if (error) throw error;\n    return data;\n  }\n};\n"], "names": [], "mappings": ";;;;;;AAGoB;AAHpB;;AAEA,4BAA4B;AAC5B,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AAErE,2CAA2C;AAC3C,MAAM,uBACJ,eACA,mBACA,YAAY,QAAQ,CAAC,kBACrB,gBAAgB,MAAM,GAAG,KAAK,sBAAsB;AAEtD,yCAAyC;AACzC,QAAQ,GAAG,CAAC,2BAA2B;IACrC,KAAK,uCAAc,GAAG,YAAY,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC;IACvD,WAAW,gBAAgB,MAAM;IACjC,cAAc;AAChB;AAIO,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EACjC,eAAe,mCACf,mBAAmB;AAId,MAAM,sBAAsB,IAAM;AA4BlC,MAAM,iBAAiB;IAC5B,mBAAmB;IACnB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK;QAEnC,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,4CAA4C;IAC5C,MAAM,0BAAyB,IAAY,EAAE,MAAe;QAC1D,IAAI,QAAQ,SACT,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ;QAEd,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,WAAW;QAC9B;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAC9B,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,uBAAuB;IACvB,MAAM,eAAc,OAA0D;QAC5E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAC;SAAQ,EAChB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,wBAAwB;IACxB,MAAM,qBAAoB,EAAU,EAAE,MAA6C;QACjF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAE;YAAQ,YAAY,IAAI,OAAO,WAAW;QAAG,GACtD,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,mBAAmB;IACnB,MAAM,eAAc,EAAU;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,+BAA+B;IAC/B,MAAM,iBAAgB,MAAc,EAAE,KAAa,EAAE,IAAY,EAAE,IAAY;QAC7E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,SAAS,OACZ,EAAE,CAAC,QAAQ,MACX,EAAE,CAAC,QAAQ,MACX,GAAG,CAAC,UAAU;QAEjB,IAAI,OAAO,MAAM;QACjB,OAAO,KAAK,MAAM,KAAK;IACzB;AACF;AAGO,MAAM,cAAc;IACzB,gBAAgB;IAChB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,MAAM;YAAE,WAAW;QAAK;QAEjC,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,SAAQ,EAAU;QACtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/app/admin/halls/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport { supabase, isSupabaseAvailable } from '@/lib/supabase';\nimport {\n  Edit,\n  Eye,\n  EyeOff,\n  ArrowRight,\n  Building,\n  Users,\n  DollarSign\n} from 'lucide-react';\n\ninterface Hall {\n  id: number;\n  name: string;\n  courts_count: number;\n  price_per_hour: number;\n  description: string;\n  detailed_description: string;\n  features: string[];\n  images: string[];\n  videos: string[];\n  specifications: Record<string, string>;\n  amenities: string[];\n  working_hours: Record<string, string>;\n  is_active: boolean;\n}\n\nexport default function AdminHallsPage() {\n  const [halls, setHalls] = useState<Hall[]>([]);\n  const [loading, setLoading] = useState(true);\n  const router = useRouter();\n\n  useEffect(() => {\n    fetchHalls();\n  }, []);\n\n  const fetchHalls = async () => {\n    console.log('Loading halls data...');\n    setLoading(true);\n\n    try {\n      const { data, error } = await supabase\n        .from('halls')\n        .select('*')\n        .order('id');\n\n      if (error) {\n        console.error('Supabase error:', error);\n        throw error;\n      }\n\n      if (data && data.length > 0) {\n        console.log('Loaded halls from Supabase:', data.length);\n        setHalls(data);\n      } else {\n        console.log('No halls found in database, using fallback data');\n        // Fallback data if database is empty\n        setHalls([\n          {\n            id: 1,\n            name: 'Зал 1',\n            courts_count: 3,\n            price_per_hour: 150,\n            description: 'Уютный зал с профессиональными кортами для игры в бадминтон',\n            detailed_description: 'Зал 1 - это идеальное место для начинающих игроков и любителей бадминтона.',\n            features: ['Профессиональное покрытие', 'Отличное освещение', 'Кондиционирование воздуха'],\n            images: [],\n            videos: [],\n            specifications: { area: '300 м²', height: '9 м' },\n            amenities: ['Раздевалки', 'Душевые', 'Парковка'],\n            working_hours: { weekdays: '06:00 - 23:00', weekends: '08:00 - 22:00' },\n            is_active: true\n          }\n        ]);\n      }\n    } catch (error) {\n      console.error('Error loading halls:', error);\n      // Fallback data on error\n      setHalls([\n        {\n          id: 1,\n          name: 'Зал 1',\n          courts_count: 3,\n          price_per_hour: 150,\n          description: 'Уютный зал с профессиональными кортами для игры в бадминтон',\n          detailed_description: 'Зал 1 - это идеальное место для начинающих игроков и любителей бадминтона.',\n          features: ['Профессиональное покрытие', 'Отличное освещение', 'Кондиционирование воздуха'],\n          images: [],\n          videos: [],\n          specifications: { area: '300 м²', height: '9 м' },\n          amenities: ['Раздевалки', 'Душевые', 'Парковка'],\n          working_hours: { weekdays: '06:00 - 23:00', weekends: '08:00 - 22:00' },\n          is_active: true\n        },\n        {\n          id: 2,\n          name: 'Зал 2',\n          courts_count: 7,\n          price_per_hour: 180,\n          description: 'Большой зал с семью кортами для турниров и тренировок',\n          detailed_description: 'Зал 2 - наш самый большой зал для турниров.',\n          features: ['Турнирные корты', 'Трибуны для зрителей'],\n          images: [],\n          videos: [],\n          specifications: { area: '700 м²', height: '12 м' },\n          amenities: ['VIP раздевалки', 'Душевые', 'Трибуны'],\n          working_hours: { weekdays: '06:00 - 23:00', weekends: '08:00 - 22:00' },\n          is_active: true\n        },\n        {\n          id: 3,\n          name: 'Зал 3',\n          courts_count: 7,\n          price_per_hour: 200,\n          description: 'Современный зал с новейшим оборудованием',\n          detailed_description: 'Зал 3 - наш новейший зал с современным оборудованием.',\n          features: ['Новейшее покрытие', 'LED освещение'],\n          images: [],\n          videos: [],\n          specifications: { area: '700 м²', height: '12 м' },\n          amenities: ['VIP раздевалки', 'Премиум душевые'],\n          working_hours: { weekdays: '06:00 - 23:00', weekends: '08:00 - 22:00' },\n          is_active: true\n        }\n      ]);\n    }\n\n    setLoading(false);\n  };\n\n  const navigateToHallEdit = (hallId: number) => {\n    router.push(`/admin/halls/${hallId}`);\n  };\n\n  const toggleHallStatus = async (hallId: number, currentStatus: boolean) => {\n    try {\n      const { error } = await supabase\n        .from('halls')\n        .update({ is_active: !currentStatus })\n        .eq('id', hallId);\n\n      if (error) throw error;\n      await fetchHalls();\n    } catch (error) {\n      console.error('Error toggling hall status:', error);\n      // For fallback data, just update locally\n      setHalls(prev => prev.map(hall => \n        hall.id === hallId ? { ...hall, is_active: !currentStatus } : hall\n      ));\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-altius-blue mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">Загрузка залов...</p>\n          </div>\n        </main>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            Управление залами\n          </h1>\n          <p className=\"text-gray-600\">\n            Выберите зал для редактирования информации, загрузки фотографий и управления контентом\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {halls.map((hall) => (\n            <div key={hall.id} className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"bg-altius-blue/10 p-2 rounded-lg\">\n                      <Building className=\"w-6 h-6 text-altius-blue\" />\n                    </div>\n                    <div>\n                      <h2 className=\"text-xl font-bold text-gray-900\">{hall.name}</h2>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                        hall.is_active \n                          ? 'bg-green-100 text-green-800' \n                          : 'bg-red-100 text-red-800'\n                      }`}>\n                        {hall.is_active ? 'Активен' : 'Неактивен'}\n                      </span>\n                    </div>\n                  </div>\n                  \n                  <button\n                    onClick={() => toggleHallStatus(hall.id, hall.is_active)}\n                    className={`p-2 rounded-lg transition-colors ${\n                      hall.is_active\n                        ? 'text-red-600 hover:bg-red-50'\n                        : 'text-green-600 hover:bg-green-50'\n                    }`}\n                    title={hall.is_active ? 'Деактивировать' : 'Активировать'}\n                  >\n                    {hall.is_active ? <EyeOff className=\"w-5 h-5\" /> : <Eye className=\"w-5 h-5\" />}\n                  </button>\n                </div>\n\n                <p className=\"text-gray-600 mb-4 line-clamp-2\">\n                  {hall.description}\n                </p>\n\n                <div className=\"space-y-3 mb-6\">\n                  <div className=\"flex items-center text-gray-700\">\n                    <Users className=\"w-4 h-4 mr-2 text-altius-blue\" />\n                    <span className=\"text-sm\">{hall.courts_count} кортов</span>\n                  </div>\n                  <div className=\"flex items-center text-gray-700\">\n                    <DollarSign className=\"w-4 h-4 mr-2 text-altius-lime\" />\n                    <span className=\"text-sm\">{hall.price_per_hour} лей/час</span>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={() => navigateToHallEdit(hall.id)}\n                    className=\"flex-1 bg-altius-blue text-white px-4 py-2 rounded-lg hover:bg-altius-blue-dark transition-colors inline-flex items-center justify-center\"\n                  >\n                    <Edit className=\"w-4 h-4 mr-2\" />\n                    Редактировать\n                  </button>\n                  <button\n                    onClick={() => navigateToHallEdit(hall.id)}\n                    className=\"bg-altius-lime text-white p-2 rounded-lg hover:bg-altius-lime-dark transition-colors\"\n                    title=\"Открыть детали\"\n                  >\n                    <ArrowRight className=\"w-4 h-4\" />\n                  </button>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {halls.length === 0 && (\n          <div className=\"text-center py-12\">\n            <Building className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Нет залов</h3>\n            <p className=\"text-gray-600\">Залы не найдены или не загружены</p>\n          </div>\n        )}\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAiCe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC;QACZ,WAAW;QAEX,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,mBAAmB;gBACjC,MAAM;YACR;YAEA,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;gBAC3B,QAAQ,GAAG,CAAC,+BAA+B,KAAK,MAAM;gBACtD,SAAS;YACX,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,qCAAqC;gBACrC,SAAS;oBACP;wBACE,IAAI;wBACJ,MAAM;wBACN,cAAc;wBACd,gBAAgB;wBAChB,aAAa;wBACb,sBAAsB;wBACtB,UAAU;4BAAC;4BAA6B;4BAAsB;yBAA4B;wBAC1F,QAAQ,EAAE;wBACV,QAAQ,EAAE;wBACV,gBAAgB;4BAAE,MAAM;4BAAU,QAAQ;wBAAM;wBAChD,WAAW;4BAAC;4BAAc;4BAAW;yBAAW;wBAChD,eAAe;4BAAE,UAAU;4BAAiB,UAAU;wBAAgB;wBACtE,WAAW;oBACb;iBACD;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,yBAAyB;YACzB,SAAS;gBACP;oBACE,IAAI;oBACJ,MAAM;oBACN,cAAc;oBACd,gBAAgB;oBAChB,aAAa;oBACb,sBAAsB;oBACtB,UAAU;wBAAC;wBAA6B;wBAAsB;qBAA4B;oBAC1F,QAAQ,EAAE;oBACV,QAAQ,EAAE;oBACV,gBAAgB;wBAAE,MAAM;wBAAU,QAAQ;oBAAM;oBAChD,WAAW;wBAAC;wBAAc;wBAAW;qBAAW;oBAChD,eAAe;wBAAE,UAAU;wBAAiB,UAAU;oBAAgB;oBACtE,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,cAAc;oBACd,gBAAgB;oBAChB,aAAa;oBACb,sBAAsB;oBACtB,UAAU;wBAAC;wBAAmB;qBAAuB;oBACrD,QAAQ,EAAE;oBACV,QAAQ,EAAE;oBACV,gBAAgB;wBAAE,MAAM;wBAAU,QAAQ;oBAAO;oBACjD,WAAW;wBAAC;wBAAkB;wBAAW;qBAAU;oBACnD,eAAe;wBAAE,UAAU;wBAAiB,UAAU;oBAAgB;oBACtE,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,cAAc;oBACd,gBAAgB;oBAChB,aAAa;oBACb,sBAAsB;oBACtB,UAAU;wBAAC;wBAAqB;qBAAgB;oBAChD,QAAQ,EAAE;oBACV,QAAQ,EAAE;oBACV,gBAAgB;wBAAE,MAAM;wBAAU,QAAQ;oBAAO;oBACjD,WAAW;wBAAC;wBAAkB;qBAAkB;oBAChD,eAAe;wBAAE,UAAU;wBAAiB,UAAU;oBAAgB;oBACtE,WAAW;gBACb;aACD;QACH;QAEA,WAAW;IACb;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,QAAQ;IACtC;IAEA,MAAM,mBAAmB,OAAO,QAAgB;QAC9C,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;gBAAE,WAAW,CAAC;YAAc,GACnC,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YACjB,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,yCAAyC;YACzC,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,SAAS;wBAAE,GAAG,IAAI;wBAAE,WAAW,CAAC;oBAAc,IAAI;QAElE;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;8BACP,6LAAC;oBAAK,WAAU;8BACd,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;8BAGtC,6LAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;IAGb;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAK/B,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;gCAAkB,WAAU;0CAC3B,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAmC,KAAK,IAAI;;;;;;8EAC1D,6LAAC;oEAAK,WAAW,CAAC,2CAA2C,EAC3D,KAAK,SAAS,GACV,gCACA,2BACJ;8EACC,KAAK,SAAS,GAAG,YAAY;;;;;;;;;;;;;;;;;;8DAKpC,6LAAC;oDACC,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,SAAS;oDACvD,WAAW,CAAC,iCAAiC,EAC3C,KAAK,SAAS,GACV,iCACA,oCACJ;oDACF,OAAO,KAAK,SAAS,GAAG,mBAAmB;8DAE1C,KAAK,SAAS,iBAAG,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAAe,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAItE,6LAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;sDAGnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DAAK,WAAU;;gEAAW,KAAK,YAAY;gEAAC;;;;;;;;;;;;;8DAE/C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAK,WAAU;;gEAAW,KAAK,cAAc;gEAAC;;;;;;;;;;;;;;;;;;;sDAInD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,mBAAmB,KAAK,EAAE;oDACzC,WAAU;;sEAEV,6LAAC,8MAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGnC,6LAAC;oDACC,SAAS,IAAM,mBAAmB,KAAK,EAAE;oDACzC,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BA5DpB,KAAK,EAAE;;;;;;;;;;oBAoEpB,MAAM,MAAM,KAAK,mBAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAKnC,6LAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GA5OwB;;QAGP,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}