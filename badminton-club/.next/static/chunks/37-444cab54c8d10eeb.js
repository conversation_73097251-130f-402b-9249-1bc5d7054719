"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[37],{2099:(e,s,t)=>{t.d(s,{ND:()=>c});var l=t(5647);let r="https://whdfkjsmyolbzlwtaoix.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndoZGZranNteW9sYnpsd3Rhb2l4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzNDUxODksImV4cCI6MjA2ODkyMTE4OX0.wodggEz_ElgvYVWPA4o3gmAg84AWezDmnRaHAkC2Dps",i=r&&a&&r.includes("supabase.co")&&a.length>100;console.log("Supabase Configuration:",{url:r?"".concat(r.substring(0,30),"..."):"NOT SET",keyLength:a.length,isConfigured:i});let c=(0,l.UU)(r||"https://placeholder.supabase.co",a||"placeholder-key")},4615:(e,s,t)=>{t.d(s,{A:()=>m});var l=t(5155),r=t(2115),a=t(5695),i=t(6874),c=t.n(i),n=t(9420),o=t(4516),x=t(4416),d=t(4783);function m(){let[e,s]=(0,r.useState)(!1),t=(0,a.useRouter)(),i=(0,a.usePathname)(),m=e=>{if(e.startsWith("#"))if("/"!==i)t.push("/"+e);else{let s=document.querySelector(e);s&&s.scrollIntoView({behavior:"smooth"})}else t.push(e);s(!1)};return(0,l.jsx)("header",{className:"bg-white shadow-lg sticky top-0 z-50",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,l.jsxs)(c(),{href:"/",className:"flex items-center",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-altius-blue",children:"\uD83C\uDFF8 Altius"}),(0,l.jsx)("div",{className:"ml-2 text-sm text-gray-600 hidden sm:block",children:"Кишинев"})]}),(0,l.jsxs)("nav",{className:"hidden md:flex space-x-8",children:[(0,l.jsx)("button",{onClick:()=>m("/"),className:"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer",children:"Главная"}),(0,l.jsx)("button",{onClick:()=>m("/about"),className:"text-gray-700 hover:text-altius-lime transition-colors cursor-pointer",children:"О нас"}),(0,l.jsx)("button",{onClick:()=>m("#halls"),className:"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer",children:"Залы"}),(0,l.jsx)("button",{onClick:()=>m("/services"),className:"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer",children:"Услуги"}),(0,l.jsx)("button",{onClick:()=>m("/contact"),className:"text-gray-700 hover:text-altius-lime transition-colors cursor-pointer",children:"Контакты"}),(0,l.jsx)("button",{onClick:()=>m("/blog"),className:"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer",children:"Блог"}),(0,l.jsx)(c(),{href:"/admin",className:"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer",children:"Админ"})]}),(0,l.jsxs)("div",{className:"hidden lg:flex items-center space-x-4",children:[(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,l.jsx)(n.A,{className:"w-4 h-4 mr-1"}),"+373 XX XXX XXX"]}),(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,l.jsx)(o.A,{className:"w-4 h-4 mr-1"}),"Кишинев"]})]}),(0,l.jsx)("button",{className:"md:hidden cursor-pointer",onClick:()=>s(!e),children:e?(0,l.jsx)(x.A,{className:"w-6 h-6 text-gray-700"}):(0,l.jsx)(d.A,{className:"w-6 h-6 text-gray-700"})})]}),e&&(0,l.jsx)("div",{className:"md:hidden py-4 border-t border-gray-200",children:(0,l.jsxs)("nav",{className:"flex flex-col space-y-4",children:[(0,l.jsx)("button",{onClick:()=>m("/"),className:"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer",children:"Главная"}),(0,l.jsx)("button",{onClick:()=>m("/about"),className:"text-gray-700 hover:text-altius-lime transition-colors text-left cursor-pointer",children:"О нас"}),(0,l.jsx)("button",{onClick:()=>m("#halls"),className:"text-gray-700 hover:text-altius-orange transition-colors text-left cursor-pointer",children:"Залы"}),(0,l.jsx)("button",{onClick:()=>m("/services"),className:"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer",children:"Услуги"}),(0,l.jsx)("button",{onClick:()=>m("/contact"),className:"text-gray-700 hover:text-altius-lime transition-colors text-left cursor-pointer",children:"Контакты"}),(0,l.jsx)("button",{onClick:()=>m("/blog"),className:"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer",children:"Блог"}),(0,l.jsx)(c(),{href:"/admin",className:"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer",children:"Админ"}),(0,l.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mb-2",children:[(0,l.jsx)(n.A,{className:"w-4 h-4 mr-1"}),"+373 XX XXX XXX"]}),(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,l.jsx)(o.A,{className:"w-4 h-4 mr-1"}),"Кишинев"]})]})]})})]})})}},6821:(e,s,t)=>{t.d(s,{A:()=>n});var l=t(5155),r=t(4516),a=t(9420),i=t(8883),c=t(4186);function n(){return(0,l.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-altius-lime mb-4",children:"\uD83C\uDFF8 Altius"}),(0,l.jsx)("p",{className:"text-gray-300 mb-4",children:"Современный бадминтонный клуб в Кишиневе с профессиональными кортами и удобной системой бронирования."}),(0,l.jsxs)("div",{className:"flex items-center text-gray-300 mb-2",children:[(0,l.jsx)(r.A,{className:"w-4 h-4 mr-2"}),"Кишинев, Молдова"]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Контакты"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,l.jsx)(a.A,{className:"w-4 h-4 mr-2"}),"+373 XX XXX XXX"]}),(0,l.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,l.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"<EMAIL>"]}),(0,l.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,l.jsx)(r.A,{className:"w-4 h-4 mr-2"}),"ул. Примерная, 123, Кишинев"]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Режим работы"}),(0,l.jsx)("div",{className:"space-y-2",children:(0,l.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,l.jsx)(c.A,{className:"w-4 h-4 mr-2"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{children:"Пн-Пт: 06:00 - 23:00"}),(0,l.jsx)("div",{children:"Сб-Вс: 08:00 - 22:00"})]})]})}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"Наши залы:"}),(0,l.jsxs)("div",{className:"text-sm text-gray-300 space-y-1",children:[(0,l.jsx)("div",{children:"Зал 1: 3 корта"}),(0,l.jsx)("div",{children:"Зал 2: 7 кортов"}),(0,l.jsx)("div",{children:"Зал 3: 7 кортов"})]})]})]})]}),(0,l.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:(0,l.jsx)("p",{children:"\xa9 2024 Altius Кишинев. Все права защищены."})})]})})}}}]);