(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{374:(e,s,t)=>{Promise.resolve().then(t.bind(t,8805))},646:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});let l=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4615:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var l=t(5155),a=t(2115),r=t(5695),i=t(6874),c=t.n(i),x=t(9420),n=t(4516),d=t(4416),m=t(4783);function o(){let[e,s]=(0,a.useState)(!1),t=(0,r.useRouter)(),i=(0,r.usePathname)(),o=e=>{if(e.startsWith("#"))if("/"!==i)t.push("/"+e);else{let s=document.querySelector(e);s&&s.scrollIntoView({behavior:"smooth"})}else t.push(e);s(!1)};return(0,l.jsx)("header",{className:"bg-white shadow-lg sticky top-0 z-50",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,l.jsxs)(c(),{href:"/",className:"flex items-center",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-altius-blue",children:"\uD83C\uDFF8 Altius"}),(0,l.jsx)("div",{className:"ml-2 text-sm text-gray-600 hidden sm:block",children:"Кишинев"})]}),(0,l.jsxs)("nav",{className:"hidden md:flex space-x-8",children:[(0,l.jsx)("button",{onClick:()=>o("/"),className:"text-gray-700 hover:text-altius-blue transition-colors",children:"Главная"}),(0,l.jsx)("button",{onClick:()=>o("/about"),className:"text-gray-700 hover:text-altius-lime transition-colors",children:"О нас"}),(0,l.jsx)("button",{onClick:()=>o("#halls"),className:"text-gray-700 hover:text-altius-orange transition-colors",children:"Залы"}),(0,l.jsx)("button",{onClick:()=>o("/services"),className:"text-gray-700 hover:text-altius-blue transition-colors",children:"Услуги"}),(0,l.jsx)("button",{onClick:()=>o("/contact"),className:"text-gray-700 hover:text-altius-lime transition-colors",children:"Контакты"}),(0,l.jsx)(c(),{href:"/admin",className:"text-gray-700 hover:text-altius-orange transition-colors",children:"Админ"})]}),(0,l.jsxs)("div",{className:"hidden lg:flex items-center space-x-4",children:[(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,l.jsx)(x.A,{className:"w-4 h-4 mr-1"}),"+373 XX XXX XXX"]}),(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,l.jsx)(n.A,{className:"w-4 h-4 mr-1"}),"Кишинев"]})]}),(0,l.jsx)("button",{className:"md:hidden",onClick:()=>s(!e),children:e?(0,l.jsx)(d.A,{className:"w-6 h-6 text-gray-700"}):(0,l.jsx)(m.A,{className:"w-6 h-6 text-gray-700"})})]}),e&&(0,l.jsx)("div",{className:"md:hidden py-4 border-t border-gray-200",children:(0,l.jsxs)("nav",{className:"flex flex-col space-y-4",children:[(0,l.jsx)("button",{onClick:()=>o("/"),className:"text-gray-700 hover:text-altius-blue transition-colors text-left",children:"Главная"}),(0,l.jsx)("button",{onClick:()=>o("/about"),className:"text-gray-700 hover:text-altius-lime transition-colors text-left",children:"О нас"}),(0,l.jsx)("button",{onClick:()=>o("#halls"),className:"text-gray-700 hover:text-altius-orange transition-colors text-left",children:"Залы"}),(0,l.jsx)("button",{onClick:()=>o("/services"),className:"text-gray-700 hover:text-altius-blue transition-colors text-left",children:"Услуги"}),(0,l.jsx)("button",{onClick:()=>o("/contact"),className:"text-gray-700 hover:text-altius-lime transition-colors text-left",children:"Контакты"}),(0,l.jsx)(c(),{href:"/admin",className:"text-gray-700 hover:text-altius-orange transition-colors",children:"Админ"}),(0,l.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mb-2",children:[(0,l.jsx)(x.A,{className:"w-4 h-4 mr-1"}),"+373 XX XXX XXX"]}),(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,l.jsx)(n.A,{className:"w-4 h-4 mr-1"}),"Кишинев"]})]})]})})]})})}},6821:(e,s,t)=>{"use strict";t.d(s,{A:()=>x});var l=t(5155),a=t(4516),r=t(9420),i=t(8883),c=t(4186);function x(){return(0,l.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-altius-lime mb-4",children:"\uD83C\uDFF8 Altius"}),(0,l.jsx)("p",{className:"text-gray-300 mb-4",children:"Современный бадминтонный клуб в Кишиневе с профессиональными кортами и удобной системой бронирования."}),(0,l.jsxs)("div",{className:"flex items-center text-gray-300 mb-2",children:[(0,l.jsx)(a.A,{className:"w-4 h-4 mr-2"}),"Кишинев, Молдова"]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Контакты"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,l.jsx)(r.A,{className:"w-4 h-4 mr-2"}),"+373 XX XXX XXX"]}),(0,l.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,l.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"<EMAIL>"]}),(0,l.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,l.jsx)(a.A,{className:"w-4 h-4 mr-2"}),"ул. Примерная, 123, Кишинев"]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Режим работы"}),(0,l.jsx)("div",{className:"space-y-2",children:(0,l.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,l.jsx)(c.A,{className:"w-4 h-4 mr-2"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{children:"Пн-Пт: 06:00 - 23:00"}),(0,l.jsx)("div",{children:"Сб-Вс: 08:00 - 22:00"})]})]})}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"Наши залы:"}),(0,l.jsxs)("div",{className:"text-sm text-gray-300 space-y-1",children:[(0,l.jsx)("div",{children:"Зал 1: 3 корта"}),(0,l.jsx)("div",{children:"Зал 2: 7 кортов"}),(0,l.jsx)("div",{children:"Зал 3: 7 кортов"})]})]})]})]}),(0,l.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:(0,l.jsx)("p",{children:"\xa9 2024 Altius Кишинев. Все права защищены."})})]})})}},8805:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var l=t(5155),a=t(2115),r=t(2177),i=t(221),c=t(8309),x=t(4615),n=t(6821),d=t(4516),m=t(9420),o=t(8883),h=t(4186),u=t(9946);let b=(0,u.A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var j=t(646);let g=(0,u.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),N=(0,u.A)("navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]]);var p=t(9074);let y=c.Ik({name:c.Yj().min(2,"Имя должно содержать минимум 2 символа"),email:c.Yj().email("Введите корректный email"),phone:c.Yj().min(8,"Введите корректный номер телефона"),subject:c.Yj().min(5,"Тема должна содержать минимум 5 символов"),message:c.Yj().min(10,"Сообщение должно содержать минимум 10 символов")});function f(){let[e,s]=(0,a.useState)(!1),{register:t,handleSubmit:c,formState:{errors:u,isSubmitting:f},reset:v}=(0,r.mN)({resolver:(0,i.u)(y)}),w=async e=>{await new Promise(e=>setTimeout(e,1e3)),console.log("Contact form submitted:",e),s(!0),v(),setTimeout(()=>s(!1),3e3)},X=[{icon:d.A,title:"Адрес",details:["ул. Примерная, 123","Кишинев, Молдова","MD-2001"],action:"Построить маршрут"},{icon:m.A,title:"Телефон",details:["+373 XX XXX XXX","+373 YY YYY YYY"],action:"Позвонить"},{icon:o.A,title:"Email",details:["<EMAIL>","<EMAIL>"],action:"Написать"},{icon:h.A,title:"Режим работы",details:["Пн-Пт: 06:00 - 23:00","Сб-Вс: 08:00 - 22:00"],action:"Забронировать"}];return(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,l.jsx)(x.A,{}),(0,l.jsx)("section",{className:"bg-gradient-to-br from-blue-600 to-blue-800 text-white",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:"Контакты"}),(0,l.jsx)("p",{className:"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto",children:"Свяжитесь с нами любым удобным способом. Мы всегда готовы ответить на ваши вопросы!"})]})})}),(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,l.jsx)("section",{className:"mb-16",children:(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:X.map((e,s)=>(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 text-center",children:[(0,l.jsx)(e.icon,{className:"w-10 h-10 text-blue-600 mx-auto mb-4"}),(0,l.jsx)("h3",{className:"text-lg font-semibold mb-3",children:e.title}),(0,l.jsx)("div",{className:"space-y-1 mb-4",children:e.details.map((e,s)=>(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:e},s))}),(0,l.jsx)("button",{className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:e.action})]},s))})}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[(0,l.jsxs)("div",{className:"flex items-center mb-6",children:[(0,l.jsx)(b,{className:"w-6 h-6 text-blue-600 mr-3"}),(0,l.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Напишите нам"})]}),e?(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)(j.A,{className:"w-16 h-16 text-green-500 mx-auto mb-4"}),(0,l.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Сообщение отправлено!"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Мы получили ваше сообщение и свяжемся с вами в ближайшее время."})]}):(0,l.jsxs)("form",{onSubmit:c(w),className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Имя *"}),(0,l.jsx)("input",{...t("name"),type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Ваше имя"}),u.name&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.name.message})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email *"}),(0,l.jsx)("input",{...t("email"),type:"email",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"<EMAIL>"}),u.email&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.email.message})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Телефон *"}),(0,l.jsx)("input",{...t("phone"),type:"tel",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"+373 XX XXX XXX"}),u.phone&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.phone.message})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Тема *"}),(0,l.jsx)("input",{...t("subject"),type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Тема сообщения"}),u.subject&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.subject.message})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Сообщение *"}),(0,l.jsx)("textarea",{...t("message"),rows:5,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Ваше сообщение..."}),u.message&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u.message.message})]}),(0,l.jsx)("button",{type:"submit",disabled:f,className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors disabled:opacity-50 flex items-center justify-center",children:f?"Отправка...":(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(g,{className:"w-4 h-4 mr-2"}),"Отправить сообщение"]})})]})]}),(0,l.jsxs)("div",{className:"space-y-8",children:[(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden",children:[(0,l.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(N,{className:"w-6 h-6 text-blue-600 mr-3"}),(0,l.jsx)("h3",{className:"text-xl font-semibold",children:"Как нас найти"})]})}),(0,l.jsx)("div",{className:"h-64 bg-gray-200 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center text-gray-500",children:[(0,l.jsx)(d.A,{className:"w-12 h-12 mx-auto mb-2"}),(0,l.jsx)("div",{className:"font-medium",children:"Интерактивная карта"}),(0,l.jsx)("div",{className:"text-sm",children:"ул. Примерная, 123, Кишинев"})]})}),(0,l.jsx)("div",{className:"p-4",children:(0,l.jsx)("button",{className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors",children:"Открыть в Google Maps"})})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,l.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Быстрые действия"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("button",{onClick:()=>window.location.href="/#halls",className:"w-full bg-blue-100 hover:bg-blue-200 text-blue-700 py-3 px-4 rounded-lg transition-colors flex items-center",children:[(0,l.jsx)(p.A,{className:"w-5 h-5 mr-3"}),"Забронировать корт"]}),(0,l.jsxs)("button",{className:"w-full bg-green-100 hover:bg-green-200 text-green-700 py-3 px-4 rounded-lg transition-colors flex items-center",children:[(0,l.jsx)(m.A,{className:"w-5 h-5 mr-3"}),"Позвонить сейчас"]}),(0,l.jsxs)("button",{className:"w-full bg-purple-100 hover:bg-purple-200 text-purple-700 py-3 px-4 rounded-lg transition-colors flex items-center",children:[(0,l.jsx)(o.A,{className:"w-5 h-5 mr-3"}),"Написать email"]})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,l.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Часто задаваемые вопросы"}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:"Можно ли забронировать корт по телефону?"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Да, вы можете забронировать корт по телефону или через наш сайт."})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:"Есть ли парковка?"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Да, у нас есть бесплатная охраняемая парковка для всех посетителей."})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:"Можно ли арендовать инвентарь?"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Да, мы предоставляем в аренду ракетки, воланы и спортивную форму."})]})]})]})]})]})]}),(0,l.jsx)(n.A,{})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[178,832,441,684,358],()=>s(374)),_N_E=e.O()}]);