(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[220],{1325:(e,s,t)=>{Promise.resolve().then(t.bind(t,9219))},4416:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});let l=(0,t(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4516:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});let l=(0,t(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4615:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var l=t(5155),a=t(2115),r=t(5695),i=t(6874),c=t.n(i),x=t(9420),n=t(4516),d=t(4416),o=t(4783);function m(){let[e,s]=(0,a.useState)(!1),t=(0,r.useRouter)(),i=(0,r.usePathname)(),m=e=>{if(e.startsWith("#"))if("/"!==i)t.push("/"+e);else{let s=document.querySelector(e);s&&s.scrollIntoView({behavior:"smooth"})}else t.push(e);s(!1)};return(0,l.jsx)("header",{className:"bg-white shadow-lg sticky top-0 z-50",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,l.jsxs)(c(),{href:"/",className:"flex items-center",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-altius-blue",children:"\uD83C\uDFF8 Altius"}),(0,l.jsx)("div",{className:"ml-2 text-sm text-gray-600 hidden sm:block",children:"Кишинев"})]}),(0,l.jsxs)("nav",{className:"hidden md:flex space-x-8",children:[(0,l.jsx)("button",{onClick:()=>m("/"),className:"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer",children:"Главная"}),(0,l.jsx)("button",{onClick:()=>m("/about"),className:"text-gray-700 hover:text-altius-lime transition-colors cursor-pointer",children:"О нас"}),(0,l.jsx)("button",{onClick:()=>m("#halls"),className:"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer",children:"Залы"}),(0,l.jsx)("button",{onClick:()=>m("/services"),className:"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer",children:"Услуги"}),(0,l.jsx)("button",{onClick:()=>m("/contact"),className:"text-gray-700 hover:text-altius-lime transition-colors cursor-pointer",children:"Контакты"}),(0,l.jsx)("button",{onClick:()=>m("/blog"),className:"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer",children:"Блог"}),(0,l.jsx)(c(),{href:"/admin",className:"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer",children:"Админ"})]}),(0,l.jsxs)("div",{className:"hidden lg:flex items-center space-x-4",children:[(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,l.jsx)(x.A,{className:"w-4 h-4 mr-1"}),"+373 XX XXX XXX"]}),(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,l.jsx)(n.A,{className:"w-4 h-4 mr-1"}),"Кишинев"]})]}),(0,l.jsx)("button",{className:"md:hidden cursor-pointer",onClick:()=>s(!e),children:e?(0,l.jsx)(d.A,{className:"w-6 h-6 text-gray-700"}):(0,l.jsx)(o.A,{className:"w-6 h-6 text-gray-700"})})]}),e&&(0,l.jsx)("div",{className:"md:hidden py-4 border-t border-gray-200",children:(0,l.jsxs)("nav",{className:"flex flex-col space-y-4",children:[(0,l.jsx)("button",{onClick:()=>m("/"),className:"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer",children:"Главная"}),(0,l.jsx)("button",{onClick:()=>m("/about"),className:"text-gray-700 hover:text-altius-lime transition-colors text-left cursor-pointer",children:"О нас"}),(0,l.jsx)("button",{onClick:()=>m("#halls"),className:"text-gray-700 hover:text-altius-orange transition-colors text-left cursor-pointer",children:"Залы"}),(0,l.jsx)("button",{onClick:()=>m("/services"),className:"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer",children:"Услуги"}),(0,l.jsx)("button",{onClick:()=>m("/contact"),className:"text-gray-700 hover:text-altius-lime transition-colors text-left cursor-pointer",children:"Контакты"}),(0,l.jsx)("button",{onClick:()=>m("/blog"),className:"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer",children:"Блог"}),(0,l.jsx)(c(),{href:"/admin",className:"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer",children:"Админ"}),(0,l.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mb-2",children:[(0,l.jsx)(x.A,{className:"w-4 h-4 mr-1"}),"+373 XX XXX XXX"]}),(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,l.jsx)(n.A,{className:"w-4 h-4 mr-1"}),"Кишинев"]})]})]})})]})})}},4783:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});let l=(0,t(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},5695:(e,s,t)=>{"use strict";var l=t(8999);t.o(l,"useParams")&&t.d(s,{useParams:function(){return l.useParams}}),t.o(l,"usePathname")&&t.d(s,{usePathname:function(){return l.usePathname}}),t.o(l,"useRouter")&&t.d(s,{useRouter:function(){return l.useRouter}})},6821:(e,s,t)=>{"use strict";t.d(s,{A:()=>x});var l=t(5155),a=t(4516),r=t(9420),i=t(8883),c=t(4186);function x(){return(0,l.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-altius-lime mb-4",children:"\uD83C\uDFF8 Altius"}),(0,l.jsx)("p",{className:"text-gray-300 mb-4",children:"Современный бадминтонный клуб в Кишиневе с профессиональными кортами и удобной системой бронирования."}),(0,l.jsxs)("div",{className:"flex items-center text-gray-300 mb-2",children:[(0,l.jsx)(a.A,{className:"w-4 h-4 mr-2"}),"Кишинев, Молдова"]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Контакты"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,l.jsx)(r.A,{className:"w-4 h-4 mr-2"}),"+373 XX XXX XXX"]}),(0,l.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,l.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"<EMAIL>"]}),(0,l.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,l.jsx)(a.A,{className:"w-4 h-4 mr-2"}),"ул. Примерная, 123, Кишинев"]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Режим работы"}),(0,l.jsx)("div",{className:"space-y-2",children:(0,l.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,l.jsx)(c.A,{className:"w-4 h-4 mr-2"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{children:"Пн-Пт: 06:00 - 23:00"}),(0,l.jsx)("div",{children:"Сб-Вс: 08:00 - 22:00"})]})]})}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"Наши залы:"}),(0,l.jsxs)("div",{className:"text-sm text-gray-300 space-y-1",children:[(0,l.jsx)("div",{children:"Зал 1: 3 корта"}),(0,l.jsx)("div",{children:"Зал 2: 7 кортов"}),(0,l.jsx)("div",{children:"Зал 3: 7 кортов"})]})]})]})]}),(0,l.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:(0,l.jsx)("p",{children:"\xa9 2024 Altius Кишинев. Все права защищены."})})]})})}},7580:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});let l=(0,t(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},8564:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});let l=(0,t(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9219:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var l=t(5155),a=t(4615),r=t(6821),i=t(9946);let c=(0,i.A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);var x=t(7580),n=t(4186);let d=(0,i.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),o=(0,i.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var m=t(8564);function h(){let e=[{icon:c,title:"500+ довольных клиентов",description:"За 5 лет работы мы обслужили более 500 постоянных клиентов"},{icon:x.A,title:"17 профессиональных кортов",description:"3 современных зала с кортами международного стандарта"},{icon:n.A,title:"16 часов работы в день",description:"Работаем с 6:00 до 23:00 для вашего удобства"},{icon:d,title:"50+ турниров проведено",description:"Организуем турниры различного уровня для всех возрастов"}];return(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,l.jsx)(a.A,{}),(0,l.jsx)("section",{className:"bg-gradient-to-br from-blue-600 to-blue-800 text-white",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:"О нашем клубе"}),(0,l.jsx)("p",{className:"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto",children:"BadmintonClub Кишинев - ведущий бадминтонный клуб Молдовы, где профессионализм встречается с комфортом"})]})})}),(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,l.jsx)("section",{className:"mb-16",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Наша история"}),(0,l.jsxs)("div",{className:"space-y-4 text-gray-600",children:[(0,l.jsx)("p",{children:"BadmintonClub Кишинев был основан в 2019 году группой энтузиастов бадминтона, которые мечтали создать современный спортивный центр европейского уровня в столице Молдовы."}),(0,l.jsx)("p",{children:"За пять лет работы мы выросли от небольшого зала с тремя кортами до крупнейшего бадминтонного комплекса в стране с 17 профессиональными кортами в трех залах."}),(0,l.jsx)("p",{children:"Сегодня наш клуб является домашней ареной для сборной команды Молдовы по бадминтону и местом проведения международных турниров."})]})]}),(0,l.jsx)("div",{className:"relative",children:(0,l.jsx)("div",{className:"bg-gradient-to-br from-blue-500 to-blue-700 rounded-xl p-8 text-white",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"text-5xl mb-4",children:"\uD83C\uDFF8"}),(0,l.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"5 лет успеха"}),(0,l.jsx)("p",{className:"text-blue-100",children:"Мы гордимся тем, что стали частью спортивной жизни Кишинева и помогли сотням людей полюбить бадминтон"})]})})})]})}),(0,l.jsxs)("section",{className:"mb-16",children:[(0,l.jsxs)("div",{className:"text-center mb-12",children:[(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Наша миссия и ценности"}),(0,l.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Мы стремимся популяризировать бадминтон в Молдове и создавать условия для развития спорта на всех уровнях"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 text-center",children:[(0,l.jsx)(o,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,l.jsx)("h3",{className:"text-xl font-semibold mb-3",children:"Любовь к спорту"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Мы искренне любим бадминтон и хотим поделиться этой страстью с каждым посетителем"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 text-center",children:[(0,l.jsx)(m.A,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,l.jsx)("h3",{className:"text-xl font-semibold mb-3",children:"Качество"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Только лучшее оборудование, покрытия и условия для комфортной игры"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 text-center",children:[(0,l.jsx)(x.A,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,l.jsx)("h3",{className:"text-xl font-semibold mb-3",children:"Сообщество"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Мы создаем дружественную атмосферу, где каждый чувствует себя частью большой семьи"})]})]})]}),(0,l.jsxs)("section",{className:"mb-16",children:[(0,l.jsxs)("div",{className:"text-center mb-12",children:[(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Наши достижения"}),(0,l.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"За годы работы мы достигли значительных результатов и продолжаем развиваться"})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:e.map((e,s)=>(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 text-center",children:[(0,l.jsx)(e.icon,{className:"w-10 h-10 text-blue-600 mx-auto mb-4"}),(0,l.jsx)("h3",{className:"text-lg font-semibold mb-2",children:e.title}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]},s))})]}),(0,l.jsxs)("section",{className:"mb-16",children:[(0,l.jsxs)("div",{className:"text-center mb-12",children:[(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Наша команда"}),(0,l.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Профессиональные тренеры и администраторы, которые сделают ваше пребывание в клубе максимально комфортным"})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[{name:"Александр Петров",position:"Директор клуба",experience:"15 лет в бадминтоне",description:"Мастер спорта по бадминтону, основатель клуба",image:"/api/placeholder/300/300?text=Александр+Петров"},{name:"Мария Иванова",position:"Главный тренер",experience:"12 лет тренерского стажа",description:"Кандидат в мастера спорта, специалист по работе с детьми",image:"/api/placeholder/300/300?text=Мария+Иванова"},{name:"Дмитрий Сидоров",position:"Тренер",experience:"8 лет в спорте",description:"Специалист по технической подготовке игроков",image:"/api/placeholder/300/300?text=Дмитрий+Сидоров"}].map((e,s)=>(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden",children:[(0,l.jsx)("div",{className:"aspect-square bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center text-white",children:[(0,l.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDC64"}),(0,l.jsx)("div",{className:"text-xl font-semibold",children:e.name})]})}),(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsx)("h3",{className:"text-xl font-semibold mb-2",children:e.name}),(0,l.jsx)("p",{className:"text-blue-600 font-medium mb-2",children:e.position}),(0,l.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:e.experience}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]})]},s))})]}),(0,l.jsxs)("section",{className:"bg-blue-600 rounded-xl text-white p-8 text-center",children:[(0,l.jsx)("h2",{className:"text-3xl font-bold mb-4",children:"Присоединяйтесь к нам!"}),(0,l.jsx)("p",{className:"text-blue-100 mb-6 max-w-2xl mx-auto",children:"Станьте частью нашего спортивного сообщества. Забронируйте корт и почувствуйте разницу игры в профессиональных условиях."}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,l.jsx)("button",{onClick:()=>window.location.href="/#halls",className:"bg-white text-blue-600 font-semibold py-3 px-8 rounded-lg hover:bg-blue-50 transition-colors",children:"Забронировать корт"}),(0,l.jsx)("button",{onClick:()=>window.location.href="/contact",className:"border-2 border-white text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:text-blue-600 transition-colors",children:"Связаться с нами"})]})]})]}),(0,l.jsx)(r.A,{})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[308,441,684,120],()=>s(1325)),_N_E=e.O()}]);