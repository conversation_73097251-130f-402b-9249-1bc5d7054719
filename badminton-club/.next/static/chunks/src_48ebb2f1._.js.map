{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { Menu, X, Phone, MapPin } from 'lucide-react';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleNavigation = (href: string) => {\n    if (href.startsWith('#')) {\n      // Якорная ссылка - переходим на главную страницу если не на ней\n      if (pathname !== '/') {\n        router.push('/' + href);\n      } else {\n        // Если уже на главной странице, просто скроллим\n        const element = document.querySelector(href);\n        if (element) {\n          element.scrollIntoView({ behavior: 'smooth' });\n        }\n      }\n    } else {\n      // Обычная ссылка\n      router.push(href);\n    }\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <div className=\"text-2xl font-bold text-blue-600\">\n              🏸 BadmintonClub\n            </div>\n            <div className=\"ml-2 text-sm text-gray-600 hidden sm:block\">\n              Кишинев\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <button\n              onClick={() => handleNavigation('/')}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Главная\n            </button>\n            <button\n              onClick={() => handleNavigation('/about')}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              О нас\n            </button>\n            <button\n              onClick={() => handleNavigation('#halls')}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Залы\n            </button>\n            <button\n              onClick={() => handleNavigation('/services')}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Услуги\n            </button>\n            <button\n              onClick={() => handleNavigation('/contact')}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Контакты\n            </button>\n            <Link href=\"/admin\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Админ\n            </Link>\n          </nav>\n\n          {/* Contact Info */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Phone className=\"w-4 h-4 mr-1\" />\n              +373 XX XXX XXX\n            </div>\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <MapPin className=\"w-4 h-4 mr-1\" />\n              Кишинев\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {isMenuOpen ? (\n              <X className=\"w-6 h-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"w-6 h-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <button\n                onClick={() => handleNavigation('/')}\n                className=\"text-gray-700 hover:text-blue-600 transition-colors text-left\"\n              >\n                Главная\n              </button>\n              <button\n                onClick={() => handleNavigation('/about')}\n                className=\"text-gray-700 hover:text-blue-600 transition-colors text-left\"\n              >\n                О нас\n              </button>\n              <button\n                onClick={() => handleNavigation('#halls')}\n                className=\"text-gray-700 hover:text-blue-600 transition-colors text-left\"\n              >\n                Залы\n              </button>\n              <button\n                onClick={() => handleNavigation('/services')}\n                className=\"text-gray-700 hover:text-blue-600 transition-colors text-left\"\n              >\n                Услуги\n              </button>\n              <button\n                onClick={() => handleNavigation('/contact')}\n                className=\"text-gray-700 hover:text-blue-600 transition-colors text-left\"\n              >\n                Контакты\n              </button>\n              <Link href=\"/admin\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Админ\n              </Link>\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center text-sm text-gray-600 mb-2\">\n                  <Phone className=\"w-4 h-4 mr-1\" />\n                  +373 XX XXX XXX\n                </div>\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <MapPin className=\"w-4 h-4 mr-1\" />\n                  Кишинев\n                </div>\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,mBAAmB,CAAC;QACxB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,gEAAgE;YAChE,IAAI,aAAa,KAAK;gBACpB,OAAO,IAAI,CAAC,MAAM;YACpB,OAAO;gBACL,gDAAgD;gBAChD,MAAM,UAAU,SAAS,aAAa,CAAC;gBACvC,IAAI,SAAS;oBACX,QAAQ,cAAc,CAAC;wBAAE,UAAU;oBAAS;gBAC9C;YACF;QACF,OAAO;YACL,iBAAiB;YACjB,OAAO,IAAI,CAAC;QACd;QACA,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CAAmC;;;;;;8CAGlD,6LAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAM9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAsD;;;;;;;;;;;;sCAMtF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE7B,2BACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAAsD;;;;;;0CAGpF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GAzJwB;;QAEP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHN", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Footer.tsx"], "sourcesContent": ["import { Phone, MapPin, Clock, Mail } from 'lucide-react';\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {/* Company Info */}\n          <div>\n            <div className=\"text-2xl font-bold text-blue-400 mb-4\">\n              🏸 BadmintonClub\n            </div>\n            <p className=\"text-gray-300 mb-4\">\n              Современный бадминтонный клуб в Кишиневе с профессиональными кортами \n              и удобной системой бронирования.\n            </p>\n            <div className=\"flex items-center text-gray-300 mb-2\">\n              <MapPin className=\"w-4 h-4 mr-2\" />\n              Кишинев, Молдова\n            </div>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Контакты</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center text-gray-300\">\n                <Phone className=\"w-4 h-4 mr-2\" />\n                +373 XX XXX XXX\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <Mail className=\"w-4 h-4 mr-2\" />\n                <EMAIL>\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <MapPin className=\"w-4 h-4 mr-2\" />\n                ул. Примерная, 123, Кишинев\n              </div>\n            </div>\n          </div>\n\n          {/* Working Hours */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Режим работы</h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-gray-300\">\n                <Clock className=\"w-4 h-4 mr-2\" />\n                <div>\n                  <div>Пн-Пт: 06:00 - 23:00</div>\n                  <div>Сб-Вс: 08:00 - 22:00</div>\n                </div>\n              </div>\n            </div>\n            \n            {/* Halls Info */}\n            <div className=\"mt-6\">\n              <h4 className=\"font-semibold mb-2\">Наши залы:</h4>\n              <div className=\"text-sm text-gray-300 space-y-1\">\n                <div>Зал 1: 3 корта</div>\n                <div>Зал 2: 7 кортов</div>\n                <div>Зал 3: 7 кортов</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n          <p>&copy; 2024 BadmintonClub Кишинев. Все права защищены.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAwC;;;;;;8CAGvD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOzC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;;kEACC,6LAAC;kEAAI;;;;;;kEACL,6LAAC;kEAAI;;;;;;;;;;;;;;;;;;;;;;;8CAMX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb;KAtEwB", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/app/contact/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport { \n  MapPin, \n  Phone, \n  Mail, \n  Clock, \n  Send,\n  CheckCircle,\n  MessageSquare,\n  Navigation,\n  Calendar\n} from 'lucide-react';\n\nconst contactSchema = z.object({\n  name: z.string().min(2, 'Имя должно содержать минимум 2 символа'),\n  email: z.string().email('Введите корректный email'),\n  phone: z.string().min(8, 'Введите корректный номер телефона'),\n  subject: z.string().min(5, 'Тема должна содержать минимум 5 символов'),\n  message: z.string().min(10, 'Сообщение должно содержать минимум 10 символов'),\n});\n\ntype ContactFormData = z.infer<typeof contactSchema>;\n\nexport default function ContactPage() {\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  \n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting },\n    reset\n  } = useForm<ContactFormData>({\n    resolver: zodResolver(contactSchema)\n  });\n\n  const onSubmit = async (data: ContactFormData) => {\n    // Симуляция отправки формы\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    console.log('Contact form submitted:', data);\n    setIsSubmitted(true);\n    reset();\n    \n    // Сброс состояния через 3 секунды\n    setTimeout(() => setIsSubmitted(false), 3000);\n  };\n\n  const contactInfo = [\n    {\n      icon: MapPin,\n      title: 'Адрес',\n      details: ['ул. Примерная, 123', 'Кишинев, Молдова', 'MD-2001'],\n      action: 'Построить маршрут'\n    },\n    {\n      icon: Phone,\n      title: 'Телефон',\n      details: ['+373 XX XXX XXX', '+373 YY YYY YYY'],\n      action: 'Позвонить'\n    },\n    {\n      icon: Mail,\n      title: 'Email',\n      details: ['<EMAIL>', '<EMAIL>'],\n      action: 'Написать'\n    },\n    {\n      icon: Clock,\n      title: 'Режим работы',\n      details: ['Пн-Пт: 06:00 - 23:00', 'Сб-Вс: 08:00 - 22:00'],\n      action: 'Забронировать'\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-600 to-blue-800 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              Контакты\n            </h1>\n            <p className=\"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto\">\n              Свяжитесь с нами любым удобным способом. \n              Мы всегда готовы ответить на ваши вопросы!\n            </p>\n          </div>\n        </div>\n      </section>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        {/* Contact Info Cards */}\n        <section className=\"mb-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {contactInfo.map((info, index) => (\n              <div key={index} className=\"bg-white rounded-xl shadow-lg p-6 text-center\">\n                <info.icon className=\"w-10 h-10 text-blue-600 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold mb-3\">{info.title}</h3>\n                <div className=\"space-y-1 mb-4\">\n                  {info.details.map((detail, detailIndex) => (\n                    <p key={detailIndex} className=\"text-gray-600 text-sm\">\n                      {detail}\n                    </p>\n                  ))}\n                </div>\n                <button className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\">\n                  {info.action}\n                </button>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* Main Content */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n          {/* Contact Form */}\n          <div className=\"bg-white rounded-xl shadow-lg p-8\">\n            <div className=\"flex items-center mb-6\">\n              <MessageSquare className=\"w-6 h-6 text-blue-600 mr-3\" />\n              <h2 className=\"text-2xl font-bold text-gray-900\">Напишите нам</h2>\n            </div>\n            \n            {isSubmitted ? (\n              <div className=\"text-center py-8\">\n                <CheckCircle className=\"w-16 h-16 text-green-500 mx-auto mb-4\" />\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                  Сообщение отправлено!\n                </h3>\n                <p className=\"text-gray-600\">\n                  Мы получили ваше сообщение и свяжемся с вами в ближайшее время.\n                </p>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n                {/* Name */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Имя *\n                  </label>\n                  <input\n                    {...register('name')}\n                    type=\"text\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    placeholder=\"Ваше имя\"\n                  />\n                  {errors.name && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.name.message}</p>\n                  )}\n                </div>\n\n                {/* Email */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Email *\n                  </label>\n                  <input\n                    {...register('email')}\n                    type=\"email\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    placeholder=\"<EMAIL>\"\n                  />\n                  {errors.email && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n                  )}\n                </div>\n\n                {/* Phone */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Телефон *\n                  </label>\n                  <input\n                    {...register('phone')}\n                    type=\"tel\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    placeholder=\"+373 XX XXX XXX\"\n                  />\n                  {errors.phone && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.phone.message}</p>\n                  )}\n                </div>\n\n                {/* Subject */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Тема *\n                  </label>\n                  <input\n                    {...register('subject')}\n                    type=\"text\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    placeholder=\"Тема сообщения\"\n                  />\n                  {errors.subject && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.subject.message}</p>\n                  )}\n                </div>\n\n                {/* Message */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Сообщение *\n                  </label>\n                  <textarea\n                    {...register('message')}\n                    rows={5}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    placeholder=\"Ваше сообщение...\"\n                  />\n                  {errors.message && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.message.message}</p>\n                  )}\n                </div>\n\n                {/* Submit Button */}\n                <button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors disabled:opacity-50 flex items-center justify-center\"\n                >\n                  {isSubmitting ? (\n                    'Отправка...'\n                  ) : (\n                    <>\n                      <Send className=\"w-4 h-4 mr-2\" />\n                      Отправить сообщение\n                    </>\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n\n          {/* Map and Additional Info */}\n          <div className=\"space-y-8\">\n            {/* Map Placeholder */}\n            <div className=\"bg-white rounded-xl shadow-lg overflow-hidden\">\n              <div className=\"p-6 border-b border-gray-200\">\n                <div className=\"flex items-center\">\n                  <Navigation className=\"w-6 h-6 text-blue-600 mr-3\" />\n                  <h3 className=\"text-xl font-semibold\">Как нас найти</h3>\n                </div>\n              </div>\n              <div className=\"h-64 bg-gray-200 flex items-center justify-center\">\n                <div className=\"text-center text-gray-500\">\n                  <MapPin className=\"w-12 h-12 mx-auto mb-2\" />\n                  <div className=\"font-medium\">Интерактивная карта</div>\n                  <div className=\"text-sm\">ул. Примерная, 123, Кишинев</div>\n                </div>\n              </div>\n              <div className=\"p-4\">\n                <button className=\"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors\">\n                  Открыть в Google Maps\n                </button>\n              </div>\n            </div>\n\n            {/* Quick Actions */}\n            <div className=\"bg-white rounded-xl shadow-lg p-6\">\n              <h3 className=\"text-xl font-semibold mb-4\">Быстрые действия</h3>\n              <div className=\"space-y-3\">\n                <button \n                  onClick={() => window.location.href = '/#halls'}\n                  className=\"w-full bg-blue-100 hover:bg-blue-200 text-blue-700 py-3 px-4 rounded-lg transition-colors flex items-center\"\n                >\n                  <Calendar className=\"w-5 h-5 mr-3\" />\n                  Забронировать корт\n                </button>\n                <button className=\"w-full bg-green-100 hover:bg-green-200 text-green-700 py-3 px-4 rounded-lg transition-colors flex items-center\">\n                  <Phone className=\"w-5 h-5 mr-3\" />\n                  Позвонить сейчас\n                </button>\n                <button className=\"w-full bg-purple-100 hover:bg-purple-200 text-purple-700 py-3 px-4 rounded-lg transition-colors flex items-center\">\n                  <Mail className=\"w-5 h-5 mr-3\" />\n                  Написать email\n                </button>\n              </div>\n            </div>\n\n            {/* FAQ */}\n            <div className=\"bg-white rounded-xl shadow-lg p-6\">\n              <h3 className=\"text-xl font-semibold mb-4\">Часто задаваемые вопросы</h3>\n              <div className=\"space-y-4\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-1\">\n                    Можно ли забронировать корт по телефону?\n                  </h4>\n                  <p className=\"text-sm text-gray-600\">\n                    Да, вы можете забронировать корт по телефону или через наш сайт.\n                  </p>\n                </div>\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-1\">\n                    Есть ли парковка?\n                  </h4>\n                  <p className=\"text-sm text-gray-600\">\n                    Да, у нас есть бесплатная охраняемая парковка для всех посетителей.\n                  </p>\n                </div>\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-1\">\n                    Можно ли арендовать инвентарь?\n                  </h4>\n                  <p className=\"text-sm text-gray-600\">\n                    Да, мы предоставляем в аренду ракетки, воланы и спортивную форму.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAoBA,MAAM,gBAAgB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;AAC9B;AAIe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,QAAQ,GAAG,CAAC,2BAA2B;QACvC,eAAe;QACf;QAEA,kCAAkC;QAClC,WAAW,IAAM,eAAe,QAAQ;IAC1C;IAEA,MAAM,cAAc;QAClB;YACE,MAAM,6MAAA,CAAA,SAAM;YACZ,OAAO;YACP,SAAS;gBAAC;gBAAsB;gBAAoB;aAAU;YAC9D,QAAQ;QACV;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;gBAAC;gBAAmB;aAAkB;YAC/C,QAAQ;QACV;QACA;YACE,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,SAAS;gBAAC;gBAAyB;aAA2B;YAC9D,QAAQ;QACV;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;gBAAC;gBAAwB;aAAuB;YACzD,QAAQ;QACV;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;0BAGP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAE,WAAU;0CAAsD;;;;;;;;;;;;;;;;;;;;;;0BAQzE,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAG,WAAU;sDAA8B,KAAK,KAAK;;;;;;sDACtD,6LAAC;4CAAI,WAAU;sDACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACzB,6LAAC;oDAAoB,WAAU;8DAC5B;mDADK;;;;;;;;;;sDAKZ,6LAAC;4CAAO,WAAU;sDACf,KAAK,MAAM;;;;;;;mCAXN;;;;;;;;;;;;;;;kCAmBhB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;oCAGlD,4BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAGzD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;6DAK/B,6LAAC;wCAAK,UAAU,aAAa;wCAAW,WAAU;;0DAEhD,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACE,GAAG,SAAS,OAAO;wDACpB,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;oDAEb,OAAO,IAAI,kBACV,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;0DAKjE,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACE,GAAG,SAAS,QAAQ;wDACrB,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;oDAEb,OAAO,KAAK,kBACX,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0DAKlE,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACE,GAAG,SAAS,QAAQ;wDACrB,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;oDAEb,OAAO,KAAK,kBACX,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0DAKlE,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACE,GAAG,SAAS,UAAU;wDACvB,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;oDAEb,OAAO,OAAO,kBACb,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;0DAKpE,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACE,GAAG,SAAS,UAAU;wDACvB,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;oDAEb,OAAO,OAAO,kBACb,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;0DAKpE,6LAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,eACC,8BAEA;;sEACE,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;0CAU7C,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAG,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAG1C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,6LAAC;4DAAI,WAAU;sEAAU;;;;;;;;;;;;;;;;;0DAG7B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAO,WAAU;8DAAyF;;;;;;;;;;;;;;;;;kDAO/G,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wDACtC,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGvC,6LAAC;wDAAO,WAAU;;0EAChB,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGpC,6LAAC;wDAAO,WAAU;;0EAChB,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;kDAOvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAiC;;;;;;0EAG/C,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAIvC,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAiC;;;;;;0EAG/C,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAIvC,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAiC;;;;;;0EAG/C,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjD,6LAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GAvSwB;;QAQlB,iKAAA,CAAA,UAAO;;;KARW", "debugId": null}}]}