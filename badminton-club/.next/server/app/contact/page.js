(()=>{var e={};e.id=977,e.ids=[977],e.modules={98:(e,s,t)=>{Promise.resolve().then(t.bind(t,3839))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3839:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Altius/badminton-club/src/app/contact/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Altius/badminton-club/src/app/contact/page.tsx","default")},3873:e=>{"use strict";e.exports=require("path")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7001:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(687),l=t(3210),a=t(7605),i=t(3442),n=t(7566),o=t(1891),c=t(1317),d=t(7992),m=t(8340),x=t(3931),u=t(8730),p=t(2688);let h=(0,p.A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var b=t(5336);let g=(0,p.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),j=(0,p.A)("navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]]);var f=t(228);let y=n.Ik({name:n.Yj().min(2,"Имя должно содержать минимум 2 символа"),email:n.Yj().email("Введите корректный email"),phone:n.Yj().min(8,"Введите корректный номер телефона"),subject:n.Yj().min(5,"Тема должна содержать минимум 5 символов"),message:n.Yj().min(10,"Сообщение должно содержать минимум 10 символов")});function N(){let[e,s]=(0,l.useState)(!1),{register:t,handleSubmit:n,formState:{errors:p,isSubmitting:N},reset:v}=(0,a.mN)({resolver:(0,i.u)(y)}),w=async e=>{await new Promise(e=>setTimeout(e,1e3)),console.log("Contact form submitted:",e),s(!0),v(),setTimeout(()=>s(!1),3e3)},k=[{icon:d.A,title:"Адрес",details:["ул. Примерная, 123","Кишинев, Молдова","MD-2001"],action:"Построить маршрут"},{icon:m.A,title:"Телефон",details:["+373 XX XXX XXX","+373 YY YYY YYY"],action:"Позвонить"},{icon:x.A,title:"Email",details:["<EMAIL>","<EMAIL>"],action:"Написать"},{icon:u.A,title:"Режим работы",details:["Пн-Пт: 06:00 - 23:00","Сб-Вс: 08:00 - 22:00"],action:"Забронировать"}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(o.A,{}),(0,r.jsx)("section",{className:"bg-gradient-to-br from-blue-600 to-blue-800 text-white",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:"Контакты"}),(0,r.jsx)("p",{className:"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto",children:"Свяжитесь с нами любым удобным способом. Мы всегда готовы ответить на ваши вопросы!"})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,r.jsx)("section",{className:"mb-16",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:k.map((e,s)=>(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 text-center",children:[(0,r.jsx)(e.icon,{className:"w-10 h-10 text-blue-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-3",children:e.title}),(0,r.jsx)("div",{className:"space-y-1 mb-4",children:e.details.map((e,s)=>(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:e},s))}),(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:e.action})]},s))})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(h,{className:"w-6 h-6 text-blue-600 mr-3"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Напишите нам"})]}),e?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(b.A,{className:"w-16 h-16 text-green-500 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Сообщение отправлено!"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Мы получили ваше сообщение и свяжемся с вами в ближайшее время."})]}):(0,r.jsxs)("form",{onSubmit:n(w),className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Имя *"}),(0,r.jsx)("input",{...t("name"),type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Ваше имя"}),p.name&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.name.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email *"}),(0,r.jsx)("input",{...t("email"),type:"email",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"<EMAIL>"}),p.email&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.email.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Телефон *"}),(0,r.jsx)("input",{...t("phone"),type:"tel",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"+373 XX XXX XXX"}),p.phone&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.phone.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Тема *"}),(0,r.jsx)("input",{...t("subject"),type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Тема сообщения"}),p.subject&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.subject.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Сообщение *"}),(0,r.jsx)("textarea",{...t("message"),rows:5,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Ваше сообщение..."}),p.message&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.message.message})]}),(0,r.jsx)("button",{type:"submit",disabled:N,className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors disabled:opacity-50 flex items-center justify-center",children:N?"Отправка...":(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g,{className:"w-4 h-4 mr-2"}),"Отправить сообщение"]})})]})]}),(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(j,{className:"w-6 h-6 text-blue-600 mr-3"}),(0,r.jsx)("h3",{className:"text-xl font-semibold",children:"Как нас найти"})]})}),(0,r.jsx)("div",{className:"h-64 bg-gray-200 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center text-gray-500",children:[(0,r.jsx)(d.A,{className:"w-12 h-12 mx-auto mb-2"}),(0,r.jsx)("div",{className:"font-medium",children:"Интерактивная карта"}),(0,r.jsx)("div",{className:"text-sm",children:"ул. Примерная, 123, Кишинев"})]})}),(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("button",{className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors",children:"Открыть в Google Maps"})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Быстрые действия"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{onClick:()=>window.location.href="/#halls",className:"w-full bg-blue-100 hover:bg-blue-200 text-blue-700 py-3 px-4 rounded-lg transition-colors flex items-center",children:[(0,r.jsx)(f.A,{className:"w-5 h-5 mr-3"}),"Забронировать корт"]}),(0,r.jsxs)("button",{className:"w-full bg-green-100 hover:bg-green-200 text-green-700 py-3 px-4 rounded-lg transition-colors flex items-center",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 mr-3"}),"Позвонить сейчас"]}),(0,r.jsxs)("button",{className:"w-full bg-purple-100 hover:bg-purple-200 text-purple-700 py-3 px-4 rounded-lg transition-colors flex items-center",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 mr-3"}),"Написать email"]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Часто задаваемые вопросы"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:"Можно ли забронировать корт по телефону?"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Да, вы можете забронировать корт по телефону или через наш сайт."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:"Есть ли парковка?"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Да, у нас есть бесплатная охраняемая парковка для всех посетителей."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:"Можно ли арендовать инвентарь?"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Да, мы предоставляем в аренду ракетки, воланы и спортивную форму."})]})]})]})]})]})]}),(0,r.jsx)(c.A,{})]})}},8250:(e,s,t)=>{Promise.resolve().then(t.bind(t,7001))},8354:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=t(5239),l=t(8088),a=t(8170),i=t.n(a),n=t(893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let c={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3839)),"/Users/<USER>/Altius/badminton-club/src/app/contact/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/Users/<USER>/Altius/badminton-club/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Altius/badminton-club/src/app/contact/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,169,506,720,94,906],()=>t(8354));module.exports=r})();