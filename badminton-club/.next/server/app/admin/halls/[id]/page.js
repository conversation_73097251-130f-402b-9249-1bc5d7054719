(()=>{var e={};e.id=793,e.ids=[793],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9005:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},16391:(e,s,r)=>{"use strict";r.d(s,{ND:()=>i});var t=r(60463);let a=process.env.NEXT_PUBLIC_SUPABASE_URL||"https://your-project.supabase.co",l=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY||"your-anon-key",i=(0,t.UU)(a,l)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22738:(e,s,r)=>{Promise.resolve().then(r.bind(r,54477))},23928:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},27910:e=>{"use strict";e.exports=require("stream")},28559:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},47990:()=>{},54477:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Altius/badminton-club/src/app/admin/halls/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Altius/badminton-club/src/app/admin/halls/[id]/page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69594:(e,s,r)=>{Promise.resolve().then(r.bind(r,98672))},74075:e=>{"use strict";e.exports=require("zlib")},79410:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84027:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},88190:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var t=r(65239),a=r(48088),l=r(88170),i=r.n(l),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(s,o);let c={children:["",{children:["admin",{children:["halls",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,54477)),"/Users/<USER>/Altius/badminton-club/src/app/admin/halls/[id]/page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Altius/badminton-club/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Altius/badminton-club/src/app/admin/halls/[id]/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/halls/[id]/page",pathname:"/admin/halls/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98672:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>N});var t=r(60687),a=r(43210),l=r(16189),i=r(21891),n=r(51317),o=r(16391),c=r(9005),d=r(62688);let u=(0,d.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var x=r(11860);function m({hallId:e,currentImages:s,onImagesUpdate:r}){let[l,i]=(0,a.useState)(!1),[n,d]=(0,a.useState)(!1),m=async t=>{if(t.length){i(!0);try{let a=[];for(let s of Array.from(t)){if(!s.type.startsWith("image/")){alert(`Файл ${s.name} не является изображением`);continue}if(s.size>5242880){alert(`Файл ${s.name} слишком большой. Максимальный размер: 5MB`);continue}let r=s.name.split(".").pop(),t=`${e}/${Date.now()}-${Math.random().toString(36).substring(2)}.${r}`,{error:l}=await o.ND.storage.from("hall-images").upload(t,s,{cacheControl:"3600",upsert:!1});if(l){console.error("Upload error:",l),alert(`Ошибка загрузки файла ${s.name}: ${l.message}`);continue}let{data:i}=o.ND.storage.from("hall-images").getPublicUrl(t);a.push(i.publicUrl)}if(a.length>0){let e=[...s,...a];r(e),alert(`Успешно загружено ${a.length} изображений`)}}catch(e){console.error("Error uploading images:",e),alert("Произошла ошибка при загрузке изображений")}finally{i(!1)}}},p=async(t,a)=>{try{let l=t.split("/"),i=l[l.length-1],n=`${e}/${i}`,{error:c}=await o.ND.storage.from("hall-images").remove([n]);c&&console.error("Error removing file:",c);let d=s.filter((e,s)=>s!==a);r(d)}catch(e){console.error("Error removing image:",e),alert("Ошибка при удалении изображения")}};return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${n?"border-altius-blue bg-altius-blue/5":"border-gray-300 hover:border-altius-blue"}`,onDrop:e=>{e.preventDefault(),d(!1);let s=e.dataTransfer.files;s.length>0&&m(s)},onDragOver:e=>{e.preventDefault(),d(!0)},onDragLeave:e=>{e.preventDefault(),d(!1)},children:[(0,t.jsx)("input",{type:"file",multiple:!0,accept:"image/*",onChange:e=>e.target.files&&m(e.target.files),className:"hidden",id:`images-${e}`,disabled:l}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.A,{className:"w-12 h-12 text-gray-400 mx-auto"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:`images-${e}`,className:`cursor-pointer text-altius-blue hover:text-altius-blue-dark font-medium ${l?"opacity-50 cursor-not-allowed":""}`,children:l?"Загрузка...":"Выберите файлы"}),(0,t.jsx)("span",{className:"text-gray-500",children:" или перетащите их сюда"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"PNG, JPG, GIF до 5MB каждый"})]})]}),(0,t.jsxs)("button",{onClick:()=>document.getElementById(`images-${e}`)?.click(),disabled:l,className:"w-full bg-altius-orange text-white py-3 px-4 rounded-lg hover:bg-altius-orange-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed inline-flex items-center justify-center",children:[(0,t.jsx)(u,{className:"w-5 h-5 mr-2"}),l?"Загрузка изображений...":"Загрузить изображения"]}),s.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:["Загруженные изображения (",s.length,")"]}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:s.map((e,s)=>(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("img",{src:e,alt:`Изображение ${s+1}`,className:"w-full h-24 object-cover rounded-lg"}),(0,t.jsx)("button",{onClick:()=>p(e,s),className:"absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600",title:"Удалить изображение",children:(0,t.jsx)(x.A,{className:"w-3 h-3"})})]},s))})]})]})}var p=r(79410),h=r(28559);let g=(0,d.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),b=(0,d.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var f=r(41312),y=r(23928),v=r(84027),j=r(48730);function N(){let e=(0,l.useParams)(),s=(0,l.useRouter)();parseInt(e.id);let[r,c]=(0,a.useState)(null),[d,u]=(0,a.useState)(!0),[N,w]=(0,a.useState)(!1),[k,_]=(0,a.useState)(""),[A,C]=(0,a.useState)(""),[P,M]=(0,a.useState)(""),[q,E]=(0,a.useState)(""),$=async()=>{if(r){w(!0);try{let{error:e}=await o.ND.from("halls").update({name:r.name,courts_count:r.courts_count,price_per_hour:r.price_per_hour,description:r.description,detailed_description:r.detailed_description,features:r.features,specifications:r.specifications,amenities:r.amenities,working_hours:r.working_hours,is_active:r.is_active}).eq("id",r.id);if(e)throw e;alert("Зал успешно обновлен!")}catch(e){console.error("Error updating hall:",e),alert("Изменения сохранены локально (база данных недоступна)")}finally{w(!1)}}},U=()=>{k.trim()&&r&&(c({...r,features:[...r.features,k.trim()]}),_(""))},S=e=>{r&&c({...r,features:r.features.filter((s,r)=>r!==e)})},D=()=>{A.trim()&&r&&(c({...r,amenities:[...r.amenities,A.trim()]}),C(""))},I=e=>{r&&c({...r,amenities:r.amenities.filter((s,r)=>r!==e)})},z=()=>{P.trim()&&q.trim()&&r&&(c({...r,specifications:{...r.specifications,[P.trim()]:q.trim()}}),M(""),E(""))},G=e=>{if(r){let s={...r.specifications};delete s[e],c({...r,specifications:s})}},B=(e,s)=>{r&&c({...r,working_hours:{...r.working_hours,[e]:s}})};return d?(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(i.A,{}),(0,t.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-altius-blue mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Загрузка зала..."})]})}),(0,t.jsx)(n.A,{})]}):r?(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(i.A,{}),(0,t.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,t.jsx)("button",{onClick:()=>s.push("/admin/halls"),className:"p-2 rounded-lg hover:bg-gray-200 transition-colors",children:(0,t.jsx)(h.A,{className:"w-5 h-5"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Редактирование: ",r.name]}),(0,t.jsx)("p",{className:"text-gray-600",children:"Управление информацией о зале, фотографиями и настройками"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("button",{onClick:$,disabled:N,className:"bg-altius-blue text-white px-6 py-2 rounded-lg hover:bg-altius-blue-dark transition-colors disabled:opacity-50 inline-flex items-center",children:[(0,t.jsx)(g,{className:"w-4 h-4 mr-2"}),N?"Сохранение...":"Сохранить изменения"]}),(0,t.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${r.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:r.is_active?"Активен":"Неактивен"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 mr-2 text-altius-blue"}),"Основная информация"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Название зала"}),(0,t.jsx)("input",{type:"text",value:r.name,onChange:e=>c({...r,name:e.target.value}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Статус"}),(0,t.jsxs)("select",{value:r.is_active?"active":"inactive",onChange:e=>c({...r,is_active:"active"===e.target.value}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent",children:[(0,t.jsx)("option",{value:"active",children:"Активен"}),(0,t.jsx)("option",{value:"inactive",children:"Неактивен"})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Количество кортов"}),(0,t.jsx)("input",{type:"number",value:r.courts_count,onChange:e=>c({...r,courts_count:parseInt(e.target.value)||0}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Цена за час (лей)"}),(0,t.jsx)("input",{type:"number",value:r.price_per_hour,onChange:e=>c({...r,price_per_hour:parseInt(e.target.value)||0}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"})]})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Краткое описание"}),(0,t.jsx)("textarea",{value:r.description,onChange:e=>c({...r,description:e.target.value}),rows:2,className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Подробное описание"}),(0,t.jsx)("textarea",{value:r.detailed_description,onChange:e=>c({...r,detailed_description:e.target.value}),rows:4,className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Особенности зала"}),(0,t.jsx)("div",{className:"space-y-2 mb-4",children:r.features.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"text",value:e,onChange:e=>{let t=[...r.features];t[s]=e.target.value,c({...r,features:t})},className:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"}),(0,t.jsx)("button",{onClick:()=>S(s),className:"text-red-600 hover:bg-red-50 p-2 rounded-lg transition-colors",children:(0,t.jsx)(x.A,{className:"w-4 h-4"})})]},s))}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"text",value:k,onChange:e=>_(e.target.value),placeholder:"Добавить особенность",className:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent",onKeyPress:e=>"Enter"===e.key&&U()}),(0,t.jsx)("button",{onClick:U,className:"bg-altius-blue text-white p-2 rounded-lg hover:bg-altius-blue-dark transition-colors",children:(0,t.jsx)(b,{className:"w-4 h-4"})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Удобства"}),(0,t.jsx)("div",{className:"space-y-2 mb-4",children:r.amenities.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"text",value:e,onChange:e=>{let t=[...r.amenities];t[s]=e.target.value,c({...r,amenities:t})},className:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"}),(0,t.jsx)("button",{onClick:()=>I(s),className:"text-red-600 hover:bg-red-50 p-2 rounded-lg transition-colors",children:(0,t.jsx)(x.A,{className:"w-4 h-4"})})]},s))}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"text",value:A,onChange:e=>C(e.target.value),placeholder:"Добавить удобство",className:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent",onKeyPress:e=>"Enter"===e.key&&D()}),(0,t.jsx)("button",{onClick:D,className:"bg-altius-lime text-white p-2 rounded-lg hover:bg-altius-lime-dark transition-colors",children:(0,t.jsx)(b,{className:"w-4 h-4"})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Технические характеристики"}),(0,t.jsx)("div",{className:"space-y-2 mb-4",children:Object.entries(r.specifications).map(([e,s])=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"text",value:e,onChange:t=>{let a={...r.specifications};delete a[e],a[t.target.value]=s,c({...r,specifications:a})},className:"w-1/3 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent",placeholder:"Название"}),(0,t.jsx)("input",{type:"text",value:s,onChange:s=>{c({...r,specifications:{...r.specifications,[e]:s.target.value}})},className:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent",placeholder:"Значение"}),(0,t.jsx)("button",{onClick:()=>G(e),className:"text-red-600 hover:bg-red-50 p-2 rounded-lg transition-colors",children:(0,t.jsx)(x.A,{className:"w-4 h-4"})})]},e))}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"text",value:P,onChange:e=>M(e.target.value),placeholder:"Название характеристики",className:"w-1/3 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"}),(0,t.jsx)("input",{type:"text",value:q,onChange:e=>E(e.target.value),placeholder:"Значение",className:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent",onKeyPress:e=>"Enter"===e.key&&z()}),(0,t.jsx)("button",{onClick:z,className:"bg-altius-orange text-white p-2 rounded-lg hover:bg-altius-orange-dark transition-colors",children:(0,t.jsx)(b,{className:"w-4 h-4"})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Управление изображениями"}),(0,t.jsx)(m,{hallId:r.id,currentImages:r.images,onImagesUpdate:e=>{r&&c({...r,images:e})}})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Статистика"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"w-4 h-4 text-altius-blue mr-2"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Кортов"})]}),(0,t.jsx)("span",{className:"font-semibold",children:r.courts_count})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(y.A,{className:"w-4 h-4 text-altius-lime mr-2"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Цена/час"})]}),(0,t.jsxs)("span",{className:"font-semibold",children:[r.price_per_hour," лей"]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(v.A,{className:"w-4 h-4 text-altius-orange mr-2"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Особенности"})]}),(0,t.jsx)("span",{className:"font-semibold",children:r.features.length})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,t.jsx)(j.A,{className:"w-5 h-5 mr-2 text-altius-orange"}),"Часы работы"]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Будни (Пн-Пт)"}),(0,t.jsx)("input",{type:"text",value:r.working_hours.weekdays||"",onChange:e=>B("weekdays",e.target.value),placeholder:"06:00 - 23:00",className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Выходные (Сб-Вс)"}),(0,t.jsx)("input",{type:"text",value:r.working_hours.weekends||"",onChange:e=>B("weekends",e.target.value),placeholder:"08:00 - 22:00",className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"})]})]})]})]})]})]}),(0,t.jsx)(n.A,{})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(i.A,{}),(0,t.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(p.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Зал не найден"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Запрашиваемый зал не существует"}),(0,t.jsx)("button",{onClick:()=>s.push("/admin/halls"),className:"bg-altius-blue text-white px-4 py-2 rounded-lg hover:bg-altius-blue-dark transition-colors",children:"Вернуться к списку залов"})]})}),(0,t.jsx)(n.A,{})]})}}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,169,431,628,906],()=>r(88190));module.exports=t})();