(()=>{var e={};e.id=793,e.ids=[793],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9005:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14534:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var r=t(60687),a=t(43210),l=t(16189),i=t(21891),n=t(51317),o=t(16391),c=t(9005),d=t(16023),u=t(11860);function x({hallId:e,currentImages:s,onImagesUpdate:t}){let[l,i]=(0,a.useState)(!1),[n,x]=(0,a.useState)(!1),m=async r=>{if(r.length){i(!0);try{let a=[];for(let s of Array.from(r)){if(!s.type.startsWith("image/")){alert(`Файл ${s.name} не является изображением`);continue}if(s.size>5242880){alert(`Файл ${s.name} слишком большой. Максимальный размер: 5MB`);continue}let t=s.name.split(".").pop(),r=`${e}/${Date.now()}-${Math.random().toString(36).substring(2)}.${t}`,{error:l}=await o.ND.storage.from("hall-images").upload(r,s,{cacheControl:"3600",upsert:!1});if(l){console.error("Upload error:",l),alert(`Ошибка загрузки файла ${s.name}: ${l.message}`);continue}let{data:i}=o.ND.storage.from("hall-images").getPublicUrl(r);a.push(i.publicUrl)}if(a.length>0){let e=[...s,...a];t(e),alert(`Успешно загружено ${a.length} изображений`)}}catch(e){console.error("Error uploading images:",e),alert("Произошла ошибка при загрузке изображений")}finally{i(!1)}}},p=async(r,a)=>{try{let l=r.split("/"),i=l[l.length-1],n=`${e}/${i}`,{error:c}=await o.ND.storage.from("hall-images").remove([n]);c&&console.error("Error removing file:",c);let d=s.filter((e,s)=>s!==a);t(d)}catch(e){console.error("Error removing image:",e),alert("Ошибка при удалении изображения")}};return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${n?"border-altius-blue bg-altius-blue/5":"border-gray-300 hover:border-altius-blue"}`,onDrop:e=>{e.preventDefault(),x(!1);let s=e.dataTransfer.files;s.length>0&&m(s)},onDragOver:e=>{e.preventDefault(),x(!0)},onDragLeave:e=>{e.preventDefault(),x(!1)},children:[(0,r.jsx)("input",{type:"file",multiple:!0,accept:"image/*",onChange:e=>e.target.files&&m(e.target.files),className:"hidden",id:`images-${e}`,disabled:l}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.A,{className:"w-12 h-12 text-gray-400 mx-auto"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:`images-${e}`,className:`cursor-pointer text-altius-blue hover:text-altius-blue-dark font-medium ${l?"opacity-50 cursor-not-allowed":""}`,children:l?"Загрузка...":"Выберите файлы"}),(0,r.jsx)("span",{className:"text-gray-500",children:" или перетащите их сюда"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"PNG, JPG, GIF до 5MB каждый"})]})]}),(0,r.jsxs)("button",{onClick:()=>document.getElementById(`images-${e}`)?.click(),disabled:l,className:"w-full bg-altius-orange text-white py-3 px-4 rounded-lg hover:bg-altius-orange-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed inline-flex items-center justify-center",children:[(0,r.jsx)(d.A,{className:"w-5 h-5 mr-2"}),l?"Загрузка изображений...":"Загрузить изображения"]}),s.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:["Загруженные изображения (",s.length,")"]}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:s.map((e,s)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("img",{src:e,alt:`Изображение ${s+1}`,className:"w-full h-24 object-cover rounded-lg"}),(0,r.jsx)("button",{onClick:()=>p(e,s),className:"absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600",title:"Удалить изображение",children:(0,r.jsx)(u.A,{className:"w-3 h-3"})})]},s))})]})]})}var m=t(79410),p=t(28559),h=t(8819),g=t(96474),b=t(41312),f=t(23928),y=t(84027),v=t(48730);function j(){let e=(0,l.useParams)(),s=(0,l.useRouter)();parseInt(e.id);let[t,c]=(0,a.useState)(null),[d,j]=(0,a.useState)(!0),[N,w]=(0,a.useState)(!1),[k,A]=(0,a.useState)(""),[_,C]=(0,a.useState)(""),[M,q]=(0,a.useState)(""),[I,P]=(0,a.useState)(""),E=async()=>{if(t){w(!0);try{let{error:e}=await o.ND.from("halls").update({name:t.name,courts_count:t.courts_count,price_per_hour:t.price_per_hour,description:t.description,detailed_description:t.detailed_description,features:t.features,specifications:t.specifications,amenities:t.amenities,working_hours:t.working_hours,is_active:t.is_active}).eq("id",t.id);if(e)throw e;alert("Зал успешно обновлен!")}catch(e){console.error("Error updating hall:",e),alert("Изменения сохранены локально (база данных недоступна)")}finally{w(!1)}}},$=()=>{k.trim()&&t&&(c({...t,features:[...t.features,k.trim()]}),A(""))},D=e=>{t&&c({...t,features:t.features.filter((s,t)=>t!==e)})},z=()=>{_.trim()&&t&&(c({...t,amenities:[...t.amenities,_.trim()]}),C(""))},S=e=>{t&&c({...t,amenities:t.amenities.filter((s,t)=>t!==e)})},U=()=>{M.trim()&&I.trim()&&t&&(c({...t,specifications:{...t.specifications,[M.trim()]:I.trim()}}),q(""),P(""))},G=e=>{if(t){let s={...t.specifications};delete s[e],c({...t,specifications:s})}},O=(e,s)=>{t&&c({...t,working_hours:{...t.working_hours,[e]:s}})},H=async e=>{if(t){c({...t,images:e});try{let{error:s}=await o.ND.from("halls").update({images:e}).eq("id",t.id);s?(console.error("Error saving images to database:",s),alert("Ошибка сохранения изображений в базу данных")):console.log("Images saved to database successfully")}catch(e){console.error("Error updating images:",e),alert("Ошибка при обновлении изображений")}}};return d?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(i.A,{}),(0,r.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-altius-blue mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Загрузка зала..."})]})}),(0,r.jsx)(n.A,{})]}):t?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(i.A,{}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,r.jsx)("button",{onClick:()=>s.push("/admin/halls"),className:"p-2 rounded-lg hover:bg-gray-200 transition-colors",children:(0,r.jsx)(p.A,{className:"w-5 h-5"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Редактирование: ",t.name]}),(0,r.jsx)("p",{className:"text-gray-600",children:"Управление информацией о зале, фотографиями и настройками"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("button",{onClick:E,disabled:N,className:"bg-altius-blue text-white px-6 py-2 rounded-lg hover:bg-altius-blue-dark transition-colors disabled:opacity-50 inline-flex items-center",children:[(0,r.jsx)(h.A,{className:"w-4 h-4 mr-2"}),N?"Сохранение...":"Сохранить изменения"]}),(0,r.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${t.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:t.is_active?"Активен":"Неактивен"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 mr-2 text-altius-blue"}),"Основная информация"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Название зала"}),(0,r.jsx)("input",{type:"text",value:t.name,onChange:e=>c({...t,name:e.target.value}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Статус"}),(0,r.jsxs)("select",{value:t.is_active?"active":"inactive",onChange:e=>c({...t,is_active:"active"===e.target.value}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent",children:[(0,r.jsx)("option",{value:"active",children:"Активен"}),(0,r.jsx)("option",{value:"inactive",children:"Неактивен"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Количество кортов"}),(0,r.jsx)("input",{type:"number",value:t.courts_count,onChange:e=>c({...t,courts_count:parseInt(e.target.value)||0}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Цена за час (лей)"}),(0,r.jsx)("input",{type:"number",value:t.price_per_hour,onChange:e=>c({...t,price_per_hour:parseInt(e.target.value)||0}),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Краткое описание"}),(0,r.jsx)("textarea",{value:t.description,onChange:e=>c({...t,description:e.target.value}),rows:2,className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Подробное описание"}),(0,r.jsx)("textarea",{value:t.detailed_description,onChange:e=>c({...t,detailed_description:e.target.value}),rows:4,className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Особенности зала"}),(0,r.jsx)("div",{className:"space-y-2 mb-4",children:t.features.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"text",value:e,onChange:e=>{let r=[...t.features];r[s]=e.target.value,c({...t,features:r})},className:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"}),(0,r.jsx)("button",{onClick:()=>D(s),className:"text-red-600 hover:bg-red-50 p-2 rounded-lg transition-colors",children:(0,r.jsx)(u.A,{className:"w-4 h-4"})})]},s))}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"text",value:k,onChange:e=>A(e.target.value),placeholder:"Добавить особенность",className:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent",onKeyPress:e=>"Enter"===e.key&&$()}),(0,r.jsx)("button",{onClick:$,className:"bg-altius-blue text-white p-2 rounded-lg hover:bg-altius-blue-dark transition-colors",children:(0,r.jsx)(g.A,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Удобства"}),(0,r.jsx)("div",{className:"space-y-2 mb-4",children:t.amenities.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"text",value:e,onChange:e=>{let r=[...t.amenities];r[s]=e.target.value,c({...t,amenities:r})},className:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"}),(0,r.jsx)("button",{onClick:()=>S(s),className:"text-red-600 hover:bg-red-50 p-2 rounded-lg transition-colors",children:(0,r.jsx)(u.A,{className:"w-4 h-4"})})]},s))}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"text",value:_,onChange:e=>C(e.target.value),placeholder:"Добавить удобство",className:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent",onKeyPress:e=>"Enter"===e.key&&z()}),(0,r.jsx)("button",{onClick:z,className:"bg-altius-lime text-white p-2 rounded-lg hover:bg-altius-lime-dark transition-colors",children:(0,r.jsx)(g.A,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Технические характеристики"}),(0,r.jsx)("div",{className:"space-y-2 mb-4",children:Object.entries(t.specifications).map(([e,s])=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"text",value:e,onChange:r=>{let a={...t.specifications};delete a[e],a[r.target.value]=s,c({...t,specifications:a})},className:"w-1/3 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent",placeholder:"Название"}),(0,r.jsx)("input",{type:"text",value:s,onChange:s=>{c({...t,specifications:{...t.specifications,[e]:s.target.value}})},className:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent",placeholder:"Значение"}),(0,r.jsx)("button",{onClick:()=>G(e),className:"text-red-600 hover:bg-red-50 p-2 rounded-lg transition-colors",children:(0,r.jsx)(u.A,{className:"w-4 h-4"})})]},e))}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"text",value:M,onChange:e=>q(e.target.value),placeholder:"Название характеристики",className:"w-1/3 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"}),(0,r.jsx)("input",{type:"text",value:I,onChange:e=>P(e.target.value),placeholder:"Значение",className:"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent",onKeyPress:e=>"Enter"===e.key&&U()}),(0,r.jsx)("button",{onClick:U,className:"bg-altius-orange text-white p-2 rounded-lg hover:bg-altius-orange-dark transition-colors",children:(0,r.jsx)(g.A,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Управление изображениями"}),(0,r.jsx)(x,{hallId:t.id,currentImages:t.images,onImagesUpdate:H})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Статистика"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"w-4 h-4 text-altius-blue mr-2"}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Кортов"})]}),(0,r.jsx)("span",{className:"font-semibold",children:t.courts_count})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"w-4 h-4 text-altius-lime mr-2"}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Цена/час"})]}),(0,r.jsxs)("span",{className:"font-semibold",children:[t.price_per_hour," лей"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(y.A,{className:"w-4 h-4 text-altius-orange mr-2"}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Особенности"})]}),(0,r.jsx)("span",{className:"font-semibold",children:t.features.length})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(v.A,{className:"w-5 h-5 mr-2 text-altius-orange"}),"Часы работы"]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Будни (Пн-Пт)"}),(0,r.jsx)("input",{type:"text",value:t.working_hours.weekdays||"",onChange:e=>O("weekdays",e.target.value),placeholder:"06:00 - 23:00",className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Выходные (Сб-Вс)"}),(0,r.jsx)("input",{type:"text",value:t.working_hours.weekends||"",onChange:e=>O("weekends",e.target.value),placeholder:"08:00 - 22:00",className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent"})]})]})]})]})]})]}),(0,r.jsx)(n.A,{})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(i.A,{}),(0,r.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(m.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Зал не найден"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Запрашиваемый зал не существует"}),(0,r.jsx)("button",{onClick:()=>s.push("/admin/halls"),className:"bg-altius-blue text-white px-4 py-2 rounded-lg hover:bg-altius-blue-dark transition-colors",children:"Вернуться к списку залов"})]})}),(0,r.jsx)(n.A,{})]})}},16023:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},16391:(e,s,t)=>{"use strict";t.d(s,{ND:()=>n});var r=t(60463);let a="https://whdfkjsmyolbzlwtaoix.supabase.co",l="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndoZGZranNteW9sYnpsd3Rhb2l4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzNDUxODksImV4cCI6MjA2ODkyMTE4OX0.wodggEz_ElgvYVWPA4o3gmAg84AWezDmnRaHAkC2Dps",i=a&&l&&a.includes("supabase.co")&&l.length>100;console.log("Supabase Configuration:",{url:a?`${a.substring(0,30)}...`:"NOT SET",keyLength:l.length,isConfigured:i});let n=(0,r.UU)(a||"https://placeholder.supabase.co",l||"placeholder-key")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22738:(e,s,t)=>{Promise.resolve().then(t.bind(t,54477))},23928:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},27910:e=>{"use strict";e.exports=require("stream")},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},41312:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},47990:()=>{},54477:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Altius/badminton-club/src/app/admin/halls/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Altius/badminton-club/src/app/admin/halls/[id]/page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69594:(e,s,t)=>{Promise.resolve().then(t.bind(t,14534))},74075:e=>{"use strict";e.exports=require("zlib")},79410:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84027:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},88190:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let c={children:["",{children:["admin",{children:["halls",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,54477)),"/Users/<USER>/Altius/badminton-club/src/app/admin/halls/[id]/page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Altius/badminton-club/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Altius/badminton-club/src/app/admin/halls/[id]/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/halls/[id]/page",pathname:"/admin/halls/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,169,431,957,906],()=>t(88190));module.exports=r})();