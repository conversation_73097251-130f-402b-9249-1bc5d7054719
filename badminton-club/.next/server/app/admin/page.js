(()=>{var e={};e.id=698,e.ids=[698],e.modules={228:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(1658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1132:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx","default")},1135:()=>{},1729:(e,t,s)=>{Promise.resolve().then(s.bind(s,1132))},2953:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(687),r=s(3210),l=s(1877),i=s(192),n=s(8494),d=s(1911),c=s(228),o=s(5336),x=s(8730),m=s(1312),h=s(2688);let p=(0,h.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);var u=s(7033),g=s(4952),y=s(8340),v=s(3931);let b=(0,h.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),j=(0,h.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);function f(){let[e,t]=(0,r.useState)(null),[s,h]=(0,r.useState)((0,l.k)(new Date,{weekStartsOn:1})),f=[{id:"1",name:"Иван Петров",phone:"+373 69 123 456",email:"<EMAIL>",hallId:1,court:1,date:new Date,time:"10:00",status:"confirmed",createdAt:new Date},{id:"2",name:"Мария Иванова",phone:"+373 69 789 012",hallId:2,court:3,date:(0,i.f)(new Date,1),time:"14:30",status:"pending",createdAt:new Date},{id:"3",name:"Александр Сидоров",phone:"+373 69 345 678",email:"<EMAIL>",hallId:1,court:2,date:new Date,time:"18:00",status:"confirmed",createdAt:new Date}],[N,w]=(0,r.useState)(f),k=Array.from({length:7},(e,t)=>(0,i.f)(s,t)),P=t=>N.filter(s=>(0,n.GP)(s.date,"yyyy-MM-dd")===(0,n.GP)(t,"yyyy-MM-dd")&&(!e||s.hallId===e)),A=(e,t)=>{w(s=>s.map(s=>s.id===e?{...s,status:t}:s))},M=e=>{w(t=>t.filter(t=>t.id!==e))},_=e=>{switch(e){case"confirmed":return"text-green-600 bg-green-100";case"pending":return"text-yellow-600 bg-yellow-100";case"cancelled":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},C=e=>{switch(e){case"confirmed":return"Подтверждено";case"pending":return"Ожидает";case"cancelled":return"Отменено";default:return"Неизвестно"}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Панель администратора"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"BadmintonClub Кишинев"})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"w-8 h-8 text-blue-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Сегодня"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P(new Date).length})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"w-8 h-8 text-green-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Подтверждено"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:N.filter(e=>"confirmed"===e.status).length})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"w-8 h-8 text-yellow-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Ожидает"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:N.filter(e=>"pending"===e.status).length})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(m.A,{className:"w-8 h-8 text-purple-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Всего"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:N.length})]})]})})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow mb-6 p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(p,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("select",{value:e||"",onChange:e=>t(e.target.value?parseInt(e.target.value):null),className:"border border-gray-300 rounded-md px-3 py-2",children:[(0,a.jsx)("option",{value:"",children:"Все залы"}),(0,a.jsx)("option",{value:"1",children:"Зал 1 (3 корта)"}),(0,a.jsx)("option",{value:"2",children:"Зал 2 (7 кортов)"}),(0,a.jsx)("option",{value:"3",children:"Зал 3 (7 кортов)"})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Расписание бронирований"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>{h((0,i.f)(s,-7))},className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(u.A,{className:"w-5 h-5"})}),(0,a.jsx)("div",{className:"text-lg font-semibold",children:(0,n.GP)(s,"MMMM yyyy",{locale:d.ru})}),(0,a.jsx)("button",{onClick:()=>{h((0,i.f)(s,7))},className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(g.A,{className:"w-5 h-5"})})]})]})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"grid grid-cols-7 gap-4",children:k.map((e,t)=>{let s=P(e);return(0,a.jsxs)("div",{className:"border rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"text-center mb-3",children:[(0,a.jsx)("div",{className:"text-xs text-gray-500 mb-1",children:(0,n.GP)(e,"EEE",{locale:d.ru})}),(0,a.jsx)("div",{className:"font-semibold",children:(0,n.GP)(e,"d")})]}),(0,a.jsx)("div",{className:"space-y-2",children:s.map(e=>(0,a.jsxs)("div",{className:"text-xs p-2 rounded border-l-2 border-blue-500 bg-blue-50",children:[(0,a.jsx)("div",{className:"font-medium",children:e.time}),(0,a.jsxs)("div",{className:"text-gray-600",children:["Зал ",e.hallId,", Корт ",e.court]}),(0,a.jsx)("div",{className:"text-gray-600 truncate",children:e.name}),(0,a.jsx)("div",{className:`inline-block px-1 py-0.5 rounded text-xs ${_(e.status)}`,children:C(e.status)})]},e.id))})]},t)})})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow mt-6",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Все бронирования"})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Клиент"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Контакты"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Бронирование"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Статус"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Действия"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:N.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-900 flex items-center",children:[(0,a.jsx)(y.A,{className:"w-4 h-4 mr-1"}),e.phone]}),e.email&&(0,a.jsxs)("div",{className:"text-sm text-gray-500 flex items-center mt-1",children:[(0,a.jsx)(v.A,{className:"w-4 h-4 mr-1"}),e.email]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:(0,n.GP)(e.date,"dd.MM.yyyy",{locale:d.ru})}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.time," • Зал ",e.hallId,", Корт ",e.court]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${_(e.status)}`,children:C(e.status)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:["pending"===e.status&&(0,a.jsx)("button",{onClick:()=>A(e.id,"confirmed"),className:"text-green-600 hover:text-green-900",title:"Подтвердить",children:(0,a.jsx)(o.A,{className:"w-4 h-4"})}),"cancelled"!==e.status&&(0,a.jsx)("button",{onClick:()=>A(e.id,"cancelled"),className:"text-red-600 hover:text-red-900",title:"Отменить",children:(0,a.jsx)(b,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>M(e.id),className:"text-gray-600 hover:text-gray-900",title:"Удалить",children:(0,a.jsx)(j,{className:"w-4 h-4"})})]})})]},e.id))})]})})]})]})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3502:()=>{},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>d});var a=s(7413),r=s(2202),l=s.n(r),i=s(4988),n=s.n(i);s(1135);let d={title:"BadmintonClub Кишинев - Бронирование кортов",description:"Современный бадминтонный клуб в Кишиневе. 17 профессиональных кортов в 3 залах. Удобная система онлайн бронирования."};function c({children:e}){return(0,a.jsx)("html",{lang:"ru",children:(0,a.jsx)("body",{className:`${l().variable} ${n().variable} antialiased`,children:e})})}},4844:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},4881:(e,t,s)=>{Promise.resolve().then(s.bind(s,2953))},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6700:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},7871:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=s(5239),r=s(8088),l=s(8170),i=s.n(l),n=s(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let c={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1132)),"/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"/Users/<USER>/Altius/badminton-club/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8230:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,169,506,225],()=>s(7871));module.exports=a})();