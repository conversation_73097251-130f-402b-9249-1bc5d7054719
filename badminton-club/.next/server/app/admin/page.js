(()=>{var e={};e.id=698,e.ids=[698],e.modules={1132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10022:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12252:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var a=t(60687),r=t(43210),l=t(51877),i=t(20192),d=t(48494),n=t(91911),c=t(85814),x=t.n(c),m=t(79410),o=t(10022),h=t(62688);let p=(0,h.A)("pen-tool",[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z",key:"nt11vn"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18",key:"15qc1e"}],["path",{d:"m2.3 2.3 7.286 7.286",key:"1wuzzi"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]]);var u=t(40228),g=t(84027),y=t(5336),v=t(48730),j=t(41312);let b=(0,h.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);var N=t(47033),f=t(14952),w=t(48340),k=t(41550);let A=(0,h.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var M=t(88233);function P(){let[e,s]=(0,r.useState)(null),[t,c]=(0,r.useState)((0,l.k)(new Date,{weekStartsOn:1})),h=[{id:"1",name:"Иван Петров",phone:"+373 69 123 456",email:"<EMAIL>",hallId:1,court:1,date:new Date,time:"10:00",status:"confirmed",createdAt:new Date},{id:"2",name:"Мария Иванова",phone:"+373 69 789 012",hallId:2,court:3,date:(0,i.f)(new Date,1),time:"14:30",status:"pending",createdAt:new Date},{id:"3",name:"Александр Сидоров",phone:"+373 69 345 678",email:"<EMAIL>",hallId:1,court:2,date:new Date,time:"18:00",status:"confirmed",createdAt:new Date}],[P,z]=(0,r.useState)(h),q=Array.from({length:7},(e,s)=>(0,i.f)(t,s)),_=s=>P.filter(t=>(0,d.GP)(t.date,"yyyy-MM-dd")===(0,d.GP)(s,"yyyy-MM-dd")&&(!e||t.hallId===e)),C=(e,s)=>{z(t=>t.map(t=>t.id===e?{...t,status:s}:t))},G=e=>{z(s=>s.filter(s=>s.id!==e))},D=e=>{switch(e){case"confirmed":return"text-green-600 bg-green-100";case"pending":return"text-yellow-600 bg-yellow-100";case"cancelled":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},E=e=>{switch(e){case"confirmed":return"Подтверждено";case"pending":return"Ожидает";case"cancelled":return"Отменено";default:return"Неизвестно"}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Панель администратора"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"BadmintonClub Кишинев"})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsx)(x(),{href:"/admin/halls",className:"bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow border-l-4 border-altius-blue",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"bg-altius-blue/10 p-3 rounded-lg",children:(0,a.jsx)(m.A,{className:"w-6 h-6 text-altius-blue"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Управление залами"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Редактировать информацию о залах"})]})]})}),(0,a.jsxs)(x(),{href:"/admin/posts",className:"bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow border-l-4 border-altius-lime cursor-pointer",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"bg-altius-lime/10 p-3 rounded-lg",children:(0,a.jsx)(o.A,{className:"w-6 h-6 text-altius-lime"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Блог и События"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Управление постами"})]})]}),(0,a.jsx)(p,{className:"w-5 h-5 text-gray-400"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Всего постов:"}),(0,a.jsx)("span",{className:"font-medium",children:"-"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Опубликованных:"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:"-"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Черновиков:"}),(0,a.jsx)("span",{className:"font-medium text-yellow-600",children:"-"})]})]})]}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-xl shadow-lg border-l-4 border-altius-orange",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"bg-altius-orange/10 p-3 rounded-lg",children:(0,a.jsx)(u.A,{className:"w-6 h-6 text-altius-orange"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Бронирования"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Управление бронированиями"})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-xl shadow-lg border-l-4 border-altius-orange",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"bg-altius-orange/10 p-3 rounded-lg",children:(0,a.jsx)(g.A,{className:"w-6 h-6 text-altius-orange"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Настройки"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Общие настройки системы"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"w-8 h-8 text-blue-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Сегодня"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:_(new Date).length})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(y.A,{className:"w-8 h-8 text-green-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Подтверждено"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.filter(e=>"confirmed"===e.status).length})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"w-8 h-8 text-yellow-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Ожидает"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.filter(e=>"pending"===e.status).length})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"w-8 h-8 text-purple-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Всего"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.length})]})]})})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow mb-6 p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(b,{className:"w-5 h-5 text-gray-400"}),(0,a.jsxs)("select",{value:e||"",onChange:e=>s(e.target.value?parseInt(e.target.value):null),className:"border border-gray-300 rounded-md px-3 py-2",children:[(0,a.jsx)("option",{value:"",children:"Все залы"}),(0,a.jsx)("option",{value:"1",children:"Зал 1 (3 корта)"}),(0,a.jsx)("option",{value:"2",children:"Зал 2 (7 кортов)"}),(0,a.jsx)("option",{value:"3",children:"Зал 3 (7 кортов)"})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Расписание бронирований"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>{c((0,i.f)(t,-7))},className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(N.A,{className:"w-5 h-5"})}),(0,a.jsx)("div",{className:"text-lg font-semibold",children:(0,d.GP)(t,"MMMM yyyy",{locale:n.ru})}),(0,a.jsx)("button",{onClick:()=>{c((0,i.f)(t,7))},className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(f.A,{className:"w-5 h-5"})})]})]})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"grid grid-cols-7 gap-4",children:q.map((e,s)=>{let t=_(e);return(0,a.jsxs)("div",{className:"border rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"text-center mb-3",children:[(0,a.jsx)("div",{className:"text-xs text-gray-500 mb-1",children:(0,d.GP)(e,"EEE",{locale:n.ru})}),(0,a.jsx)("div",{className:"font-semibold",children:(0,d.GP)(e,"d")})]}),(0,a.jsx)("div",{className:"space-y-2",children:t.map(e=>(0,a.jsxs)("div",{className:"text-xs p-2 rounded border-l-2 border-blue-500 bg-blue-50",children:[(0,a.jsx)("div",{className:"font-medium",children:e.time}),(0,a.jsxs)("div",{className:"text-gray-600",children:["Зал ",e.hallId,", Корт ",e.court]}),(0,a.jsx)("div",{className:"text-gray-600 truncate",children:e.name}),(0,a.jsx)("div",{className:`inline-block px-1 py-0.5 rounded text-xs ${D(e.status)}`,children:E(e.status)})]},e.id))})]},s)})})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow mt-6",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Все бронирования"})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Клиент"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Контакты"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Бронирование"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Статус"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Действия"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:P.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-900 flex items-center",children:[(0,a.jsx)(w.A,{className:"w-4 h-4 mr-1"}),e.phone]}),e.email&&(0,a.jsxs)("div",{className:"text-sm text-gray-500 flex items-center mt-1",children:[(0,a.jsx)(k.A,{className:"w-4 h-4 mr-1"}),e.email]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:(0,d.GP)(e.date,"dd.MM.yyyy",{locale:n.ru})}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.time," • Зал ",e.hallId,", Корт ",e.court]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${D(e.status)}`,children:E(e.status)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:["pending"===e.status&&(0,a.jsx)("button",{onClick:()=>C(e.id,"confirmed"),className:"text-green-600 hover:text-green-900",title:"Подтвердить",children:(0,a.jsx)(y.A,{className:"w-4 h-4"})}),"cancelled"!==e.status&&(0,a.jsx)("button",{onClick:()=>C(e.id,"cancelled"),className:"text-red-600 hover:text-red-900",title:"Отменить",children:(0,a.jsx)(A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>G(e.id),className:"text-gray-600 hover:text-gray-900",title:"Удалить",children:(0,a.jsx)(M.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})]})]})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33502:()=>{},33873:e=>{"use strict";e.exports=require("path")},36700:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},38230:()=>{},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70798:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>x,routeModule:()=>o,tree:()=>c});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),d=t(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(s,n);let c={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Altius/badminton-club/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["/Users/<USER>/Altius/badminton-club/src/app/admin/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},o=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79410:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},79551:e=>{"use strict";e.exports=require("url")},81729:(e,s,t)=>{Promise.resolve().then(t.bind(t,1132))},84027:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},88233:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,metadata:()=>n});var a=t(37413),r=t(2202),l=t.n(r),i=t(64988),d=t.n(i);t(61135);let n={title:"BadmintonClub Кишинев - Бронирование кортов",description:"Современный бадминтонный клуб в Кишиневе. 17 профессиональных кортов в 3 залах. Удобная система онлайн бронирования."};function c({children:e}){return(0,a.jsx)("html",{lang:"ru",children:(0,a.jsx)("body",{className:`${l().variable} ${d().variable} antialiased`,children:e})})}},94844:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},94881:(e,s,t)=>{Promise.resolve().then(t.bind(t,12252))}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,169,431,225],()=>t(70798));module.exports=a})();