(()=>{var e={};e.id=861,e.ids=[861],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9005:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},16243:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var r=t(60687),l=t(43210),a=t(16189),i=t(21891),n=t(51317),c=t(46436),d=t(54332),o=t(9005);let x=(0,t(62688).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);var m=t(47033),h=t(14952),u=t(11860);function p({images:e,videos:s,title:t}){let[a,i]=(0,l.useState)(0),[n,c]=(0,l.useState)(!1),[d,p]=(0,l.useState)(!1),[b,g]=(0,l.useState)(0),[j,v]=(0,l.useState)("images"),N=()=>{i(s=>(s+1)%e.length)},f=()=>{i(s=>(s-1+e.length)%e.length)},y=e=>{i(e),c(!0)},w=e=>{g(e),p(!0)};return(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden",children:[(0,r.jsx)("div",{className:"border-b border-gray-200",children:(0,r.jsxs)("nav",{className:"flex",children:[(0,r.jsxs)("button",{onClick:()=>v("images"),className:`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${"images"===j?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:[(0,r.jsx)(o.A,{className:"w-4 h-4 inline mr-2"}),"Фотографии (",e.length,")"]}),(0,r.jsxs)("button",{onClick:()=>v("videos"),className:`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${"videos"===j?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:[(0,r.jsx)(x,{className:"w-4 h-4 inline mr-2"}),"Видео (",s.length,")"]})]})}),(0,r.jsxs)("div",{className:"p-6",children:["images"===j&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"relative h-96 mb-4 rounded-lg overflow-hidden",children:[(0,r.jsx)("img",{src:e[a],alt:`${t} - фото ${a+1}`,className:"w-full h-full object-cover cursor-pointer",onClick:()=>y(a)}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"}),e.length>1&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:f,className:"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 transition-colors",children:(0,r.jsx)(m.A,{className:"w-5 h-5"})}),(0,r.jsx)("button",{onClick:N,className:"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 transition-colors",children:(0,r.jsx)(h.A,{className:"w-5 h-5"})})]}),(0,r.jsxs)("div",{className:"absolute bottom-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm",children:[a+1," / ",e.length]})]}),(0,r.jsx)("div",{className:"flex space-x-2 overflow-x-auto pb-2",children:e.map((e,s)=>(0,r.jsx)("button",{onClick:()=>i(s),className:`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors ${a===s?"border-blue-500":"border-gray-200"}`,children:(0,r.jsx)("img",{src:e,alt:`Миниатюра ${s+1}`,className:"w-full h-full object-cover"})},s))})]}),"videos"===j&&(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.map((e,s)=>(0,r.jsxs)("div",{className:"relative bg-gray-200 rounded-lg overflow-hidden cursor-pointer group",onClick:()=>w(s),children:[(0,r.jsx)("div",{className:"aspect-video flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(x,{className:"w-12 h-12 text-blue-600 group-hover:text-blue-700 transition-colors mx-auto mb-2"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Видео ",s+1]})]})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors"})]},s))})]}),n&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"relative max-w-6xl max-h-full w-full",children:[(0,r.jsx)("button",{onClick:()=>c(!1),className:"absolute top-4 right-4 text-white hover:text-gray-300 z-10 bg-black/50 rounded-full p-2",children:(0,r.jsx)(u.A,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("img",{src:e[a],alt:`${t} - фото ${a+1}`,className:"max-w-full max-h-[80vh] object-contain mx-auto"}),e.length>1&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:f,className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 bg-black/50 rounded-full p-2",children:(0,r.jsx)(m.A,{className:"w-8 h-8"})}),(0,r.jsx)("button",{onClick:N,className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 bg-black/50 rounded-full p-2",children:(0,r.jsx)(h.A,{className:"w-8 h-8"})})]})]}),(0,r.jsxs)("div",{className:"text-center mt-4",children:[(0,r.jsx)("p",{className:"text-white text-lg",children:t}),(0,r.jsxs)("p",{className:"text-gray-300",children:["Фото ",a+1," из ",e.length]})]})]})}),d&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"relative max-w-4xl max-h-full w-full",children:[(0,r.jsx)("button",{onClick:()=>p(!1),className:"absolute top-4 right-4 text-white hover:text-gray-300 z-10 bg-black/50 rounded-full p-2",children:(0,r.jsx)(u.A,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{className:"bg-gray-800 rounded-lg p-8 text-center",children:[(0,r.jsx)(x,{className:"w-16 h-16 text-white mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-white text-xl mb-2",children:"Видео плеер"}),(0,r.jsxs)("p",{className:"text-gray-400 mb-4",children:["Здесь будет воспроизводиться видео: ",s[b]]}),(0,r.jsx)("div",{className:"bg-gray-700 rounded-lg p-4 aspect-video flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(x,{className:"w-12 h-12 text-gray-400 mx-auto mb-2"}),(0,r.jsxs)("p",{className:"text-gray-400",children:["Видео ",b+1]})]})})]})]})})]})}t(16391);var b=t(28559),g=t(41312),j=t(97992),v=t(64398),N=t(40228),f=t(48730);function y(){let e=(0,a.useParams)(),s=(0,a.useRouter)();parseInt(e.id);let[t,o]=(0,l.useState)(null),[x,m]=(0,l.useState)(!0),[h,u]=(0,l.useState)(!1),[y,w]=(0,l.useState)(null);return x?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-altius-blue mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Загрузка информации о зале..."})]})}):t?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(i.A,{}),(0,r.jsxs)("section",{className:"relative h-96 bg-gradient-to-br from-blue-600 to-blue-800",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,r.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center",children:(0,r.jsxs)("div",{className:"text-white",children:[(0,r.jsxs)("button",{onClick:()=>s.push("/"),className:"flex items-center text-blue-200 hover:text-white mb-4 transition-colors",children:[(0,r.jsx)(b.A,{className:"w-4 h-4 mr-2"}),"Назад к залам"]}),(0,r.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-4",children:t.name}),(0,r.jsx)("p",{className:"text-xl md:text-2xl text-blue-100 mb-6",children:t.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-blue-100",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"w-5 h-5 mr-2"}),t.courts_count," кортов"]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(j.A,{className:"w-5 h-5 mr-2"}),"Кишинев"]}),(0,r.jsxs)("div",{className:"text-2xl font-bold text-white",children:[t.price_per_hour," лей/час"]})]})]})})]}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)(p,{images:t.images,videos:t.videos,title:t.name})}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 mb-8",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"О зале"}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed mb-6",children:t.detailed_description}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"Особенности:"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:t.features.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(v.A,{className:"w-4 h-4 text-blue-500 mr-2"}),(0,r.jsx)("span",{className:"text-gray-600",children:e})]},s))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"Технические характеристики:"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Площадь:"}),(0,r.jsx)("div",{className:"font-medium",children:t.specifications.area})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Высота потолков:"}),(0,r.jsx)("div",{className:"font-medium",children:t.specifications.height})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Покрытие:"}),(0,r.jsx)("div",{className:"font-medium",children:t.specifications.flooring})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Освещение:"}),(0,r.jsx)("div",{className:"font-medium",children:t.specifications.lighting})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Вентиляция:"}),(0,r.jsx)("div",{className:"font-medium",children:t.specifications.ventilation})]})]})]})]})]}),(0,r.jsxs)("div",{className:"lg:col-span-1",children:[!h&&!y&&(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 mb-6 sticky top-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Бронирование"}),(0,r.jsxs)("div",{className:"text-center mb-4",children:[(0,r.jsxs)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:[t.price_per_hour," лей"]}),(0,r.jsx)("div",{className:"text-gray-600",children:"за час"})]}),(0,r.jsxs)("button",{onClick:()=>u(!0),className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center",children:[(0,r.jsx)(N.A,{className:"w-4 h-4 mr-2"}),"Забронировать"]})]}),h&&!y&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)(c.A,{hallId:t.id,onSlotSelect:(e,s,t)=>{w({date:e,time:s,court:t})}}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)("button",{onClick:()=>u(!1),className:"w-full text-gray-600 hover:text-gray-800 py-2",children:"Отмена"})})]}),y&&(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)(d.A,{hallId:t.id,date:y.date,time:y.time,court:y.court,onSubmit:e=>{console.log("Booking submitted:",e),w(null),u(!1)},onCancel:()=>{w(null),u(!1)}})}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Режим работы"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:"Пн-Пт:"}),(0,r.jsx)("div",{className:"text-gray-600",children:t.working_hours.weekdays})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:"Сб-Вс:"}),(0,r.jsx)("div",{className:"text-gray-600",children:t.working_hours.weekends})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Удобства"}),(0,r.jsx)("div",{className:"space-y-2",children:t.amenities.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,r.jsx)("span",{className:"text-gray-600",children:e})]},s))})]})]})]})}),(0,r.jsx)(n.A,{})]}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Зал не найден"}),(0,r.jsx)("button",{onClick:()=>s.push("/"),className:"text-blue-600 hover:text-blue-700",children:"Вернуться на главную"})]})})}},18629:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Altius/badminton-club/src/app/halls/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Altius/badminton-club/src/app/halls/[id]/page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36240:(e,s,t)=>{Promise.resolve().then(t.bind(t,18629))},45968:(e,s,t)=>{Promise.resolve().then(t.bind(t,16243))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98138:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var r=t(65239),l=t(48088),a=t(88170),i=t.n(a),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d={children:["",{children:["halls",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,18629)),"/Users/<USER>/Altius/badminton-club/src/app/halls/[id]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Altius/badminton-club/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/Altius/badminton-club/src/app/halls/[id]/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/halls/[id]/page",pathname:"/halls/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,169,431,957,94,225,906,475],()=>t(98138));module.exports=r})();