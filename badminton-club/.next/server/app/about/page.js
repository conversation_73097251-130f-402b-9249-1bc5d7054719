(()=>{var e={};e.id=220,e.ids=[220],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12941:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},16189:(e,t,s)=>{"use strict";var r=s(65773);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28770:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Altius/badminton-club/src/app/about/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Altius/badminton-club/src/app/about/page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39236:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(60687),a=s(21891),l=s(51317),i=s(62688);let n=(0,i.A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);var c=s(41312),d=s(48730);let o=(0,i.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),x=(0,i.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var m=s(64398);function h(){let e=[{icon:n,title:"500+ довольных клиентов",description:"За 5 лет работы мы обслужили более 500 постоянных клиентов"},{icon:c.A,title:"17 профессиональных кортов",description:"3 современных зала с кортами международного стандарта"},{icon:d.A,title:"16 часов работы в день",description:"Работаем с 6:00 до 23:00 для вашего удобства"},{icon:o,title:"50+ турниров проведено",description:"Организуем турниры различного уровня для всех возрастов"}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(a.A,{}),(0,r.jsx)("section",{className:"bg-gradient-to-br from-blue-600 to-blue-800 text-white",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:"О нашем клубе"}),(0,r.jsx)("p",{className:"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto",children:"BadmintonClub Кишинев - ведущий бадминтонный клуб Молдовы, где профессионализм встречается с комфортом"})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,r.jsx)("section",{className:"mb-16",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Наша история"}),(0,r.jsxs)("div",{className:"space-y-4 text-gray-600",children:[(0,r.jsx)("p",{children:"BadmintonClub Кишинев был основан в 2019 году группой энтузиастов бадминтона, которые мечтали создать современный спортивный центр европейского уровня в столице Молдовы."}),(0,r.jsx)("p",{children:"За пять лет работы мы выросли от небольшого зала с тремя кортами до крупнейшего бадминтонного комплекса в стране с 17 профессиональными кортами в трех залах."}),(0,r.jsx)("p",{children:"Сегодня наш клуб является домашней ареной для сборной команды Молдовы по бадминтону и местом проведения международных турниров."})]})]}),(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("div",{className:"bg-gradient-to-br from-blue-500 to-blue-700 rounded-xl p-8 text-white",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-5xl mb-4",children:"\uD83C\uDFF8"}),(0,r.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"5 лет успеха"}),(0,r.jsx)("p",{className:"text-blue-100",children:"Мы гордимся тем, что стали частью спортивной жизни Кишинева и помогли сотням людей полюбить бадминтон"})]})})})]})}),(0,r.jsxs)("section",{className:"mb-16",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Наша миссия и ценности"}),(0,r.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Мы стремимся популяризировать бадминтон в Молдове и создавать условия для развития спорта на всех уровнях"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 text-center",children:[(0,r.jsx)(x,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-3",children:"Любовь к спорту"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Мы искренне любим бадминтон и хотим поделиться этой страстью с каждым посетителем"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 text-center",children:[(0,r.jsx)(m.A,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-3",children:"Качество"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Только лучшее оборудование, покрытия и условия для комфортной игры"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 text-center",children:[(0,r.jsx)(c.A,{className:"w-12 h-12 text-blue-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-3",children:"Сообщество"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Мы создаем дружественную атмосферу, где каждый чувствует себя частью большой семьи"})]})]})]}),(0,r.jsxs)("section",{className:"mb-16",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Наши достижения"}),(0,r.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"За годы работы мы достигли значительных результатов и продолжаем развиваться"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:e.map((e,t)=>(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 text-center",children:[(0,r.jsx)(e.icon,{className:"w-10 h-10 text-blue-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]},t))})]}),(0,r.jsxs)("section",{className:"mb-16",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Наша команда"}),(0,r.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Профессиональные тренеры и администраторы, которые сделают ваше пребывание в клубе максимально комфортным"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[{name:"Александр Петров",position:"Директор клуба",experience:"15 лет в бадминтоне",description:"Мастер спорта по бадминтону, основатель клуба",image:"/api/placeholder/300/300?text=Александр+Петров"},{name:"Мария Иванова",position:"Главный тренер",experience:"12 лет тренерского стажа",description:"Кандидат в мастера спорта, специалист по работе с детьми",image:"/api/placeholder/300/300?text=Мария+Иванова"},{name:"Дмитрий Сидоров",position:"Тренер",experience:"8 лет в спорте",description:"Специалист по технической подготовке игроков",image:"/api/placeholder/300/300?text=Дмитрий+Сидоров"}].map((e,t)=>(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden",children:[(0,r.jsx)("div",{className:"aspect-square bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center text-white",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDC64"}),(0,r.jsx)("div",{className:"text-xl font-semibold",children:e.name})]})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2",children:e.name}),(0,r.jsx)("p",{className:"text-blue-600 font-medium mb-2",children:e.position}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:e.experience}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]})]},t))})]}),(0,r.jsxs)("section",{className:"bg-blue-600 rounded-xl text-white p-8 text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-4",children:"Присоединяйтесь к нам!"}),(0,r.jsx)("p",{className:"text-blue-100 mb-6 max-w-2xl mx-auto",children:"Станьте частью нашего спортивного сообщества. Забронируйте корт и почувствуйте разницу игры в профессиональных условиях."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)("button",{onClick:()=>window.location.href="/#halls",className:"bg-white text-blue-600 font-semibold py-3 px-8 rounded-lg hover:bg-blue-50 transition-colors",children:"Забронировать корт"}),(0,r.jsx)("button",{onClick:()=>window.location.href="/contact",className:"border-2 border-white text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:text-blue-600 transition-colors",children:"Связаться с нами"})]})]})]}),(0,r.jsx)(l.A,{})]})}},41312:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},56914:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var r=s(65239),a=s(48088),l=s(88170),i=s.n(l),n=s(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let d={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,28770)),"/Users/<USER>/Altius/badminton-club/src/app/about/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Altius/badminton-club/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/Altius/badminton-club/src/app/about/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63611:(e,t,s)=>{Promise.resolve().then(s.bind(s,28770))},64398:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},74227:(e,t,s)=>{Promise.resolve().then(s.bind(s,39236))},79551:e=>{"use strict";e.exports=require("url")},97992:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,169,431,906],()=>s(56914));module.exports=r})();