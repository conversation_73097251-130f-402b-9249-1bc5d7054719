(()=>{var e={};e.id=790,e.ids=[790],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,r,t)=>{"use strict";t.d(r,{ND:()=>a});var s=t(66437);let o="https://whdfkjsmyolbzlwtaoix.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndoZGZranNteW9sYnpsd3Rhb2l4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzNDUxODksImV4cCI6MjA2ODkyMTE4OX0.wodggEz_ElgvYVWPA4o3gmAg84AWezDmnRaHAkC2Dps",n=o&&i&&o.includes("supabase.co")&&i.length>100;console.log("Supabase Configuration:",{url:o?`${o.substring(0,30)}...`:"NOT SET",keyLength:i.length,isConfigured:n});let a=(0,s.UU)(o||"https://placeholder.supabase.co",i||"placeholder-key")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82976:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>l});var o=t(96559),i=t(48088),n=t(37719),a=t(32190),u=t(56621);async function p(){try{let{data:e,error:r}=await u.ND.from("halls").select("*").order("id");if(r)return console.error("Error fetching halls:",r),a.NextResponse.json({error:"Failed to fetch halls"},{status:500});return a.NextResponse.json(e)}catch(e){return console.error("Unexpected error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(e){try{let r=await e.json(),{data:t,error:s}=await u.ND.from("halls").insert([r]).select().single();if(s)return console.error("Error creating hall:",s),a.NextResponse.json({error:"Failed to create hall"},{status:500});return a.NextResponse.json(t)}catch(e){return console.error("Unexpected error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/halls/route",pathname:"/api/halls",filename:"route",bundlePath:"app/api/halls/route"},resolvedPagePath:"/Users/<USER>/Altius/badminton-club/src/app/api/halls/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:x,serverHooks:h}=c;function g(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:x})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,437],()=>t(82976));module.exports=s})();