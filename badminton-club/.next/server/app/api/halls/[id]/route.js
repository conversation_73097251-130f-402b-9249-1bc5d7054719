(()=>{var e={};e.id=596,e.ids=[596],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,r,t)=>{"use strict";t.d(r,{ND:()=>a});var s=t(66437);let o="https://whdfkjsmyolbzlwtaoix.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndoZGZranNteW9sYnpsd3Rhb2l4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzNDUxODksImV4cCI6MjA2ODkyMTE4OX0.wodggEz_ElgvYVWPA4o3gmAg84AWezDmnRaHAkC2Dps",i=o&&n&&o.includes("supabase.co")&&n.length>100;console.log("Supabase Configuration:",{url:o?`${o.substring(0,30)}...`:"NOT SET",keyLength:n.length,isConfigured:i});let a=(0,s.UU)(o||"https://placeholder.supabase.co",n||"placeholder-key")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71918:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{DELETE:()=>c,GET:()=>l,PUT:()=>p});var o=t(96559),n=t(48088),i=t(37719),a=t(32190),u=t(56621);async function l(e,{params:r}){let{id:t}=await r;try{let{data:e,error:r}=await u.ND.from("halls").select("*").eq("id",t).single();if(r)return console.error("Error fetching hall:",r),a.NextResponse.json({error:"Hall not found"},{status:404});return a.NextResponse.json(e)}catch(e){return console.error("Unexpected error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e,{params:r}){try{let{id:t}=await r,s=await e.json(),{data:o,error:n}=await u.ND.from("halls").update(s).eq("id",t).select().single();if(n)return console.error("Error updating hall:",n),a.NextResponse.json({error:"Failed to update hall"},{status:500});return a.NextResponse.json(o)}catch(e){return console.error("Unexpected error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function c(e,{params:r}){try{let{id:e}=await r,{error:t}=await u.ND.from("halls").delete().eq("id",e);if(t)return console.error("Error deleting hall:",t),a.NextResponse.json({error:"Failed to delete hall"},{status:500});return a.NextResponse.json({message:"Hall deleted successfully"})}catch(e){return console.error("Unexpected error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/halls/[id]/route",pathname:"/api/halls/[id]",filename:"route",bundlePath:"app/api/halls/[id]/route"},resolvedPagePath:"/Users/<USER>/Altius/badminton-club/src/app/api/halls/[id]/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:h,serverHooks:g}=d;function f(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:h})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,437],()=>t(71918));module.exports=s})();