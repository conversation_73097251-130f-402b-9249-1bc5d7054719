(()=>{var e={};e.id=953,e.ids=[953],e.modules={3018:(e,t,s)=>{Promise.resolve().then(s.bind(s,89427))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14952:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},16391:(e,t,s)=>{"use strict";s.d(t,{ND:()=>n});var r=s(60463);let l="https://whdfkjsmyolbzlwtaoix.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndoZGZranNteW9sYnpsd3Rhb2l4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzNDUxODksImV4cCI6MjA2ODkyMTE4OX0.wodggEz_ElgvYVWPA4o3gmAg84AWezDmnRaHAkC2Dps",i=l&&a&&l.includes("supabase.co")&&a.length>100;console.log("Supabase Configuration:",{url:l?`${l.substring(0,30)}...`:"NOT SET",keyLength:a.length,isConfigured:i});let n=(0,r.UU)(l||"https://placeholder.supabase.co",a||"placeholder-key")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37360:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},39727:()=>{},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},40479:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var r=s(60687),l=s(43210),a=s(16189),i=s(85814),n=s.n(i),o=s(21891),c=s(51317),d=s(62688);let x=(0,d.A)("zoom-in",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);var h=s(11860),u=s(47033),m=s(14952);function p({images:e,alt:t="Изображение",className:s=""}){let[a,i]=(0,l.useState)(0),[n,o]=(0,l.useState)(!1);if(!e||0===e.length)return null;let c=()=>{i(t=>(t+1)%e.length)},d=()=>{i(t=>(t-1+e.length)%e.length)},p=e=>{i(e)},g=()=>{o(!0)},b=()=>{o(!1)};return 1===e.length?(0,r.jsxs)("div",{className:`relative group ${s}`,children:[(0,r.jsx)("img",{src:e[0],alt:t,className:"w-full h-auto rounded-lg cursor-pointer",onClick:g}),(0,r.jsx)("button",{onClick:g,className:"absolute top-4 right-4 bg-black/50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity",children:(0,r.jsx)(x,{className:"w-4 h-4"})}),n&&(0,r.jsxs)("div",{className:"fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4",children:[(0,r.jsx)("button",{onClick:b,className:"absolute top-4 right-4 text-white p-2 hover:bg-white/20 rounded-full transition-colors",children:(0,r.jsx)(h.A,{className:"w-6 h-6"})}),(0,r.jsx)("img",{src:e[0],alt:t,className:"max-w-full max-h-full object-contain"})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:`relative group ${s}`,children:[(0,r.jsxs)("div",{className:"relative aspect-video overflow-hidden rounded-lg",children:[(0,r.jsx)("img",{src:e[a],alt:`${t} ${a+1}`,className:"w-full h-full object-cover cursor-pointer",onClick:g}),e.length>1&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:d,className:"absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-black/70",children:(0,r.jsx)(u.A,{className:"w-5 h-5"})}),(0,r.jsx)("button",{onClick:c,className:"absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-black/70",children:(0,r.jsx)(m.A,{className:"w-5 h-5"})})]}),(0,r.jsx)("button",{onClick:g,className:"absolute top-4 right-4 bg-black/50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-black/70",children:(0,r.jsx)(x,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{className:"absolute bottom-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm",children:[a+1," / ",e.length]})]}),e.length>1&&(0,r.jsx)("div",{className:"flex gap-2 mt-4 overflow-x-auto pb-2",children:e.map((e,s)=>(0,r.jsx)("button",{onClick:()=>p(s),className:`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all ${s===a?"border-altius-blue shadow-lg":"border-gray-200 hover:border-gray-300"}`,children:(0,r.jsx)("img",{src:e,alt:`${t} ${s+1}`,className:"w-full h-full object-cover"})},s))})]}),n&&(0,r.jsxs)("div",{className:"fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4",children:[(0,r.jsx)("button",{onClick:b,className:"absolute top-4 right-4 text-white p-2 hover:bg-white/20 rounded-full transition-colors z-10",children:(0,r.jsx)(h.A,{className:"w-6 h-6"})}),e.length>1&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:d,className:"absolute left-4 top-1/2 -translate-y-1/2 text-white p-3 hover:bg-white/20 rounded-full transition-colors z-10",children:(0,r.jsx)(u.A,{className:"w-8 h-8"})}),(0,r.jsx)("button",{onClick:c,className:"absolute right-4 top-1/2 -translate-y-1/2 text-white p-3 hover:bg-white/20 rounded-full transition-colors z-10",children:(0,r.jsx)(m.A,{className:"w-8 h-8"})})]}),(0,r.jsx)("img",{src:e[a],alt:`${t} ${a+1}`,className:"max-w-full max-h-full object-contain"}),e.length>1&&(0,r.jsxs)("div",{className:"absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-4 py-2 rounded-full",children:[a+1," / ",e.length]}),e.length>1&&(0,r.jsx)("div",{className:"absolute bottom-4 left-4 right-4 flex justify-center",children:(0,r.jsx)("div",{className:"flex gap-2 overflow-x-auto max-w-md",children:e.map((e,s)=>(0,r.jsx)("button",{onClick:()=>p(s),className:`flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden border-2 transition-all ${s===a?"border-white shadow-lg":"border-white/30 hover:border-white/60"}`,children:(0,r.jsx)("img",{src:e,alt:`${t} ${s+1}`,className:"w-full h-full object-cover"})},s))})})]})]})}var g=s(16391),b=s(28559);let y=(0,d.A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var v=s(13861),j=s(58869),f=s(48730),w=s(40228),N=s(97992),k=s(37360);function A(){let e=(0,a.useParams)(),t=(0,a.useRouter)(),s=e.slug,[i,d]=(0,l.useState)(null),[x,h]=(0,l.useState)(!0),[u,m]=(0,l.useState)([]);(0,l.useCallback)(async()=>{console.log("Loading post with slug:",s),h(!0);try{let{data:e,error:r}=await g.ND.from("posts").select("*").eq("slug",s).eq("status","published").single();if(r)throw console.error("Supabase error:",r),r;e?(console.log("Loaded post from Supabase:",e.title),d(e),await g.ND.from("posts").update({views_count:e.views_count+1}).eq("id",e.id),A(e)):(console.error("Post not found with slug:",s),t.push("/blog"))}catch(e){console.error("Error loading post:",e),t.push("/blog")}finally{h(!1)}},[s,t]);let A=async e=>{try{let{data:t,error:s}=await g.ND.from("posts").select("id, title, slug, excerpt, featured_image, category, created_at").eq("status","published").eq("category",e.category).neq("id",e.id).order("created_at",{ascending:!1}).limit(3);if(s)return void console.error("Error fetching related posts:",s);t&&m(t)}catch(e){console.error("Error fetching related posts:",e)}},_=e=>new Date(e).toLocaleDateString("ru-RU",{year:"numeric",month:"long",day:"numeric"});return x?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(o.A,{}),(0,r.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-altius-blue mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Загрузка поста..."})]})}),(0,r.jsx)(c.A,{})]}):i?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(o.A,{}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)(n(),{href:"/blog",className:"inline-flex items-center text-altius-blue hover:text-blue-700 transition-colors",children:[(0,r.jsx)(b.A,{className:"w-4 h-4 mr-2"}),"Назад к блогу"]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,r.jsxs)("article",{className:"lg:col-span-3 bg-white rounded-xl shadow-lg overflow-hidden",children:[i.featured_image&&(0,r.jsx)("div",{className:"aspect-video overflow-hidden",children:(0,r.jsx)("img",{src:i.featured_image,alt:i.title,className:"w-full h-full object-cover"})}),(0,r.jsxs)("div",{className:"p-8 lg:p-12",children:[(0,r.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-8",children:i.title}),(0,r.jsx)("div",{className:"prose prose-lg prose-blue max-w-none",children:((e,t=[])=>{let s=e.split('<div data-slider="gallery"></div>');return 1===s.length?(0,r.jsx)("div",{dangerouslySetInnerHTML:{__html:e}}):(0,r.jsx)("div",{children:s.map((e,l)=>(0,r.jsxs)("div",{children:[e&&(0,r.jsx)("div",{dangerouslySetInnerHTML:{__html:e}}),l<s.length-1&&t.length>0&&(0,r.jsx)("div",{className:"my-8",children:(0,r.jsx)(p,{images:t,alt:i?.title||"Gallery",className:"rounded-lg"})})]},l))})})(i.content,i.gallery_images)})]})]}),(0,r.jsxs)("aside",{className:"lg:col-span-1 space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Информация"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${"event"===i.category?"bg-altius-orange text-white":"bg-altius-lime text-white"}`,children:"event"===i.category?"Событие":"Новость"}),(0,r.jsx)("button",{onClick:()=>{navigator.share&&i?navigator.share({title:i.title,text:i.excerpt||"",url:window.location.href}):(navigator.clipboard.writeText(window.location.href),alert("Ссылка скопирована в буфер обмена!"))},className:"flex items-center text-gray-500 hover:text-altius-blue transition-colors",children:(0,r.jsx)(y,{className:"w-5 h-5"})})]}),(0,r.jsxs)("div",{className:"flex items-center text-gray-500 text-sm",children:[(0,r.jsx)(v.A,{className:"w-4 h-4 mr-2"}),(0,r.jsxs)("span",{children:[i.views_count+1," просмотров"]})]}),(0,r.jsxs)("div",{className:"flex items-center text-gray-500 text-sm",children:[(0,r.jsx)(j.A,{className:"w-4 h-4 mr-2"}),(0,r.jsx)("span",{children:i.author_name})]}),(0,r.jsxs)("div",{className:"flex items-center text-gray-500 text-sm",children:[(0,r.jsx)(f.A,{className:"w-4 h-4 mr-2"}),(0,r.jsx)("span",{children:_(i.created_at)})]})]})]}),"event"===i.category&&(0,r.jsxs)("div",{className:"bg-altius-orange/10 border border-altius-orange/20 rounded-xl p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-altius-orange mb-4",children:"Детали события"}),(0,r.jsxs)("div",{className:"space-y-3",children:[i.event_date&&(0,r.jsxs)("div",{className:"flex items-start text-gray-700",children:[(0,r.jsx)(w.A,{className:"w-5 h-5 mr-3 text-altius-orange mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium block",children:"Дата и время:"}),(0,r.jsx)("span",{className:"text-sm",children:new Date(i.event_date).toLocaleDateString("ru-RU",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})]})]}),i.event_location&&(0,r.jsxs)("div",{className:"flex items-start text-gray-700",children:[(0,r.jsx)(N.A,{className:"w-5 h-5 mr-3 text-altius-orange mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium block",children:"Место:"}),(0,r.jsx)("span",{className:"text-sm",children:i.event_location})]})]})]})]}),i.tags&&i.tags.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Теги"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:i.tags.map((e,t)=>(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md bg-gray-100 text-gray-700 text-sm",children:[(0,r.jsx)(k.A,{className:"w-3 h-3 mr-1"}),e]},t))})]})]})]}),u.length>0&&(0,r.jsxs)("section",{className:"mt-12",children:[(0,r.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 mb-8",children:["Похожие ","event"===i.category?"события":"новости"]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:u.map(e=>(0,r.jsxs)("article",{className:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow",children:[e.featured_image&&(0,r.jsx)("div",{className:"aspect-video overflow-hidden",children:(0,r.jsx)("img",{src:e.featured_image,alt:e.title,className:"w-full h-full object-cover hover:scale-105 transition-transform duration-300"})}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)("span",{className:`inline-block px-2 py-1 rounded-full text-xs font-medium mb-2 ${"event"===e.category?"bg-altius-orange text-white":"bg-altius-lime text-white"}`,children:"event"===e.category?"Событие":"Новость"}),(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-2 line-clamp-2",children:(0,r.jsx)(n(),{href:`/blog/${e.slug}`,className:"hover:text-altius-blue transition-colors",children:e.title})}),e.excerpt&&(0,r.jsx)("p",{className:"text-gray-600 text-sm line-clamp-2 mb-3",children:e.excerpt}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:_(e.created_at)})]})]},e.id))})]})]}),(0,r.jsx)(c.A,{})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(o.A,{}),(0,r.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Пост не найден"}),(0,r.jsx)(n(),{href:"/blog",className:"text-altius-blue hover:text-blue-700",children:"Вернуться к блогу"})]})}),(0,r.jsx)(c.A,{})]})}},47033:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66066:(e,t,s)=>{Promise.resolve().then(s.bind(s,40479))},74075:e=>{"use strict";e.exports=require("zlib")},76716:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>c});var r=s(65239),l=s(48088),a=s(88170),i=s.n(a),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c={children:["",{children:["blog",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,89427)),"/Users/<USER>/Altius/badminton-club/src/app/blog/[slug]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Altius/badminton-club/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Altius/badminton-club/src/app/blog/[slug]/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/blog/[slug]/page",pathname:"/blog/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89427:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Altius/badminton-club/src/app/blog/[slug]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Altius/badminton-club/src/app/blog/[slug]/page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,169,431,957,906],()=>s(76716));module.exports=r})();