exports.id=906,exports.ids=[906],exports.modules={21891:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var r=t(60687),i=t(43210),l=t(16189),a=t(85814),n=t.n(a),c=t(48340),o=t(97992),x=t(11860),d=t(12941);function m(){let[e,s]=(0,i.useState)(!1),t=(0,l.useRouter)(),a=(0,l.usePathname)(),m=e=>{if(e.startsWith("#"))if("/"!==a)t.push("/"+e);else{let s=document.querySelector(e);s&&s.scrollIntoView({behavior:"smooth"})}else t.push(e);s(!1)};return(0,r.jsx)("header",{className:"bg-white shadow-lg sticky top-0 z-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,r.jsxs)(n(),{href:"/",className:"flex items-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-altius-blue",children:"\uD83C\uDFF8 Altius"}),(0,r.jsx)("div",{className:"ml-2 text-sm text-gray-600 hidden sm:block",children:"Кишинев"})]}),(0,r.jsxs)("nav",{className:"hidden md:flex space-x-8",children:[(0,r.jsx)("button",{onClick:()=>m("/"),className:"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer",children:"Главная"}),(0,r.jsx)("button",{onClick:()=>m("/about"),className:"text-gray-700 hover:text-altius-lime transition-colors cursor-pointer",children:"О нас"}),(0,r.jsx)("button",{onClick:()=>m("#halls"),className:"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer",children:"Залы"}),(0,r.jsx)("button",{onClick:()=>m("/services"),className:"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer",children:"Услуги"}),(0,r.jsx)("button",{onClick:()=>m("/contact"),className:"text-gray-700 hover:text-altius-lime transition-colors cursor-pointer",children:"Контакты"}),(0,r.jsx)("button",{onClick:()=>m("/blog"),className:"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer",children:"Блог"}),(0,r.jsx)(n(),{href:"/admin",className:"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer",children:"Админ"})]}),(0,r.jsxs)("div",{className:"hidden lg:flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 mr-1"}),"+373 XX XXX XXX"]}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 mr-1"}),"Кишинев"]})]}),(0,r.jsx)("button",{className:"md:hidden cursor-pointer",onClick:()=>s(!e),children:e?(0,r.jsx)(x.A,{className:"w-6 h-6 text-gray-700"}):(0,r.jsx)(d.A,{className:"w-6 h-6 text-gray-700"})})]}),e&&(0,r.jsx)("div",{className:"md:hidden py-4 border-t border-gray-200",children:(0,r.jsxs)("nav",{className:"flex flex-col space-y-4",children:[(0,r.jsx)("button",{onClick:()=>m("/"),className:"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer",children:"Главная"}),(0,r.jsx)("button",{onClick:()=>m("/about"),className:"text-gray-700 hover:text-altius-lime transition-colors text-left cursor-pointer",children:"О нас"}),(0,r.jsx)("button",{onClick:()=>m("#halls"),className:"text-gray-700 hover:text-altius-orange transition-colors text-left cursor-pointer",children:"Залы"}),(0,r.jsx)("button",{onClick:()=>m("/services"),className:"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer",children:"Услуги"}),(0,r.jsx)("button",{onClick:()=>m("/contact"),className:"text-gray-700 hover:text-altius-lime transition-colors text-left cursor-pointer",children:"Контакты"}),(0,r.jsx)("button",{onClick:()=>m("/blog"),className:"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer",children:"Блог"}),(0,r.jsx)(n(),{href:"/admin",className:"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer",children:"Админ"}),(0,r.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600 mb-2",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 mr-1"}),"+373 XX XXX XXX"]}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 mr-1"}),"Кишинев"]})]})]})})]})})}},33502:()=>{},36700:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},38230:()=>{},51317:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var r=t(60687),i=t(97992),l=t(48340),a=t(41550),n=t(48730);function c(){return(0,r.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-altius-lime mb-4",children:"\uD83C\uDFF8 Altius"}),(0,r.jsx)("p",{className:"text-gray-300 mb-4",children:"Современный бадминтонный клуб в Кишиневе с профессиональными кортами и удобной системой бронирования."}),(0,r.jsxs)("div",{className:"flex items-center text-gray-300 mb-2",children:[(0,r.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Кишинев, Молдова"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Контакты"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,r.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"+373 XX XXX XXX"]}),(0,r.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,r.jsx)(a.A,{className:"w-4 h-4 mr-2"}),"<EMAIL>"]}),(0,r.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,r.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"ул. Примерная, 123, Кишинев"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Режим работы"}),(0,r.jsx)("div",{className:"space-y-2",children:(0,r.jsxs)("div",{className:"flex items-center text-gray-300",children:[(0,r.jsx)(n.A,{className:"w-4 h-4 mr-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:"Пн-Пт: 06:00 - 23:00"}),(0,r.jsx)("div",{children:"Сб-Вс: 08:00 - 22:00"})]})]})}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"Наши залы:"}),(0,r.jsxs)("div",{className:"text-sm text-gray-300 space-y-1",children:[(0,r.jsx)("div",{children:"Зал 1: 3 корта"}),(0,r.jsx)("div",{children:"Зал 2: 7 кортов"}),(0,r.jsx)("div",{children:"Зал 3: 7 кортов"})]})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:(0,r.jsx)("p",{children:"\xa9 2024 Altius Кишинев. Все права защищены."})})]})})}},61135:()=>{},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},94431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o,metadata:()=>c});var r=t(37413),i=t(2202),l=t.n(i),a=t(64988),n=t.n(a);t(61135);let c={title:"BadmintonClub Кишинев - Бронирование кортов",description:"Современный бадминтонный клуб в Кишиневе. 17 профессиональных кортов в 3 залах. Удобная система онлайн бронирования."};function o({children:e}){return(0,r.jsx)("html",{lang:"ru",children:(0,r.jsx)("body",{className:`${l().variable} ${n().variable} antialiased`,children:e})})}},94844:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))}};