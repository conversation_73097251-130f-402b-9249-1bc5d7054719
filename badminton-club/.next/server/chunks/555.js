exports.id=555,exports.ids=[555],exports.modules={8819:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9005:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},10022:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16023:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},16391:(e,t,s)=>{"use strict";s.d(t,{ND:()=>o});var r=s(60463);let a="https://whdfkjsmyolbzlwtaoix.supabase.co",l="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndoZGZranNteW9sYnpsd3Rhb2l4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzNDUxODksImV4cCI6MjA2ODkyMTE4OX0.wodggEz_ElgvYVWPA4o3gmAg84AWezDmnRaHAkC2Dps",i=a&&l&&a.includes("supabase.co")&&l.length>100;console.log("Supabase Configuration:",{url:a?`${a.substring(0,30)}...`:"NOT SET",keyLength:l.length,isConfigured:i});let o=(0,r.UU)(a||"https://placeholder.supabase.co",l||"placeholder-key")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},37360:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},39727:()=>{},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},47990:()=>{},53768:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(60687),a=s(43210),l=s(16391);let i=(0,s(62688).A)("move",[["path",{d:"M12 2v20",key:"t6zp3m"}],["path",{d:"m15 19-3 3-3-3",key:"11eu04"}],["path",{d:"m19 9 3 3-3 3",key:"1mg7y2"}],["path",{d:"M2 12h20",key:"9i4pu4"}],["path",{d:"m5 9-3 3 3 3",key:"j64kie"}],["path",{d:"m9 5 3-3 3 3",key:"l8vdw6"}]]);var o=s(11860),c=s(9005),d=s(16023),n=s(96474);function h({images:e,onImagesUpdate:t}){let[s,h]=(0,a.useState)(!1),[m,p]=(0,a.useState)(!1),[u,x]=(0,a.useState)(""),g=async s=>{if(s){if(!s.type.startsWith("image/"))return void alert("Файл должен быть изображением");if(s.size>5242880)return void alert("Файл слишком большой. Максимальный размер: 5MB");h(!0);try{let r=s.name.split(".").pop(),a=`posts/gallery/${Date.now()}-${Math.random().toString(36).substring(2)}.${r}`,{error:i}=await l.ND.storage.from("post-images").upload(a,s,{cacheControl:"3600",upsert:!1});if(i){console.error("Upload error:",i),alert(`Ошибка загрузки: ${i.message}`);return}let{data:o}=l.ND.storage.from("post-images").getPublicUrl(a),c=[...e,o.publicUrl];t(c),alert("Изображение успешно добавлено в галерею!")}catch(e){console.error("Error uploading image:",e),alert("Произошла ошибка при загрузке изображения")}finally{h(!1)}}},b=s=>{t(e.filter((e,t)=>t!==s))},y=(s,r)=>{let a=[...e],[l]=a.splice(s,1);a.splice(r,0,l),t(a)};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["Галерея изображений (",e.length,")"]}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"Для слайдера в посте"})]}),e.length>0&&(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:e.map((t,s)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("img",{src:t,alt:`Gallery image ${s+1}`,className:"w-full h-32 object-cover rounded-lg",onError:e=>{e.currentTarget.style.display="none"}}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[s>0&&(0,r.jsx)("button",{onClick:()=>y(s,s-1),className:"p-1 bg-white/20 text-white rounded hover:bg-white/30 transition-colors",title:"Переместить влево",children:(0,r.jsx)(i,{className:"w-4 h-4 rotate-180"})}),s<e.length-1&&(0,r.jsx)("button",{onClick:()=>y(s,s+1),className:"p-1 bg-white/20 text-white rounded hover:bg-white/30 transition-colors",title:"Переместить вправо",children:(0,r.jsx)(i,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:()=>b(s),className:"p-1 bg-red-500/80 text-white rounded hover:bg-red-600 transition-colors",title:"Удалить",children:(0,r.jsx)(o.A,{className:"w-4 h-4"})})]})}),(0,r.jsx)("div",{className:"absolute top-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded",children:s+1})]},s))}),(0,r.jsx)("div",{className:`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${m?"border-altius-blue bg-blue-50":"border-gray-300 hover:border-gray-400"} ${s?"opacity-50 pointer-events-none":""}`,onDrop:e=>{e.preventDefault(),p(!1);let t=Array.from(e.dataTransfer.files);t.length>0&&g(t[0])},onDragOver:e=>{e.preventDefault(),p(!0)},onDragLeave:()=>p(!1),children:s?(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-altius-blue mb-2"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Загрузка изображения..."})]}):(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(c.A,{className:"w-12 h-12 text-gray-400 mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 mb-2",children:"Перетащите изображение сюда или"}),(0,r.jsxs)("label",{className:"cursor-pointer bg-altius-blue text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Выберите файл",(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let t=e.target.files;t&&t.length>0&&g(t[0])},className:"hidden"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"PNG, JPG, GIF до 5MB"})]})}),(0,r.jsxs)("div",{className:"border-t pt-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Добавить изображение по URL"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("input",{type:"url",value:u,onChange:e=>x(e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-altius-blue focus:border-transparent",placeholder:"https://example.com/image.jpg"}),(0,r.jsxs)("button",{onClick:()=>{u.trim()&&(t([...e,u.trim()]),x(""))},disabled:!u.trim(),className:"px-4 py-2 bg-altius-lime text-white rounded-lg hover:bg-lime-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:[(0,r.jsx)(n.A,{className:"w-4 h-4 mr-1"}),"Добавить"]})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Как использовать в посте:"}),(0,r.jsx)("p",{className:"text-sm text-blue-800 mb-2",children:"Добавьте в содержание поста следующий код для отображения слайдера:"}),(0,r.jsx)("code",{className:"block bg-blue-100 text-blue-900 p-2 rounded text-sm font-mono",children:'<div data-slider="gallery"></div>'}),(0,r.jsx)("p",{className:"text-xs text-blue-700 mt-2",children:"Слайдер автоматически отобразит все изображения из галереи"})]})]})}},58869:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},69282:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("type",[["path",{d:"M12 4v16",key:"1654pz"}],["path",{d:"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2",key:"e0r10z"}],["path",{d:"M9 20h6",key:"s66wpe"}]])},75313:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(60687),a=s(43210),l=s(16391),i=s(11860),o=s(9005),c=s(16023);function d({currentImage:e,onImageUpdate:t}){let[s,d]=(0,a.useState)(!1),[n,h]=(0,a.useState)(!1),m=async e=>{if(e){if(!e.type.startsWith("image/"))return void alert("Файл должен быть изображением");if(e.size>5242880)return void alert("Файл слишком большой. Максимальный размер: 5MB");d(!0);try{let s=e.name.split(".").pop(),r=`posts/${Date.now()}-${Math.random().toString(36).substring(2)}.${s}`,{error:a}=await l.ND.storage.from("post-images").upload(r,e,{cacheControl:"3600",upsert:!1});if(a){console.error("Upload error:",a),alert(`Ошибка загрузки: ${a.message}`);return}let{data:i}=l.ND.storage.from("post-images").getPublicUrl(r);t(i.publicUrl),alert("Изображение успешно загружено!")}catch(e){console.error("Error uploading image:",e),alert("Произошла ошибка при загрузке изображения")}finally{d(!1)}}};return(0,r.jsxs)("div",{className:"space-y-4",children:[e&&(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("img",{src:e,alt:"Featured image",className:"w-full h-48 object-cover rounded-lg",onError:e=>{e.currentTarget.style.display="none"}}),(0,r.jsx)("button",{onClick:()=>{t("")},className:"absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors",title:"Удалить изображение",children:(0,r.jsx)(i.A,{className:"w-4 h-4"})})]}),(0,r.jsx)("div",{className:`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${n?"border-altius-blue bg-blue-50":"border-gray-300 hover:border-gray-400"} ${s?"opacity-50 pointer-events-none":""}`,onDrop:e=>{e.preventDefault(),h(!1);let t=Array.from(e.dataTransfer.files);t.length>0&&m(t[0])},onDragOver:e=>{e.preventDefault(),h(!0)},onDragLeave:()=>h(!1),children:s?(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-altius-blue mb-2"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Загрузка изображения..."})]}):(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(o.A,{className:"w-12 h-12 text-gray-400 mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 mb-2",children:"Перетащите изображение сюда или"}),(0,r.jsxs)("label",{className:"cursor-pointer bg-altius-blue text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Выберите файл",(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let t=e.target.files;t&&t.length>0&&m(t[0])},className:"hidden"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"PNG, JPG, GIF до 5MB"})]})}),(0,r.jsx)("div",{className:"text-center text-gray-500",children:(0,r.jsx)("span",{className:"text-sm",children:"или"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"URL изображения"}),(0,r.jsx)("input",{type:"url",value:e,onChange:e=>t(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-altius-blue focus:border-transparent",placeholder:"https://example.com/image.jpg"})]})]})}},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};