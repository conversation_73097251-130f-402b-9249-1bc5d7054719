{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Menu, X, Phone, MapPin } from 'lucide-react';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl font-bold text-blue-600\">\n              🏸 BadmintonClub\n            </div>\n            <div className=\"ml-2 text-sm text-gray-600 hidden sm:block\">\n              Кишинев\n            </div>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <a href=\"#home\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Главная\n            </a>\n            <a href=\"#halls\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Залы\n            </a>\n            <a href=\"#booking\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Бронирование\n            </a>\n            <a href=\"#contact\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Контакты\n            </a>\n            <a href=\"/admin\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Админ\n            </a>\n          </nav>\n\n          {/* Contact Info */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Phone className=\"w-4 h-4 mr-1\" />\n              +373 XX XXX XXX\n            </div>\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <MapPin className=\"w-4 h-4 mr-1\" />\n              Кишинев\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {isMenuOpen ? (\n              <X className=\"w-6 h-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"w-6 h-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <a href=\"#home\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Главная\n              </a>\n              <a href=\"#halls\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Залы\n              </a>\n              <a href=\"#booking\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Бронирование\n              </a>\n              <a href=\"#contact\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Контакты\n              </a>\n              <a href=\"/admin\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Админ\n              </a>\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center text-sm text-gray-600 mb-2\">\n                  <Phone className=\"w-4 h-4 mr-1\" />\n                  +373 XX XXX XXX\n                </div>\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <MapPin className=\"w-4 h-4 mr-1\" />\n                  Кишинев\n                </div>\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAmC;;;;;;8CAGlD,8OAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAQ,WAAU;8CAAsD;;;;;;8CAGhF,8OAAC;oCAAE,MAAK;oCAAS,WAAU;8CAAsD;;;;;;8CAGjF,8OAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAsD;;;;;;8CAGnF,8OAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAsD;;;;;;8CAGnF,8OAAC;oCAAE,MAAK;oCAAS,WAAU;8CAAsD;;;;;;;;;;;;sCAMnF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE7B,2BACC,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,MAAK;gCAAQ,WAAU;0CAAsD;;;;;;0CAGhF,8OAAC;gCAAE,MAAK;gCAAS,WAAU;0CAAsD;;;;;;0CAGjF,8OAAC;gCAAE,MAAK;gCAAW,WAAU;0CAAsD;;;;;;0CAGnF,8OAAC;gCAAE,MAAK;gCAAW,WAAU;0CAAsD;;;;;;0CAGnF,8OAAC;gCAAE,MAAK;gCAAS,WAAU;0CAAsD;;;;;;0CAGjF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Footer.tsx"], "sourcesContent": ["import { Phone, MapPin, Clock, Mail } from 'lucide-react';\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {/* Company Info */}\n          <div>\n            <div className=\"text-2xl font-bold text-blue-400 mb-4\">\n              🏸 BadmintonClub\n            </div>\n            <p className=\"text-gray-300 mb-4\">\n              Современный бадминтонный клуб в Кишиневе с профессиональными кортами \n              и удобной системой бронирования.\n            </p>\n            <div className=\"flex items-center text-gray-300 mb-2\">\n              <MapPin className=\"w-4 h-4 mr-2\" />\n              Кишинев, Молдова\n            </div>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Контакты</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center text-gray-300\">\n                <Phone className=\"w-4 h-4 mr-2\" />\n                +373 XX XXX XXX\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <Mail className=\"w-4 h-4 mr-2\" />\n                <EMAIL>\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <MapPin className=\"w-4 h-4 mr-2\" />\n                ул. Примерная, 123, Кишинев\n              </div>\n            </div>\n          </div>\n\n          {/* Working Hours */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Режим работы</h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-gray-300\">\n                <Clock className=\"w-4 h-4 mr-2\" />\n                <div>\n                  <div>Пн-Пт: 06:00 - 23:00</div>\n                  <div>Сб-Вс: 08:00 - 22:00</div>\n                </div>\n              </div>\n            </div>\n            \n            {/* Halls Info */}\n            <div className=\"mt-6\">\n              <h4 className=\"font-semibold mb-2\">Наши залы:</h4>\n              <div className=\"text-sm text-gray-300 space-y-1\">\n                <div>Зал 1: 3 корта</div>\n                <div>Зал 2: 7 кортов</div>\n                <div>Зал 3: 7 кортов</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n          <p>&copy; 2024 BadmintonClub Кишинев. Все права защищены.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;8CAGvD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOzC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;kEAAI;;;;;;kEACL,8OAAC;kEAAI;;;;;;;;;;;;;;;;;;;;;;;8CAMX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/HallCard.tsx"], "sourcesContent": ["import { Users, MapPin, Calendar, Eye } from 'lucide-react';\nimport Link from 'next/link';\n\ninterface HallCardProps {\n  id: number;\n  name: string;\n  courts: number;\n  image: string;\n  description: string;\n  features: string[];\n  pricePerHour: number;\n  onBookClick: (hallId: number) => void;\n}\n\nexport default function HallCard({\n  id,\n  name,\n  courts,\n  image,\n  description,\n  features,\n  pricePerHour,\n  onBookClick\n}: HallCardProps) {\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\">\n      {/* Image */}\n      <div className=\"relative h-64 bg-gradient-to-br from-blue-500 to-blue-700\">\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"text-center text-white\">\n            <div className=\"text-6xl mb-2\">🏸</div>\n            <div className=\"text-xl font-semibold\">{name}</div>\n          </div>\n        </div>\n        <div className=\"absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1\">\n          <div className=\"flex items-center text-sm font-semibold text-gray-700\">\n            <Users className=\"w-4 h-4 mr-1\" />\n            {courts} кортов\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-6\">\n        <h3 className=\"text-xl font-bold text-gray-900 mb-2\">{name}</h3>\n        <p className=\"text-gray-600 mb-4\">{description}</p>\n\n        {/* Features */}\n        <div className=\"mb-4\">\n          <h4 className=\"text-sm font-semibold text-gray-700 mb-2\">Особенности:</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            {features.map((feature, index) => (\n              <li key={index} className=\"flex items-center\">\n                <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full mr-2\"></div>\n                {feature}\n              </li>\n            ))}\n          </ul>\n        </div>\n\n        {/* Price and Location */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"text-lg font-bold text-blue-600\">\n            {pricePerHour} лей/час\n          </div>\n          <div className=\"flex items-center text-sm text-gray-500\">\n            <MapPin className=\"w-4 h-4 mr-1\" />\n            Кишинев\n          </div>\n        </div>\n\n        {/* Book Button */}\n        <button\n          onClick={() => onBookClick(id)}\n          className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center\"\n        >\n          <Calendar className=\"w-4 h-4 mr-2\" />\n          Забронировать\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;;;AAce,SAAS,SAAS,EAC/B,EAAE,EACF,IAAI,EACJ,MAAM,EACN,KAAK,EACL,WAAW,EACX,QAAQ,EACR,YAAY,EACZ,WAAW,EACG;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAI,WAAU;8CAAyB;;;;;;;;;;;;;;;;;kCAG5C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAChB;gCAAO;;;;;;;;;;;;;;;;;;0BAMd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAsB;;;;;;kCAGnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAG,WAAU;0CACX,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC;gDAAI,WAAU;;;;;;4CACd;;uCAFM;;;;;;;;;;;;;;;;kCASf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ;oCAAa;;;;;;;0CAEhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMvC,8OAAC;wBACC,SAAS,IAAM,YAAY;wBAC3B,WAAU;;0CAEV,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAM/C", "debugId": null}}, {"offset": {"line": 804, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/BookingCalendar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { format, addDays, startOfWeek, isSameDay, isToday } from 'date-fns';\nimport { ru } from 'date-fns/locale';\nimport { ChevronLeft, ChevronRight, Clock } from 'lucide-react';\n\ninterface TimeSlot {\n  time: string;\n  available: boolean;\n  court?: number;\n}\n\ninterface BookingCalendarProps {\n  hallId: number;\n  onSlotSelect: (date: Date, time: string, court: number) => void;\n}\n\nexport default function BookingCalendar({ hallId, onSlotSelect }: BookingCalendarProps) {\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [currentWeek, setCurrentWeek] = useState(startOfWeek(new Date(), { weekStartsOn: 1 }));\n\n  // Генерируем временные слоты с 6:00 до 23:00\n  const timeSlots: string[] = [];\n  for (let hour = 6; hour <= 22; hour++) {\n    timeSlots.push(`${hour.toString().padStart(2, '0')}:00`);\n    timeSlots.push(`${hour.toString().padStart(2, '0')}:30`);\n  }\n\n  // Получаем количество кортов для зала\n  const getCourtCount = (hallId: number) => {\n    switch (hallId) {\n      case 1: return 3;\n      case 2: return 7;\n      case 3: return 7;\n      default: return 3;\n    }\n  };\n\n  const courtCount = getCourtCount(hallId);\n\n  // Генерируем дни недели\n  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(currentWeek, i));\n\n  // Симуляция занятых слотов (в реальном приложении это будет из API)\n  const isSlotBooked = (date: Date, time: string, court: number) => {\n    // Случайная логика для демонстрации\n    const dateStr = format(date, 'yyyy-MM-dd');\n    const key = `${dateStr}-${time}-${court}`;\n    return Math.random() > 0.7; // 30% слотов заняты\n  };\n\n  const goToPreviousWeek = () => {\n    setCurrentWeek(addDays(currentWeek, -7));\n  };\n\n  const goToNextWeek = () => {\n    setCurrentWeek(addDays(currentWeek, 7));\n  };\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg p-6\">\n      <div className=\"mb-6\">\n        <h3 className=\"text-xl font-bold text-gray-900 mb-2\">\n          Выберите дату и время\n        </h3>\n        <p className=\"text-gray-600\">\n          Зал {hallId} • {courtCount} кортов\n        </p>\n      </div>\n\n      {/* Week Navigation */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <button\n          onClick={goToPreviousWeek}\n          className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n        >\n          <ChevronLeft className=\"w-5 h-5\" />\n        </button>\n        \n        <div className=\"text-lg font-semibold\">\n          {format(currentWeek, 'MMMM yyyy', { locale: ru })}\n        </div>\n        \n        <button\n          onClick={goToNextWeek}\n          className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n        >\n          <ChevronRight className=\"w-5 h-5\" />\n        </button>\n      </div>\n\n      {/* Days of Week */}\n      <div className=\"grid grid-cols-7 gap-2 mb-4\">\n        {weekDays.map((day, index) => (\n          <button\n            key={index}\n            onClick={() => setSelectedDate(day)}\n            className={`p-3 text-center rounded-lg transition-colors ${\n              isSameDay(day, selectedDate)\n                ? 'bg-blue-600 text-white'\n                : isToday(day)\n                ? 'bg-blue-100 text-blue-600'\n                : 'hover:bg-gray-100'\n            }`}\n          >\n            <div className=\"text-xs text-gray-500 mb-1\">\n              {format(day, 'EEE', { locale: ru })}\n            </div>\n            <div className=\"font-semibold\">\n              {format(day, 'd')}\n            </div>\n          </button>\n        ))}\n      </div>\n\n      {/* Time Slots */}\n      <div className=\"max-h-96 overflow-y-auto\">\n        <div className=\"space-y-2\">\n          {timeSlots.map((time) => (\n            <div key={time} className=\"border rounded-lg p-3\">\n              <div className=\"flex items-center mb-2\">\n                <Clock className=\"w-4 h-4 mr-2 text-gray-500\" />\n                <span className=\"font-medium\">{time}</span>\n              </div>\n              \n              <div className=\"grid grid-cols-3 gap-2\">\n                {Array.from({ length: courtCount }, (_, courtIndex) => {\n                  const court = courtIndex + 1;\n                  const isBooked = isSlotBooked(selectedDate, time, court);\n                  \n                  return (\n                    <button\n                      key={court}\n                      onClick={() => !isBooked && onSlotSelect(selectedDate, time, court)}\n                      disabled={isBooked}\n                      className={`p-2 text-sm rounded transition-colors ${\n                        isBooked\n                          ? 'bg-red-100 text-red-600 cursor-not-allowed'\n                          : 'bg-green-100 text-green-600 hover:bg-green-200'\n                      }`}\n                    >\n                      Корт {court}\n                      {isBooked && (\n                        <div className=\"text-xs\">Занят</div>\n                      )}\n                    </button>\n                  );\n                })}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AALA;;;;;;AAkBe,SAAS,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAwB;IACpF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,EAAE,IAAI,QAAQ;QAAE,cAAc;IAAE;IAEzF,6CAA6C;IAC7C,MAAM,YAAsB,EAAE;IAC9B,IAAK,IAAI,OAAO,GAAG,QAAQ,IAAI,OAAQ;QACrC,UAAU,IAAI,CAAC,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;QACvD,UAAU,IAAI,CAAC,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;IACzD;IAEA,sCAAsC;IACtC,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,cAAc;IAEjC,wBAAwB;IACxB,MAAM,WAAW,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAE,GAAG,CAAC,GAAG,IAAM,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAE1E,oEAAoE;IACpE,MAAM,eAAe,CAAC,MAAY,MAAc;QAC9C,oCAAoC;QACpC,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QAC7B,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,KAAK,CAAC,EAAE,OAAO;QACzC,OAAO,KAAK,MAAM,KAAK,KAAK,oBAAoB;IAClD;IAEA,MAAM,mBAAmB;QACvB,eAAe,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,aAAa,CAAC;IACvC;IAEA,MAAM,eAAe;QACnB,eAAe,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IACtC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;kCAGrD,8OAAC;wBAAE,WAAU;;4BAAgB;4BACtB;4BAAO;4BAAI;4BAAW;;;;;;;;;;;;;0BAK/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAGzB,8OAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,aAAa,aAAa;4BAAE,QAAQ,2IAAA,CAAA,KAAE;wBAAC;;;;;;kCAGjD,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAK5B,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,8OAAC;wBAEC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,6CAA6C,EACvD,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,KAAK,gBACX,2BACA,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,OACR,8BACA,qBACJ;;0CAEF,8OAAC;gCAAI,WAAU;0CACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,OAAO;oCAAE,QAAQ,2IAAA,CAAA,KAAE;gCAAC;;;;;;0CAEnC,8OAAC;gCAAI,WAAU;0CACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK;;;;;;;uBAdV;;;;;;;;;;0BAqBX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;4BAAe,WAAU;;8CACxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAGjC,8OAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAW,GAAG,CAAC,GAAG;wCACtC,MAAM,QAAQ,aAAa;wCAC3B,MAAM,WAAW,aAAa,cAAc,MAAM;wCAElD,qBACE,8OAAC;4CAEC,SAAS,IAAM,CAAC,YAAY,aAAa,cAAc,MAAM;4CAC7D,UAAU;4CACV,WAAW,CAAC,sCAAsC,EAChD,WACI,+CACA,kDACJ;;gDACH;gDACO;gDACL,0BACC,8OAAC;oDAAI,WAAU;8DAAU;;;;;;;2CAXtB;;;;;oCAeX;;;;;;;2BA5BM;;;;;;;;;;;;;;;;;;;;;AAoCtB", "debugId": null}}, {"offset": {"line": 1078, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/BookingForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { format } from 'date-fns';\nimport { ru } from 'date-fns/locale';\nimport { User, Phone, Calendar, Clock, MapPin, CreditCard, QrCode } from 'lucide-react';\n\nconst bookingSchema = z.object({\n  name: z.string().min(2, 'Имя должно содержать минимум 2 символа'),\n  phone: z.string().min(8, 'Введите корректный номер телефона'),\n  email: z.string().email('Введите корректный email').optional().or(z.literal('')),\n});\n\ntype BookingFormData = z.infer<typeof bookingSchema>;\n\ninterface BookingFormProps {\n  hallId: number;\n  date: Date;\n  time: string;\n  court: number;\n  onSubmit: (data: BookingFormData & { hallId: number; date: Date; time: string; court: number }) => void;\n  onCancel: () => void;\n}\n\nexport default function BookingForm({ hallId, date, time, court, onSubmit, onCancel }: BookingFormProps) {\n  const [showPayment, setShowPayment] = useState(false);\n  const [paymentMethod, setPaymentMethod] = useState<'qr' | 'transfer'>('qr');\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting }\n  } = useForm<BookingFormData>({\n    resolver: zodResolver(bookingSchema)\n  });\n\n  const pricePerHour = 150; // лей за час\n\n  const handleFormSubmit = (data: BookingFormData) => {\n    setShowPayment(true);\n  };\n\n  const handlePaymentConfirm = () => {\n    const formData = new FormData(document.querySelector('form') as HTMLFormElement);\n    const data = {\n      name: formData.get('name') as string,\n      phone: formData.get('phone') as string,\n      email: formData.get('email') as string,\n      hallId,\n      date,\n      time,\n      court\n    };\n    onSubmit(data);\n  };\n\n  if (showPayment) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-lg p-6\">\n        <h3 className=\"text-xl font-bold text-gray-900 mb-6\">Оплата бронирования</h3>\n        \n        {/* Booking Summary */}\n        <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n          <h4 className=\"font-semibold mb-2\">Детали бронирования:</h4>\n          <div className=\"space-y-1 text-sm text-gray-600\">\n            <div>Зал {hallId}, Корт {court}</div>\n            <div>{format(date, 'dd MMMM yyyy', { locale: ru })}</div>\n            <div>{time}</div>\n            <div className=\"font-semibold text-lg text-blue-600 mt-2\">\n              Стоимость: {pricePerHour} лей\n            </div>\n          </div>\n        </div>\n\n        {/* Payment Method Selection */}\n        <div className=\"mb-6\">\n          <h4 className=\"font-semibold mb-3\">Способ оплаты:</h4>\n          <div className=\"grid grid-cols-2 gap-3\">\n            <button\n              onClick={() => setPaymentMethod('qr')}\n              className={`p-3 border rounded-lg flex items-center justify-center ${\n                paymentMethod === 'qr' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'\n              }`}\n            >\n              <QrCode className=\"w-5 h-5 mr-2\" />\n              QR-код\n            </button>\n            <button\n              onClick={() => setPaymentMethod('transfer')}\n              className={`p-3 border rounded-lg flex items-center justify-center ${\n                paymentMethod === 'transfer' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'\n              }`}\n            >\n              <CreditCard className=\"w-5 h-5 mr-2\" />\n              Перевод\n            </button>\n          </div>\n        </div>\n\n        {/* Payment Details */}\n        {paymentMethod === 'qr' && (\n          <div className=\"text-center mb-6\">\n            <div className=\"w-48 h-48 bg-gray-200 rounded-lg mx-auto mb-4 flex items-center justify-center\">\n              <QrCode className=\"w-16 h-16 text-gray-400\" />\n            </div>\n            <p className=\"text-sm text-gray-600\">\n              Отсканируйте QR-код для оплаты через банковское приложение\n            </p>\n          </div>\n        )}\n\n        {paymentMethod === 'transfer' && (\n          <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n            <h4 className=\"font-semibold mb-3\">Реквизиты для перевода:</h4>\n            <div className=\"space-y-2 text-sm\">\n              <div><strong>Получатель:</strong> BadmintonClub SRL</div>\n              <div><strong>Банк:</strong> Moldova Agroindbank</div>\n              <div><strong>IBAN:</strong> ************************</div>\n              <div><strong>Сумма:</strong> {pricePerHour} лей</div>\n              <div><strong>Назначение:</strong> Бронирование зал {hallId}, корт {court}, {format(date, 'dd.MM.yyyy')} {time}</div>\n            </div>\n          </div>\n        )}\n\n        {/* Action Buttons */}\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={onCancel}\n            className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            Отмена\n          </button>\n          <button\n            onClick={handlePaymentConfirm}\n            className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Подтвердить оплату\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg p-6\">\n      <h3 className=\"text-xl font-bold text-gray-900 mb-6\">Форма бронирования</h3>\n      \n      {/* Booking Details */}\n      <div className=\"bg-blue-50 rounded-lg p-4 mb-6\">\n        <div className=\"flex items-center text-blue-800 mb-2\">\n          <MapPin className=\"w-4 h-4 mr-2\" />\n          <span className=\"font-semibold\">Зал {hallId}, Корт {court}</span>\n        </div>\n        <div className=\"flex items-center text-blue-700 mb-1\">\n          <Calendar className=\"w-4 h-4 mr-2\" />\n          <span>{format(date, 'dd MMMM yyyy', { locale: ru })}</span>\n        </div>\n        <div className=\"flex items-center text-blue-700\">\n          <Clock className=\"w-4 h-4 mr-2\" />\n          <span>{time}</span>\n        </div>\n      </div>\n\n      <form onSubmit={handleSubmit(handleFormSubmit)} className=\"space-y-4\">\n        {/* Name Field */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Имя *\n          </label>\n          <div className=\"relative\">\n            <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n            <input\n              {...register('name')}\n              type=\"text\"\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"Введите ваше имя\"\n            />\n          </div>\n          {errors.name && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.name.message}</p>\n          )}\n        </div>\n\n        {/* Phone Field */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Телефон *\n          </label>\n          <div className=\"relative\">\n            <Phone className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n            <input\n              {...register('phone')}\n              type=\"tel\"\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"+373 XX XXX XXX\"\n            />\n          </div>\n          {errors.phone && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.phone.message}</p>\n          )}\n        </div>\n\n        {/* Email Field (Optional) */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Email (необязательно)\n          </label>\n          <input\n            {...register('email')}\n            type=\"email\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            placeholder=\"<EMAIL>\"\n          />\n          {errors.email && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n          )}\n        </div>\n\n        {/* Price Info */}\n        <div className=\"bg-gray-50 rounded-lg p-3\">\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">Стоимость (1 час):</span>\n            <span className=\"text-lg font-bold text-blue-600\">{pricePerHour} лей</span>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex space-x-3 pt-4\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            Отмена\n          </button>\n          <button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\"\n          >\n            {isSubmitting ? 'Обработка...' : 'Продолжить к оплате'}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAUA,MAAM,gBAAgB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,4BAA4B,QAAQ,GAAG,EAAE,CAAC,6KAAA,CAAA,IAAC,CAAC,OAAO,CAAC;AAC9E;AAae,SAAS,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAoB;IACrG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAEtE,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACpC,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,eAAe,KAAK,aAAa;IAEvC,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,MAAM,uBAAuB;QAC3B,MAAM,WAAW,IAAI,SAAS,SAAS,aAAa,CAAC;QACrD,MAAM,OAAO;YACX,MAAM,SAAS,GAAG,CAAC;YACnB,OAAO,SAAS,GAAG,CAAC;YACpB,OAAO,SAAS,GAAG,CAAC;YACpB;YACA;YACA;YACA;QACF;QACA,SAAS;IACX;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAuC;;;;;;8BAGrD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAI;wCAAK;wCAAO;wCAAQ;;;;;;;8CACzB,8OAAC;8CAAK,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,gBAAgB;wCAAE,QAAQ,2IAAA,CAAA,KAAE;oCAAC;;;;;;8CAChD,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAI,WAAU;;wCAA2C;wCAC5C;wCAAa;;;;;;;;;;;;;;;;;;;8BAM/B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAW,CAAC,uDAAuD,EACjE,kBAAkB,OAAO,+BAA+B,mBACxD;;sDAEF,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAW,CAAC,uDAAuD,EACjE,kBAAkB,aAAa,+BAA+B,mBAC9D;;sDAEF,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;gBAO5C,kBAAkB,sBACjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;gBAMxC,kBAAkB,4BACjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDAAI,8OAAC;sDAAO;;;;;;wCAAoB;;;;;;;8CACjC,8OAAC;;sDAAI,8OAAC;sDAAO;;;;;;wCAAc;;;;;;;8CAC3B,8OAAC;;sDAAI,8OAAC;sDAAO;;;;;;wCAAc;;;;;;;8CAC3B,8OAAC;;sDAAI,8OAAC;sDAAO;;;;;;wCAAe;wCAAE;wCAAa;;;;;;;8CAC3C,8OAAC;;sDAAI,8OAAC;sDAAO;;;;;;wCAAoB;wCAAmB;wCAAO;wCAAQ;wCAAM;wCAAG,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;wCAAc;wCAAE;;;;;;;;;;;;;;;;;;;8BAM/G,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAuC;;;;;;0BAGrD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAK,WAAU;;oCAAgB;oCAAK;oCAAO;oCAAQ;;;;;;;;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;0CAAM,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,gBAAgB;oCAAE,QAAQ,2IAAA,CAAA,KAAE;gCAAC;;;;;;;;;;;;kCAEnD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;0CAAM;;;;;;;;;;;;;;;;;;0BAIX,8OAAC;gBAAK,UAAU,aAAa;gBAAmB,WAAU;;kCAExD,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCACE,GAAG,SAAS,OAAO;wCACpB,MAAK;wCACL,WAAU;wCACV,aAAY;;;;;;;;;;;;4BAGf,OAAO,IAAI,kBACV,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;kCAKjE,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCACE,GAAG,SAAS,QAAQ;wCACrB,MAAK;wCACL,WAAU;wCACV,aAAY;;;;;;;;;;;;4BAGf,OAAO,KAAK,kBACX,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAKlE,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACE,GAAG,SAAS,QAAQ;gCACrB,MAAK;gCACL,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,KAAK,kBACX,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAKlE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;;wCAAmC;wCAAa;;;;;;;;;;;;;;;;;;kCAKpE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,eAAe,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 1770, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport HallCard from '@/components/HallCard';\nimport BookingCalendar from '@/components/BookingCalendar';\nimport BookingForm from '@/components/BookingForm';\nimport { Star, Users, Clock, MapPin, Phone, Mail, CheckCircle } from 'lucide-react';\n\ninterface BookingData {\n  name: string;\n  phone: string;\n  email?: string;\n  hallId: number;\n  date: Date;\n  time: string;\n  court: number;\n}\n\nexport default function Home() {\n  const [selectedHall, setSelectedHall] = useState<number | null>(null);\n  const [selectedSlot, setSelectedSlot] = useState<{\n    date: Date;\n    time: string;\n    court: number;\n  } | null>(null);\n  const [bookingComplete, setBookingComplete] = useState(false);\n\n  const halls = [\n    {\n      id: 1,\n      name: 'Зал 1',\n      courts: 3,\n      image: '/hall1.jpg',\n      description: 'Уютный зал с профессиональными кортами для игры в бадминтон',\n      features: [\n        'Профессиональное покрытие',\n        'Отличное освещение',\n        'Кондиционирование воздуха',\n        'Раздевалки с душем'\n      ],\n      pricePerHour: 150\n    },\n    {\n      id: 2,\n      name: 'Зал 2',\n      courts: 7,\n      image: '/hall2.jpg',\n      description: 'Большой зал с семью кортами для турниров и тренировок',\n      features: [\n        'Турнирные корты',\n        'Трибуны для зрителей',\n        'Профессиональная разметка',\n        'Система вентиляции',\n        'Звуковая система'\n      ],\n      pricePerHour: 180\n    },\n    {\n      id: 3,\n      name: 'Зал 3',\n      courts: 7,\n      image: '/hall3.jpg',\n      description: 'Современный зал с новейшим оборудованием',\n      features: [\n        'Новейшее покрытие',\n        'LED освещение',\n        'Климат-контроль',\n        'VIP раздевалки',\n        'Зона отдыха'\n      ],\n      pricePerHour: 200\n    }\n  ];\n\n  const handleHallSelect = (hallId: number) => {\n    setSelectedHall(hallId);\n    setSelectedSlot(null);\n  };\n\n  const handleSlotSelect = (date: Date, time: string, court: number) => {\n    setSelectedSlot({ date, time, court });\n  };\n\n  const handleBookingSubmit = (data: BookingData) => {\n    console.log('Booking submitted:', data);\n    setBookingComplete(true);\n    // Здесь будет отправка данных на сервер\n  };\n\n  const handleBookingCancel = () => {\n    setSelectedSlot(null);\n    setSelectedHall(null);\n  };\n\n  const resetBooking = () => {\n    setBookingComplete(false);\n    setSelectedSlot(null);\n    setSelectedHall(null);\n  };\n\n  if (bookingComplete) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"bg-white rounded-xl shadow-lg p-8 text-center\">\n            <CheckCircle className=\"w-16 h-16 text-green-500 mx-auto mb-4\" />\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">\n              Бронирование успешно!\n            </h2>\n            <p className=\"text-gray-600 mb-6\">\n              Ваша заявка принята. Мы свяжемся с вами в ближайшее время для подтверждения.\n            </p>\n            <button\n              onClick={resetBooking}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors\"\n            >\n              Сделать новое бронирование\n            </button>\n          </div>\n        </main>\n        <Footer />\n      </div>\n    );\n  }\n\n  // Booking flow\n  if (selectedSlot && selectedHall) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <BookingForm\n            hallId={selectedHall}\n            date={selectedSlot.date}\n            time={selectedSlot.time}\n            court={selectedSlot.court}\n            onSubmit={handleBookingSubmit}\n            onCancel={handleBookingCancel}\n          />\n        </main>\n        <Footer />\n      </div>\n    );\n  }\n\n  if (selectedHall) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <main className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"mb-6\">\n            <button\n              onClick={() => setSelectedHall(null)}\n              className=\"text-blue-600 hover:text-blue-700 font-medium\"\n            >\n              ← Назад к выбору зала\n            </button>\n          </div>\n          <BookingCalendar\n            hallId={selectedHall}\n            onSlotSelect={handleSlotSelect}\n          />\n        </main>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n\n      {/* Hero Section */}\n      <section id=\"home\" className=\"bg-gradient-to-br from-blue-600 to-blue-800 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              Бадминтонный клуб\n              <span className=\"block text-blue-200\">в Кишиневе</span>\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto\">\n              Современные залы с профессиональными кортами.\n              Удобная система онлайн бронирования. 17 кортов в 3 залах.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href=\"#halls\"\n                className=\"bg-white text-blue-600 font-semibold py-3 px-8 rounded-lg hover:bg-blue-50 transition-colors\"\n              >\n                Посмотреть залы\n              </a>\n              <a\n                href=\"#booking\"\n                className=\"border-2 border-white text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:text-blue-600 transition-colors\"\n              >\n                Забронировать\n              </a>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Почему выбирают нас\n            </h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Мы предлагаем лучшие условия для игры в бадминтон в Кишиневе\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Users className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">17 кортов</h3>\n              <p className=\"text-gray-600\">\n                3 современных зала с профессиональными кортами для игры и тренировок\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Clock className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Удобное время</h3>\n              <p className=\"text-gray-600\">\n                Работаем с 6:00 до 23:00 в будни и с 8:00 до 22:00 в выходные\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Star className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Высокое качество</h3>\n              <p className=\"text-gray-600\">\n                Профессиональное покрытие, отличное освещение и климат-контроль\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Halls Section */}\n      <section id=\"halls\" className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Наши залы\n            </h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Выберите подходящий зал для вашей игры\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {halls.map((hall) => (\n              <HallCard\n                key={hall.id}\n                {...hall}\n                onBookClick={handleHallSelect}\n              />\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Section */}\n      <section id=\"contact\" className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Контакты\n            </h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Свяжитесь с нами для получения дополнительной информации\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-12\">\n            {/* Contact Info */}\n            <div>\n              <h3 className=\"text-xl font-semibold mb-6\">Как нас найти</h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-start\">\n                  <MapPin className=\"w-5 h-5 text-blue-600 mt-1 mr-3\" />\n                  <div>\n                    <div className=\"font-medium\">Адрес</div>\n                    <div className=\"text-gray-600\">ул. Примерная, 123, Кишинев, Молдова</div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start\">\n                  <Phone className=\"w-5 h-5 text-blue-600 mt-1 mr-3\" />\n                  <div>\n                    <div className=\"font-medium\">Телефон</div>\n                    <div className=\"text-gray-600\">+373 XX XXX XXX</div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start\">\n                  <Mail className=\"w-5 h-5 text-blue-600 mt-1 mr-3\" />\n                  <div>\n                    <div className=\"font-medium\">Email</div>\n                    <div className=\"text-gray-600\"><EMAIL></div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start\">\n                  <Clock className=\"w-5 h-5 text-blue-600 mt-1 mr-3\" />\n                  <div>\n                    <div className=\"font-medium\">Режим работы</div>\n                    <div className=\"text-gray-600\">\n                      <div>Пн-Пт: 06:00 - 23:00</div>\n                      <div>Сб-Вс: 08:00 - 22:00</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Map Placeholder */}\n            <div>\n              <h3 className=\"text-xl font-semibold mb-6\">Расположение</h3>\n              <div className=\"bg-gray-200 rounded-lg h-64 flex items-center justify-center\">\n                <div className=\"text-center text-gray-500\">\n                  <MapPin className=\"w-12 h-12 mx-auto mb-2\" />\n                  <div>Карта будет здесь</div>\n                  <div className=\"text-sm\">Кишинев, Молдова</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAoBe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIrC;IACV,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,cAAc;QAChB;KACD;IAED,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAC,MAAY,MAAc;QAClD,gBAAgB;YAAE;YAAM;YAAM;QAAM;IACtC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,sBAAsB;QAClC,mBAAmB;IACnB,wCAAwC;IAC1C;IAEA,MAAM,sBAAsB;QAC1B,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,eAAe;QACnB,mBAAmB;QACnB,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,IAAI,iBAAiB;QACnB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAKL,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;IAGb;IAEA,eAAe;IACf,IAAI,gBAAgB,cAAc;QAChC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC,iIAAA,CAAA,UAAW;wBACV,QAAQ;wBACR,MAAM,aAAa,IAAI;wBACvB,MAAM,aAAa,IAAI;wBACvB,OAAO,aAAa,KAAK;wBACzB,UAAU;wBACV,UAAU;;;;;;;;;;;8BAGd,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;IAGb;IAEA,IAAI,cAAc;QAChB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;;sCACd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;;;;;;sCAIH,8OAAC,qIAAA,CAAA,UAAe;4BACd,QAAQ;4BACR,cAAc;;;;;;;;;;;;8BAGlB,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;IAGb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAGP,8OAAC;gBAAQ,IAAG;gBAAO,WAAU;0BAC3B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAsC;kDAElD,8OAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAExC,8OAAC;gCAAE,WAAU;0CAA2D;;;;;;0CAIxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,8OAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,8HAAA,CAAA,UAAQ;oCAEN,GAAG,IAAI;oCACR,aAAa;mCAFR,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0BAUtB,8OAAC;gBAAQ,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAc;;;;;;8EAC7B,8OAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;8DAInC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAc;;;;;;8EAC7B,8OAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;8DAInC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAc;;;;;;8EAC7B,8OAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;8DAInC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAc;;;;;;8EAC7B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAI;;;;;;sFACL,8OAAC;sFAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQf,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAI;;;;;;kEACL,8OAAC;wDAAI,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrC,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}