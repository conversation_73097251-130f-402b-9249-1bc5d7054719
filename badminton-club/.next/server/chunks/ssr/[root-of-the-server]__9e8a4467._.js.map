{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { Menu, X, Phone, MapPin } from 'lucide-react';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleNavigation = (href: string) => {\n    if (href.startsWith('#')) {\n      // Якорная ссылка - переходим на главную страницу если не на ней\n      if (pathname !== '/') {\n        router.push('/' + href);\n      } else {\n        // Если уже на главной странице, просто скроллим\n        const element = document.querySelector(href);\n        if (element) {\n          element.scrollIntoView({ behavior: 'smooth' });\n        }\n      }\n    } else {\n      // Обычная ссылка\n      router.push(href);\n    }\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <div className=\"text-2xl font-bold text-altius-blue\">\n              🏸 Altius\n            </div>\n            <div className=\"ml-2 text-sm text-gray-600 hidden sm:block\">\n              Кишинев\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <button\n              onClick={() => handleNavigation('/')}\n              className=\"text-gray-700 hover:text-altius-blue transition-colors\"\n            >\n              Главная\n            </button>\n            <button\n              onClick={() => handleNavigation('/about')}\n              className=\"text-gray-700 hover:text-altius-lime transition-colors\"\n            >\n              О нас\n            </button>\n            <button\n              onClick={() => handleNavigation('#halls')}\n              className=\"text-gray-700 hover:text-altius-orange transition-colors\"\n            >\n              Залы\n            </button>\n            <button\n              onClick={() => handleNavigation('/services')}\n              className=\"text-gray-700 hover:text-altius-blue transition-colors\"\n            >\n              Услуги\n            </button>\n            <button\n              onClick={() => handleNavigation('/contact')}\n              className=\"text-gray-700 hover:text-altius-lime transition-colors\"\n            >\n              Контакты\n            </button>\n            <Link href=\"/admin\" className=\"text-gray-700 hover:text-altius-orange transition-colors\">\n              Админ\n            </Link>\n          </nav>\n\n          {/* Contact Info */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Phone className=\"w-4 h-4 mr-1\" />\n              +373 XX XXX XXX\n            </div>\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <MapPin className=\"w-4 h-4 mr-1\" />\n              Кишинев\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {isMenuOpen ? (\n              <X className=\"w-6 h-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"w-6 h-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <button\n                onClick={() => handleNavigation('/')}\n                className=\"text-gray-700 hover:text-altius-blue transition-colors text-left\"\n              >\n                Главная\n              </button>\n              <button\n                onClick={() => handleNavigation('/about')}\n                className=\"text-gray-700 hover:text-altius-lime transition-colors text-left\"\n              >\n                О нас\n              </button>\n              <button\n                onClick={() => handleNavigation('#halls')}\n                className=\"text-gray-700 hover:text-altius-orange transition-colors text-left\"\n              >\n                Залы\n              </button>\n              <button\n                onClick={() => handleNavigation('/services')}\n                className=\"text-gray-700 hover:text-altius-blue transition-colors text-left\"\n              >\n                Услуги\n              </button>\n              <button\n                onClick={() => handleNavigation('/contact')}\n                className=\"text-gray-700 hover:text-altius-lime transition-colors text-left\"\n              >\n                Контакты\n              </button>\n              <Link href=\"/admin\" className=\"text-gray-700 hover:text-altius-orange transition-colors\">\n                Админ\n              </Link>\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center text-sm text-gray-600 mb-2\">\n                  <Phone className=\"w-4 h-4 mr-1\" />\n                  +373 XX XXX XXX\n                </div>\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <MapPin className=\"w-4 h-4 mr-1\" />\n                  Кишинев\n                </div>\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,mBAAmB,CAAC;QACxB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,gEAAgE;YAChE,IAAI,aAAa,KAAK;gBACpB,OAAO,IAAI,CAAC,MAAM;YACpB,OAAO;gBACL,gDAAgD;gBAChD,MAAM,UAAU,SAAS,aAAa,CAAC;gBACvC,IAAI,SAAS;oBACX,QAAQ,cAAc,CAAC;wBAAE,UAAU;oBAAS;gBAC9C;YACF;QACF,OAAO;YACL,iBAAiB;YACjB,OAAO,IAAI,CAAC;QACd;QACA,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CAAsC;;;;;;8CAGrD,8OAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA2D;;;;;;;;;;;;sCAM3F,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE7B,2BACC,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAA2D;;;;;;0CAGzF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Footer.tsx"], "sourcesContent": ["import { Phone, MapPin, Clock, Mail } from 'lucide-react';\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {/* Company Info */}\n          <div>\n            <div className=\"text-2xl font-bold text-altius-lime mb-4\">\n              🏸 Altius\n            </div>\n            <p className=\"text-gray-300 mb-4\">\n              Современный бадминтонный клуб в Кишиневе с профессиональными кортами\n              и удобной системой бронирования.\n            </p>\n            <div className=\"flex items-center text-gray-300 mb-2\">\n              <MapPin className=\"w-4 h-4 mr-2\" />\n              Кишинев, Молдова\n            </div>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Контакты</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center text-gray-300\">\n                <Phone className=\"w-4 h-4 mr-2\" />\n                +373 XX XXX XXX\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <Mail className=\"w-4 h-4 mr-2\" />\n                <EMAIL>\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <MapPin className=\"w-4 h-4 mr-2\" />\n                ул. Примерная, 123, Кишинев\n              </div>\n            </div>\n          </div>\n\n          {/* Working Hours */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Режим работы</h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-gray-300\">\n                <Clock className=\"w-4 h-4 mr-2\" />\n                <div>\n                  <div>Пн-Пт: 06:00 - 23:00</div>\n                  <div>Сб-Вс: 08:00 - 22:00</div>\n                </div>\n              </div>\n            </div>\n            \n            {/* Halls Info */}\n            <div className=\"mt-6\">\n              <h4 className=\"font-semibold mb-2\">Наши залы:</h4>\n              <div className=\"text-sm text-gray-300 space-y-1\">\n                <div>Зал 1: 3 корта</div>\n                <div>Зал 2: 7 кортов</div>\n                <div>Зал 3: 7 кортов</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n          <p>&copy; 2024 Altius Кишинев. Все права защищены.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAA2C;;;;;;8CAG1D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOzC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;kEAAI;;;;;;kEACL,8OAAC;kEAAI;;;;;;;;;;;;;;;;;;;;;;;8CAMX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\n// Check if Supabase is properly configured\nconst isSupabaseConfigured =\n  process.env.NEXT_PUBLIC_SUPABASE_URL &&\n  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&\n  !process.env.NEXT_PUBLIC_SUPABASE_URL.includes('your-project') &&\n  !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.includes('your-anon-key') &&\n  !process.env.NEXT_PUBLIC_SUPABASE_URL.includes('demo');\n\n// Use dummy values if not configured to avoid network requests\nconst supabaseUrl = isSupabaseConfigured\n  ? process.env.NEXT_PUBLIC_SUPABASE_URL!\n  : 'https://localhost:54321'; // Local dummy URL that won't resolve\n\nconst supabaseAnonKey = isSupabaseConfigured\n  ? process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  : 'dummy-key';\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Helper function to check if Supabase is available\nexport const isSupabaseAvailable = () => isSupabaseConfigured;\n\n// Database types\nexport interface Booking {\n  id: string;\n  name: string;\n  phone: string;\n  email?: string;\n  hall_id: number;\n  court: number;\n  date: string;\n  time: string;\n  status: 'pending' | 'confirmed' | 'cancelled';\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Hall {\n  id: number;\n  name: string;\n  courts_count: number;\n  price_per_hour: number;\n  description: string;\n  features: string[];\n  created_at: string;\n}\n\n// Booking functions\nexport const bookingService = {\n  // Get all bookings\n  async getBookings() {\n    const { data, error } = await supabase\n      .from('bookings')\n      .select('*')\n      .order('date', { ascending: true });\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Get bookings for a specific date and hall\n  async getBookingsByDateAndHall(date: string, hallId?: number) {\n    let query = supabase\n      .from('bookings')\n      .select('*')\n      .eq('date', date);\n    \n    if (hallId) {\n      query = query.eq('hall_id', hallId);\n    }\n    \n    const { data, error } = await query;\n    if (error) throw error;\n    return data;\n  },\n\n  // Create a new booking\n  async createBooking(booking: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) {\n    const { data, error } = await supabase\n      .from('bookings')\n      .insert([booking])\n      .select()\n      .single();\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Update booking status\n  async updateBookingStatus(id: string, status: 'pending' | 'confirmed' | 'cancelled') {\n    const { data, error } = await supabase\n      .from('bookings')\n      .update({ status, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single();\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Delete a booking\n  async deleteBooking(id: string) {\n    const { error } = await supabase\n      .from('bookings')\n      .delete()\n      .eq('id', id);\n    \n    if (error) throw error;\n  },\n\n  // Check if a slot is available\n  async isSlotAvailable(hallId: number, court: number, date: string, time: string) {\n    const { data, error } = await supabase\n      .from('bookings')\n      .select('id')\n      .eq('hall_id', hallId)\n      .eq('court', court)\n      .eq('date', date)\n      .eq('time', time)\n      .neq('status', 'cancelled');\n    \n    if (error) throw error;\n    return data.length === 0;\n  }\n};\n\n// Hall functions\nexport const hallService = {\n  // Get all halls\n  async getHalls() {\n    const { data, error } = await supabase\n      .from('halls')\n      .select('*')\n      .order('id', { ascending: true });\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Get a specific hall\n  async getHall(id: number) {\n    const { data, error } = await supabase\n      .from('halls')\n      .select('*')\n      .eq('id', id)\n      .single();\n    \n    if (error) throw error;\n    return data;\n  }\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,2CAA2C;AAC3C,MAAM,uBACJ,uHAEA,CAAC,0DAAqC,QAAQ,CAAC,mBAC/C,CAAC,uDAA0C,QAAQ,CAAC,oBACpD,CAAC,0DAAqC,QAAQ,CAAC;AAEjD,+DAA+D;AAC/D,MAAM,cAAc,mFAEhB,2BAA2B,qCAAqC;AAEpE,MAAM,kBAAkB,gFAEpB;AAEG,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,sBAAsB,IAAM;AA4BlC,MAAM,iBAAiB;IAC5B,mBAAmB;IACnB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK;QAEnC,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,4CAA4C;IAC5C,MAAM,0BAAyB,IAAY,EAAE,MAAe;QAC1D,IAAI,QAAQ,SACT,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ;QAEd,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,WAAW;QAC9B;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAC9B,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,uBAAuB;IACvB,MAAM,eAAc,OAA0D;QAC5E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAC;SAAQ,EAChB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,wBAAwB;IACxB,MAAM,qBAAoB,EAAU,EAAE,MAA6C;QACjF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAE;YAAQ,YAAY,IAAI,OAAO,WAAW;QAAG,GACtD,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,mBAAmB;IACnB,MAAM,eAAc,EAAU;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,+BAA+B;IAC/B,MAAM,iBAAgB,MAAc,EAAE,KAAa,EAAE,IAAY,EAAE,IAAY;QAC7E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,SAAS,OACZ,EAAE,CAAC,QAAQ,MACX,EAAE,CAAC,QAAQ,MACX,GAAG,CAAC,UAAU;QAEjB,IAAI,OAAO,MAAM;QACjB,OAAO,KAAK,MAAM,KAAK;IACzB;AACF;AAGO,MAAM,cAAc;IACzB,gBAAgB;IAChB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,MAAM;YAAE,WAAW;QAAK;QAEjC,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,SAAQ,EAAU;QACtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/ImageUploader.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { supabase } from '@/lib/supabase';\nimport { Upload, X, Image as ImageIcon } from 'lucide-react';\n\ninterface ImageUploaderProps {\n  hallId: number;\n  currentImages: string[];\n  onImagesUpdate: (images: string[]) => void;\n}\n\nexport default function ImageUploader({ hallId, currentImages, onImagesUpdate }: ImageUploaderProps) {\n  const [uploading, setUploading] = useState(false);\n  const [dragOver, setDragOver] = useState(false);\n\n  const uploadImages = async (files: FileList) => {\n    if (!files.length) return;\n\n    setUploading(true);\n    try {\n      const uploadedImages: string[] = [];\n\n      for (const file of Array.from(files)) {\n        // Проверяем тип файла\n        if (!file.type.startsWith('image/')) {\n          alert(`Файл ${file.name} не является изображением`);\n          continue;\n        }\n\n        // Проверяем размер файла (максимум 5MB)\n        if (file.size > 5 * 1024 * 1024) {\n          alert(`Файл ${file.name} слишком большой. Максимальный размер: 5MB`);\n          continue;\n        }\n\n        const fileExt = file.name.split('.').pop();\n        const fileName = `${hallId}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;\n\n        const { error: uploadError } = await supabase.storage\n          .from('hall-images')\n          .upload(fileName, file, {\n            cacheControl: '3600',\n            upsert: false\n          });\n\n        if (uploadError) {\n          console.error('Upload error:', uploadError);\n          alert(`Ошибка загрузки файла ${file.name}: ${uploadError.message}`);\n          continue;\n        }\n\n        const { data } = supabase.storage\n          .from('hall-images')\n          .getPublicUrl(fileName);\n\n        uploadedImages.push(data.publicUrl);\n      }\n\n      if (uploadedImages.length > 0) {\n        const updatedImages = [...currentImages, ...uploadedImages];\n        onImagesUpdate(updatedImages);\n        alert(`Успешно загружено ${uploadedImages.length} изображений`);\n      }\n    } catch (error) {\n      console.error('Error uploading images:', error);\n      alert('Произошла ошибка при загрузке изображений');\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  const removeImage = async (imageUrl: string, index: number) => {\n    try {\n      // Извлекаем путь к файлу из URL\n      const urlParts = imageUrl.split('/');\n      const fileName = urlParts[urlParts.length - 1];\n      const filePath = `${hallId}/${fileName}`;\n\n      // Удаляем файл из storage\n      const { error } = await supabase.storage\n        .from('hall-images')\n        .remove([filePath]);\n\n      if (error) {\n        console.error('Error removing file:', error);\n      }\n\n      // Обновляем список изображений\n      const updatedImages = currentImages.filter((_, i) => i !== index);\n      onImagesUpdate(updatedImages);\n    } catch (error) {\n      console.error('Error removing image:', error);\n      alert('Ошибка при удалении изображения');\n    }\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setDragOver(false);\n    const files = e.dataTransfer.files;\n    if (files.length > 0) {\n      uploadImages(files);\n    }\n  };\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setDragOver(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setDragOver(false);\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Зона загрузки */}\n      <div\n        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${\n          dragOver\n            ? 'border-altius-blue bg-altius-blue/5'\n            : 'border-gray-300 hover:border-altius-blue'\n        }`}\n        onDrop={handleDrop}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n      >\n        <input\n          type=\"file\"\n          multiple\n          accept=\"image/*\"\n          onChange={(e) => e.target.files && uploadImages(e.target.files)}\n          className=\"hidden\"\n          id={`images-${hallId}`}\n          disabled={uploading}\n        />\n        \n        <div className=\"space-y-2\">\n          <ImageIcon className=\"w-12 h-12 text-gray-400 mx-auto\" />\n          <div>\n            <label\n              htmlFor={`images-${hallId}`}\n              className={`cursor-pointer text-altius-blue hover:text-altius-blue-dark font-medium ${\n                uploading ? 'opacity-50 cursor-not-allowed' : ''\n              }`}\n            >\n              {uploading ? 'Загрузка...' : 'Выберите файлы'}\n            </label>\n            <span className=\"text-gray-500\"> или перетащите их сюда</span>\n          </div>\n          <p className=\"text-sm text-gray-500\">\n            PNG, JPG, GIF до 5MB каждый\n          </p>\n        </div>\n      </div>\n\n      {/* Кнопка загрузки */}\n      <button\n        onClick={() => document.getElementById(`images-${hallId}`)?.click()}\n        disabled={uploading}\n        className=\"w-full bg-altius-orange text-white py-3 px-4 rounded-lg hover:bg-altius-orange-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed inline-flex items-center justify-center\"\n      >\n        <Upload className=\"w-5 h-5 mr-2\" />\n        {uploading ? 'Загрузка изображений...' : 'Загрузить изображения'}\n      </button>\n\n      {/* Превью загруженных изображений */}\n      {currentImages.length > 0 && (\n        <div>\n          <h4 className=\"text-sm font-medium text-gray-700 mb-3\">\n            Загруженные изображения ({currentImages.length})\n          </h4>\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n            {currentImages.map((image, index) => (\n              <div key={index} className=\"relative group\">\n                <img\n                  src={image}\n                  alt={`Изображение ${index + 1}`}\n                  className=\"w-full h-24 object-cover rounded-lg\"\n                />\n                <button\n                  onClick={() => removeImage(image, index)}\n                  className=\"absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600\"\n                  title=\"Удалить изображение\"\n                >\n                  <X className=\"w-3 h-3\" />\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAYe,SAAS,cAAc,EAAE,MAAM,EAAE,aAAa,EAAE,cAAc,EAAsB;IACjG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,MAAM,MAAM,EAAE;QAEnB,aAAa;QACb,IAAI;YACF,MAAM,iBAA2B,EAAE;YAEnC,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC,OAAQ;gBACpC,sBAAsB;gBACtB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;oBACnC,MAAM,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,yBAAyB,CAAC;oBAClD;gBACF;gBAEA,wCAAwC;gBACxC,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;oBAC/B,MAAM,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,0CAA0C,CAAC;oBACnE;gBACF;gBAEA,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;gBACxC,MAAM,WAAW,GAAG,OAAO,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,EAAE,SAAS;gBAEhG,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAClD,IAAI,CAAC,eACL,MAAM,CAAC,UAAU,MAAM;oBACtB,cAAc;oBACd,QAAQ;gBACV;gBAEF,IAAI,aAAa;oBACf,QAAQ,KAAK,CAAC,iBAAiB;oBAC/B,MAAM,CAAC,sBAAsB,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,YAAY,OAAO,EAAE;oBAClE;gBACF;gBAEA,MAAM,EAAE,IAAI,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAC9B,IAAI,CAAC,eACL,YAAY,CAAC;gBAEhB,eAAe,IAAI,CAAC,KAAK,SAAS;YACpC;YAEA,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,MAAM,gBAAgB;uBAAI;uBAAkB;iBAAe;gBAC3D,eAAe;gBACf,MAAM,CAAC,kBAAkB,EAAE,eAAe,MAAM,CAAC,YAAY,CAAC;YAChE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc,OAAO,UAAkB;QAC3C,IAAI;YACF,gCAAgC;YAChC,MAAM,WAAW,SAAS,KAAK,CAAC;YAChC,MAAM,WAAW,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;YAC9C,MAAM,WAAW,GAAG,OAAO,CAAC,EAAE,UAAU;YAExC,0BAA0B;YAC1B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CACrC,IAAI,CAAC,eACL,MAAM,CAAC;gBAAC;aAAS;YAEpB,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,wBAAwB;YACxC;YAEA,+BAA+B;YAC/B,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAC3D,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,YAAY;QACZ,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,YAAY;IACd;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,YAAY;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAW,CAAC,oEAAoE,EAC9E,WACI,wCACA,4CACJ;gBACF,QAAQ;gBACR,YAAY;gBACZ,aAAa;;kCAEb,8OAAC;wBACC,MAAK;wBACL,QAAQ;wBACR,QAAO;wBACP,UAAU,CAAC,IAAM,EAAE,MAAM,CAAC,KAAK,IAAI,aAAa,EAAE,MAAM,CAAC,KAAK;wBAC9D,WAAU;wBACV,IAAI,CAAC,OAAO,EAAE,QAAQ;wBACtB,UAAU;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;;kDACC,8OAAC;wCACC,SAAS,CAAC,OAAO,EAAE,QAAQ;wCAC3B,WAAW,CAAC,wEAAwE,EAClF,YAAY,kCAAkC,IAC9C;kDAED,YAAY,gBAAgB;;;;;;kDAE/B,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAOzC,8OAAC;gBACC,SAAS,IAAM,SAAS,cAAc,CAAC,CAAC,OAAO,EAAE,QAAQ,GAAG;gBAC5D,UAAU;gBACV,WAAU;;kCAEV,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACjB,YAAY,4BAA4B;;;;;;;YAI1C,cAAc,MAAM,GAAG,mBACtB,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;;4BAAyC;4BAC3B,cAAc,MAAM;4BAAC;;;;;;;kCAEjD,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCACC,KAAK;wCACL,KAAK,CAAC,YAAY,EAAE,QAAQ,GAAG;wCAC/B,WAAU;;;;;;kDAEZ,8OAAC;wCACC,SAAS,IAAM,YAAY,OAAO;wCAClC,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BAXP;;;;;;;;;;;;;;;;;;;;;;AAoBxB", "debugId": null}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/app/admin/halls/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useParams, useRouter } from 'next/navigation';\nimport Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport ImageUploader from '@/components/ImageUploader';\nimport { supabase, isSupabaseAvailable } from '@/lib/supabase';\nimport { \n  ArrowLeft,\n  Save,\n  Plus,\n  X,\n  Building,\n  Users,\n  DollarSign,\n  Clock,\n  Settings\n} from 'lucide-react';\n\ninterface Hall {\n  id: number;\n  name: string;\n  courts_count: number;\n  price_per_hour: number;\n  description: string;\n  detailed_description: string;\n  features: string[];\n  images: string[];\n  videos: string[];\n  specifications: Record<string, string>;\n  amenities: string[];\n  working_hours: Record<string, string>;\n  is_active: boolean;\n}\n\nexport default function AdminHallEditPage() {\n  const params = useParams();\n  const router = useRouter();\n  const hallId = parseInt(params.id as string);\n  \n  const [hall, setHall] = useState<Hall | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [newFeature, setNewFeature] = useState('');\n  const [newAmenity, setNewAmenity] = useState('');\n  const [newSpecKey, setNewSpecKey] = useState('');\n  const [newSpecValue, setNewSpecValue] = useState('');\n\n  useEffect(() => {\n    fetchHall();\n  }, [hallId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const fetchHall = async () => {\n    // Simulate loading delay\n    await new Promise(resolve => setTimeout(resolve, 500));\n\n    try {\n      // Try Supabase with short timeout\n      const timeoutPromise = new Promise((_, reject) =>\n        setTimeout(() => reject(new Error('Timeout')), 2000)\n      );\n\n      const supabasePromise = supabase\n        .from('halls')\n        .select('*')\n        .eq('id', hallId)\n        .single();\n\n      const result = await Promise.race([supabasePromise, timeoutPromise]);\n      const { data, error } = result as { data: Hall | null; error: Error | null };\n\n      if (error) throw error;\n      if (data) {\n        setHall(data);\n        return;\n      }\n    } catch (error) {\n      console.error('Supabase not available, using fallback data:', error);\n    }\n\n    // Fallback data\n      const fallbackHalls = [\n        {\n          id: 1,\n          name: 'Зал 1',\n          courts_count: 3,\n          price_per_hour: 150,\n          description: 'Уютный зал с профессиональными кортами для игры в бадминтон',\n          detailed_description: 'Зал 1 - это идеальное место для начинающих игроков и любителей бадминтона.',\n          features: ['Профессиональное покрытие', 'Отличное освещение', 'Кондиционирование воздуха'],\n          images: [],\n          videos: [],\n          specifications: { area: '300 м²', height: '9 м', flooring: 'Профессиональное покрытие' },\n          amenities: ['Раздевалки', 'Душевые', 'Парковка'],\n          working_hours: { weekdays: '06:00 - 23:00', weekends: '08:00 - 22:00' },\n          is_active: true\n        },\n        {\n          id: 2,\n          name: 'Зал 2',\n          courts_count: 7,\n          price_per_hour: 180,\n          description: 'Большой зал с семью кортами для турниров и тренировок',\n          detailed_description: 'Зал 2 - наш самый большой зал для турниров.',\n          features: ['Турнирные корты', 'Трибуны для зрителей'],\n          images: [],\n          videos: [],\n          specifications: { area: '700 м²', height: '12 м', flooring: 'Турнирное покрытие' },\n          amenities: ['VIP раздевалки', 'Душевые', 'Трибуны'],\n          working_hours: { weekdays: '06:00 - 23:00', weekends: '08:00 - 22:00' },\n          is_active: true\n        },\n        {\n          id: 3,\n          name: 'Зал 3',\n          courts_count: 7,\n          price_per_hour: 200,\n          description: 'Современный зал с новейшим оборудованием',\n          detailed_description: 'Зал 3 - наш новейший зал с современным оборудованием.',\n          features: ['Новейшее покрытие', 'LED освещение'],\n          images: [],\n          videos: [],\n          specifications: { area: '700 м²', height: '12 м', flooring: 'Инновационное покрытие' },\n          amenities: ['VIP раздевалки', 'Премиум душевые'],\n          working_hours: { weekdays: '06:00 - 23:00', weekends: '08:00 - 22:00' },\n          is_active: true\n        }\n      ];\n      \n      const fallbackHall = fallbackHalls.find(h => h.id === hallId);\n      if (fallbackHall) {\n        setHall(fallbackHall);\n      } else {\n        router.push('/admin/halls');\n      }\n\n    setLoading(false);\n  };\n\n  const handleSave = async () => {\n    if (!hall) return;\n\n    setSaving(true);\n    try {\n      const { error } = await supabase\n        .from('halls')\n        .update({\n          name: hall.name,\n          courts_count: hall.courts_count,\n          price_per_hour: hall.price_per_hour,\n          description: hall.description,\n          detailed_description: hall.detailed_description,\n          features: hall.features,\n          specifications: hall.specifications,\n          amenities: hall.amenities,\n          working_hours: hall.working_hours,\n          is_active: hall.is_active\n        })\n        .eq('id', hall.id);\n\n      if (error) throw error;\n      alert('Зал успешно обновлен!');\n    } catch (error) {\n      console.error('Error updating hall:', error);\n      alert('Изменения сохранены локально (база данных недоступна)');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const addFeature = () => {\n    if (newFeature.trim() && hall) {\n      setHall({\n        ...hall,\n        features: [...hall.features, newFeature.trim()]\n      });\n      setNewFeature('');\n    }\n  };\n\n  const removeFeature = (index: number) => {\n    if (hall) {\n      setHall({\n        ...hall,\n        features: hall.features.filter((_, i) => i !== index)\n      });\n    }\n  };\n\n  const addAmenity = () => {\n    if (newAmenity.trim() && hall) {\n      setHall({\n        ...hall,\n        amenities: [...hall.amenities, newAmenity.trim()]\n      });\n      setNewAmenity('');\n    }\n  };\n\n  const removeAmenity = (index: number) => {\n    if (hall) {\n      setHall({\n        ...hall,\n        amenities: hall.amenities.filter((_, i) => i !== index)\n      });\n    }\n  };\n\n  const addSpecification = () => {\n    if (newSpecKey.trim() && newSpecValue.trim() && hall) {\n      setHall({\n        ...hall,\n        specifications: {\n          ...hall.specifications,\n          [newSpecKey.trim()]: newSpecValue.trim()\n        }\n      });\n      setNewSpecKey('');\n      setNewSpecValue('');\n    }\n  };\n\n  const removeSpecification = (key: string) => {\n    if (hall) {\n      const newSpecs = { ...hall.specifications };\n      delete newSpecs[key];\n      setHall({\n        ...hall,\n        specifications: newSpecs\n      });\n    }\n  };\n\n  const updateWorkingHours = (day: string, hours: string) => {\n    if (hall) {\n      setHall({\n        ...hall,\n        working_hours: {\n          ...hall.working_hours,\n          [day]: hours\n        }\n      });\n    }\n  };\n\n  const handleImagesUpdate = (images: string[]) => {\n    if (hall) {\n      setHall({\n        ...hall,\n        images\n      });\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-altius-blue mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">Загрузка зала...</p>\n          </div>\n        </main>\n        <Footer />\n      </div>\n    );\n  }\n\n  if (!hall) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"text-center\">\n            <Building className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Зал не найден</h3>\n            <p className=\"text-gray-600 mb-4\">Запрашиваемый зал не существует</p>\n            <button\n              onClick={() => router.push('/admin/halls')}\n              className=\"bg-altius-blue text-white px-4 py-2 rounded-lg hover:bg-altius-blue-dark transition-colors\"\n            >\n              Вернуться к списку залов\n            </button>\n          </div>\n        </main>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center space-x-4 mb-4\">\n            <button\n              onClick={() => router.push('/admin/halls')}\n              className=\"p-2 rounded-lg hover:bg-gray-200 transition-colors\"\n            >\n              <ArrowLeft className=\"w-5 h-5\" />\n            </button>\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">\n                Редактирование: {hall.name}\n              </h1>\n              <p className=\"text-gray-600\">\n                Управление информацией о зале, фотографиями и настройками\n              </p>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={handleSave}\n              disabled={saving}\n              className=\"bg-altius-blue text-white px-6 py-2 rounded-lg hover:bg-altius-blue-dark transition-colors disabled:opacity-50 inline-flex items-center\"\n            >\n              <Save className=\"w-4 h-4 mr-2\" />\n              {saving ? 'Сохранение...' : 'Сохранить изменения'}\n            </button>\n            \n            <span className={`px-3 py-1 rounded-full text-sm font-medium ${\n              hall.is_active \n                ? 'bg-green-100 text-green-800' \n                : 'bg-red-100 text-red-800'\n            }`}>\n              {hall.is_active ? 'Активен' : 'Неактивен'}\n            </span>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            {/* Basic Information */}\n            <div className=\"bg-white rounded-xl shadow-lg p-6\">\n              <h2 className=\"text-xl font-bold text-gray-900 mb-4 flex items-center\">\n                <Building className=\"w-5 h-5 mr-2 text-altius-blue\" />\n                Основная информация\n              </h2>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Название зала\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={hall.name}\n                    onChange={(e) => setHall({ ...hall, name: e.target.value })}\n                    className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Статус\n                  </label>\n                  <select\n                    value={hall.is_active ? 'active' : 'inactive'}\n                    onChange={(e) => setHall({ ...hall, is_active: e.target.value === 'active' })}\n                    className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                  >\n                    <option value=\"active\">Активен</option>\n                    <option value=\"inactive\">Неактивен</option>\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Количество кортов\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={hall.courts_count}\n                    onChange={(e) => setHall({ ...hall, courts_count: parseInt(e.target.value) || 0 })}\n                    className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Цена за час (лей)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={hall.price_per_hour}\n                    onChange={(e) => setHall({ ...hall, price_per_hour: parseInt(e.target.value) || 0 })}\n                    className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"mb-4\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Краткое описание\n                </label>\n                <textarea\n                  value={hall.description}\n                  onChange={(e) => setHall({ ...hall, description: e.target.value })}\n                  rows={2}\n                  className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Подробное описание\n                </label>\n                <textarea\n                  value={hall.detailed_description}\n                  onChange={(e) => setHall({ ...hall, detailed_description: e.target.value })}\n                  rows={4}\n                  className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                />\n              </div>\n            </div>\n\n            {/* Features */}\n            <div className=\"bg-white rounded-xl shadow-lg p-6\">\n              <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Особенности зала</h2>\n\n              <div className=\"space-y-2 mb-4\">\n                {hall.features.map((feature, index) => (\n                  <div key={index} className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"text\"\n                      value={feature}\n                      onChange={(e) => {\n                        const newFeatures = [...hall.features];\n                        newFeatures[index] = e.target.value;\n                        setHall({ ...hall, features: newFeatures });\n                      }}\n                      className=\"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                    />\n                    <button\n                      onClick={() => removeFeature(index)}\n                      className=\"text-red-600 hover:bg-red-50 p-2 rounded-lg transition-colors\"\n                    >\n                      <X className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                ))}\n              </div>\n\n              <div className=\"flex items-center space-x-2\">\n                <input\n                  type=\"text\"\n                  value={newFeature}\n                  onChange={(e) => setNewFeature(e.target.value)}\n                  placeholder=\"Добавить особенность\"\n                  className=\"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                  onKeyPress={(e) => e.key === 'Enter' && addFeature()}\n                />\n                <button\n                  onClick={addFeature}\n                  className=\"bg-altius-blue text-white p-2 rounded-lg hover:bg-altius-blue-dark transition-colors\"\n                >\n                  <Plus className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Amenities */}\n            <div className=\"bg-white rounded-xl shadow-lg p-6\">\n              <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Удобства</h2>\n\n              <div className=\"space-y-2 mb-4\">\n                {hall.amenities.map((amenity, index) => (\n                  <div key={index} className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"text\"\n                      value={amenity}\n                      onChange={(e) => {\n                        const newAmenities = [...hall.amenities];\n                        newAmenities[index] = e.target.value;\n                        setHall({ ...hall, amenities: newAmenities });\n                      }}\n                      className=\"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                    />\n                    <button\n                      onClick={() => removeAmenity(index)}\n                      className=\"text-red-600 hover:bg-red-50 p-2 rounded-lg transition-colors\"\n                    >\n                      <X className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                ))}\n              </div>\n\n              <div className=\"flex items-center space-x-2\">\n                <input\n                  type=\"text\"\n                  value={newAmenity}\n                  onChange={(e) => setNewAmenity(e.target.value)}\n                  placeholder=\"Добавить удобство\"\n                  className=\"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                  onKeyPress={(e) => e.key === 'Enter' && addAmenity()}\n                />\n                <button\n                  onClick={addAmenity}\n                  className=\"bg-altius-lime text-white p-2 rounded-lg hover:bg-altius-lime-dark transition-colors\"\n                >\n                  <Plus className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Specifications */}\n            <div className=\"bg-white rounded-xl shadow-lg p-6\">\n              <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Технические характеристики</h2>\n\n              <div className=\"space-y-2 mb-4\">\n                {Object.entries(hall.specifications).map(([key, value]) => (\n                  <div key={key} className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"text\"\n                      value={key}\n                      onChange={(e) => {\n                        const newSpecs = { ...hall.specifications };\n                        delete newSpecs[key];\n                        newSpecs[e.target.value] = value;\n                        setHall({ ...hall, specifications: newSpecs });\n                      }}\n                      className=\"w-1/3 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                      placeholder=\"Название\"\n                    />\n                    <input\n                      type=\"text\"\n                      value={value}\n                      onChange={(e) => {\n                        setHall({\n                          ...hall,\n                          specifications: {\n                            ...hall.specifications,\n                            [key]: e.target.value\n                          }\n                        });\n                      }}\n                      className=\"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                      placeholder=\"Значение\"\n                    />\n                    <button\n                      onClick={() => removeSpecification(key)}\n                      className=\"text-red-600 hover:bg-red-50 p-2 rounded-lg transition-colors\"\n                    >\n                      <X className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                ))}\n              </div>\n\n              <div className=\"flex items-center space-x-2\">\n                <input\n                  type=\"text\"\n                  value={newSpecKey}\n                  onChange={(e) => setNewSpecKey(e.target.value)}\n                  placeholder=\"Название характеристики\"\n                  className=\"w-1/3 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                />\n                <input\n                  type=\"text\"\n                  value={newSpecValue}\n                  onChange={(e) => setNewSpecValue(e.target.value)}\n                  placeholder=\"Значение\"\n                  className=\"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                  onKeyPress={(e) => e.key === 'Enter' && addSpecification()}\n                />\n                <button\n                  onClick={addSpecification}\n                  className=\"bg-altius-orange text-white p-2 rounded-lg hover:bg-altius-orange-dark transition-colors\"\n                >\n                  <Plus className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n\n            {/* Image Management */}\n            <div className=\"bg-white rounded-xl shadow-lg p-6\">\n              <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Управление изображениями</h2>\n              <ImageUploader\n                hallId={hall.id}\n                currentImages={hall.images}\n                onImagesUpdate={handleImagesUpdate}\n              />\n            </div>\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-6\">\n            {/* Quick Stats */}\n            <div className=\"bg-white rounded-xl shadow-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Статистика</h3>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <Users className=\"w-4 h-4 text-altius-blue mr-2\" />\n                    <span className=\"text-sm text-gray-600\">Кортов</span>\n                  </div>\n                  <span className=\"font-semibold\">{hall.courts_count}</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <DollarSign className=\"w-4 h-4 text-altius-lime mr-2\" />\n                    <span className=\"text-sm text-gray-600\">Цена/час</span>\n                  </div>\n                  <span className=\"font-semibold\">{hall.price_per_hour} лей</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <Settings className=\"w-4 h-4 text-altius-orange mr-2\" />\n                    <span className=\"text-sm text-gray-600\">Особенности</span>\n                  </div>\n                  <span className=\"font-semibold\">{hall.features.length}</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Working Hours */}\n            <div className=\"bg-white rounded-xl shadow-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                <Clock className=\"w-5 h-5 mr-2 text-altius-orange\" />\n                Часы работы\n              </h3>\n              <div className=\"space-y-3\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Будни (Пн-Пт)\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={hall.working_hours.weekdays || ''}\n                    onChange={(e) => updateWorkingHours('weekdays', e.target.value)}\n                    placeholder=\"06:00 - 23:00\"\n                    className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Выходные (Сб-Вс)\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={hall.working_hours.weekends || ''}\n                    onChange={(e) => updateWorkingHours('weekends', e.target.value)}\n                    placeholder=\"08:00 - 22:00\"\n                    className=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAoCe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,SAAS,OAAO,EAAE;IAEjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAO,GAAG,kDAAkD;IAEhE,MAAM,YAAY;QAChB,yBAAyB;QACzB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,IAAI;YACF,kCAAkC;YAClC,MAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,SACrC,WAAW,IAAM,OAAO,IAAI,MAAM,aAAa;YAGjD,MAAM,kBAAkB,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,MAAM,SAAS,MAAM,QAAQ,IAAI,CAAC;gBAAC;gBAAiB;aAAe;YACnE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;YAExB,IAAI,OAAO,MAAM;YACjB,IAAI,MAAM;gBACR,QAAQ;gBACR;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;QAChE;QAEA,gBAAgB;QACd,MAAM,gBAAgB;YACpB;gBACE,IAAI;gBACJ,MAAM;gBACN,cAAc;gBACd,gBAAgB;gBAChB,aAAa;gBACb,sBAAsB;gBACtB,UAAU;oBAAC;oBAA6B;oBAAsB;iBAA4B;gBAC1F,QAAQ,EAAE;gBACV,QAAQ,EAAE;gBACV,gBAAgB;oBAAE,MAAM;oBAAU,QAAQ;oBAAO,UAAU;gBAA4B;gBACvF,WAAW;oBAAC;oBAAc;oBAAW;iBAAW;gBAChD,eAAe;oBAAE,UAAU;oBAAiB,UAAU;gBAAgB;gBACtE,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,cAAc;gBACd,gBAAgB;gBAChB,aAAa;gBACb,sBAAsB;gBACtB,UAAU;oBAAC;oBAAmB;iBAAuB;gBACrD,QAAQ,EAAE;gBACV,QAAQ,EAAE;gBACV,gBAAgB;oBAAE,MAAM;oBAAU,QAAQ;oBAAQ,UAAU;gBAAqB;gBACjF,WAAW;oBAAC;oBAAkB;oBAAW;iBAAU;gBACnD,eAAe;oBAAE,UAAU;oBAAiB,UAAU;gBAAgB;gBACtE,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,cAAc;gBACd,gBAAgB;gBAChB,aAAa;gBACb,sBAAsB;gBACtB,UAAU;oBAAC;oBAAqB;iBAAgB;gBAChD,QAAQ,EAAE;gBACV,QAAQ,EAAE;gBACV,gBAAgB;oBAAE,MAAM;oBAAU,QAAQ;oBAAQ,UAAU;gBAAyB;gBACrF,WAAW;oBAAC;oBAAkB;iBAAkB;gBAChD,eAAe;oBAAE,UAAU;oBAAiB,UAAU;gBAAgB;gBACtE,WAAW;YACb;SACD;QAED,MAAM,eAAe,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,IAAI,cAAc;YAChB,QAAQ;QACV,OAAO;YACL,OAAO,IAAI,CAAC;QACd;QAEF,WAAW;IACb;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM;QAEX,UAAU;QACV,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,MAAM,KAAK,IAAI;gBACf,cAAc,KAAK,YAAY;gBAC/B,gBAAgB,KAAK,cAAc;gBACnC,aAAa,KAAK,WAAW;gBAC7B,sBAAsB,KAAK,oBAAoB;gBAC/C,UAAU,KAAK,QAAQ;gBACvB,gBAAgB,KAAK,cAAc;gBACnC,WAAW,KAAK,SAAS;gBACzB,eAAe,KAAK,aAAa;gBACjC,WAAW,KAAK,SAAS;YAC3B,GACC,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,OAAO,MAAM;YACjB,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,WAAW,IAAI,MAAM,MAAM;YAC7B,QAAQ;gBACN,GAAG,IAAI;gBACP,UAAU;uBAAI,KAAK,QAAQ;oBAAE,WAAW,IAAI;iBAAG;YACjD;YACA,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,MAAM;YACR,QAAQ;gBACN,GAAG,IAAI;gBACP,UAAU,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YACjD;QACF;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,WAAW,IAAI,MAAM,MAAM;YAC7B,QAAQ;gBACN,GAAG,IAAI;gBACP,WAAW;uBAAI,KAAK,SAAS;oBAAE,WAAW,IAAI;iBAAG;YACnD;YACA,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,MAAM;YACR,QAAQ;gBACN,GAAG,IAAI;gBACP,WAAW,KAAK,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YACnD;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,WAAW,IAAI,MAAM,aAAa,IAAI,MAAM,MAAM;YACpD,QAAQ;gBACN,GAAG,IAAI;gBACP,gBAAgB;oBACd,GAAG,KAAK,cAAc;oBACtB,CAAC,WAAW,IAAI,GAAG,EAAE,aAAa,IAAI;gBACxC;YACF;YACA,cAAc;YACd,gBAAgB;QAClB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,MAAM;YACR,MAAM,WAAW;gBAAE,GAAG,KAAK,cAAc;YAAC;YAC1C,OAAO,QAAQ,CAAC,IAAI;YACpB,QAAQ;gBACN,GAAG,IAAI;gBACP,gBAAgB;YAClB;QACF;IACF;IAEA,MAAM,qBAAqB,CAAC,KAAa;QACvC,IAAI,MAAM;YACR,QAAQ;gBACN,GAAG,IAAI;gBACP,eAAe;oBACb,GAAG,KAAK,aAAa;oBACrB,CAAC,IAAI,EAAE;gBACT;YACF;QACF;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,MAAM;YACR,QAAQ;gBACN,GAAG,IAAI;gBACP;YACF;QACF;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;8BAGtC,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;IAGb;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,8OAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAKL,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;IAGb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAAmC;oDAC9B,KAAK,IAAI;;;;;;;0DAE5B,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;0CAMjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,SAAS,kBAAkB;;;;;;;kDAG9B,8OAAC;wCAAK,WAAW,CAAC,2CAA2C,EAC3D,KAAK,SAAS,GACV,gCACA,2BACJ;kDACC,KAAK,SAAS,GAAG,YAAY;;;;;;;;;;;;;;;;;;kCAKpC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAkC;;;;;;;0DAIxD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,OAAO,KAAK,IAAI;gEAChB,UAAU,CAAC,IAAM,QAAQ;wEAAE,GAAG,IAAI;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACzD,WAAU;;;;;;;;;;;;kEAId,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,OAAO,KAAK,SAAS,GAAG,WAAW;gEACnC,UAAU,CAAC,IAAM,QAAQ;wEAAE,GAAG,IAAI;wEAAE,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAS;gEAC3E,WAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,8OAAC;wEAAO,OAAM;kFAAW;;;;;;;;;;;;;;;;;;;;;;;;0DAK/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,OAAO,KAAK,YAAY;gEACxB,UAAU,CAAC,IAAM,QAAQ;wEAAE,GAAG,IAAI;wEAAE,cAAc,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAE;gEAChF,WAAU;;;;;;;;;;;;kEAId,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,OAAO,KAAK,cAAc;gEAC1B,UAAU,CAAC,IAAM,QAAQ;wEAAE,GAAG,IAAI;wEAAE,gBAAgB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oEAAE;gEAClF,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO,KAAK,WAAW;wDACvB,UAAU,CAAC,IAAM,QAAQ;gEAAE,GAAG,IAAI;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAChE,MAAM;wDACN,WAAU;;;;;;;;;;;;0DAId,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO,KAAK,oBAAoB;wDAChC,UAAU,CAAC,IAAM,QAAQ;gEAAE,GAAG,IAAI;gEAAE,sBAAsB,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACzE,MAAM;wDACN,WAAU;;;;;;;;;;;;;;;;;;kDAMhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DAErD,8OAAC;gDAAI,WAAU;0DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC;oEACT,MAAM,cAAc;2EAAI,KAAK,QAAQ;qEAAC;oEACtC,WAAW,CAAC,MAAM,GAAG,EAAE,MAAM,CAAC,KAAK;oEACnC,QAAQ;wEAAE,GAAG,IAAI;wEAAE,UAAU;oEAAY;gEAC3C;gEACA,WAAU;;;;;;0EAEZ,8OAAC;gEACC,SAAS,IAAM,cAAc;gEAC7B,WAAU;0EAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;;;;;;;uDAfP;;;;;;;;;;0DAqBd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,aAAY;wDACZ,WAAU;wDACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;kEAE1C,8OAAC;wDACC,SAAS;wDACT,WAAU;kEAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAMtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DAErD,8OAAC;gDAAI,WAAU;0DACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC5B,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC;oEACT,MAAM,eAAe;2EAAI,KAAK,SAAS;qEAAC;oEACxC,YAAY,CAAC,MAAM,GAAG,EAAE,MAAM,CAAC,KAAK;oEACpC,QAAQ;wEAAE,GAAG,IAAI;wEAAE,WAAW;oEAAa;gEAC7C;gEACA,WAAU;;;;;;0EAEZ,8OAAC;gEACC,SAAS,IAAM,cAAc;gEAC7B,WAAU;0EAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;;;;;;;uDAfP;;;;;;;;;;0DAqBd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,aAAY;wDACZ,WAAU;wDACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;kEAE1C,8OAAC;wDACC,SAAS;wDACT,WAAU;kEAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAMtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DAErD,8OAAC;gDAAI,WAAU;0DACZ,OAAO,OAAO,CAAC,KAAK,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACpD,8OAAC;wDAAc,WAAU;;0EACvB,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC;oEACT,MAAM,WAAW;wEAAE,GAAG,KAAK,cAAc;oEAAC;oEAC1C,OAAO,QAAQ,CAAC,IAAI;oEACpB,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG;oEAC3B,QAAQ;wEAAE,GAAG,IAAI;wEAAE,gBAAgB;oEAAS;gEAC9C;gEACA,WAAU;gEACV,aAAY;;;;;;0EAEd,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC;oEACT,QAAQ;wEACN,GAAG,IAAI;wEACP,gBAAgB;4EACd,GAAG,KAAK,cAAc;4EACtB,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK;wEACvB;oEACF;gEACF;gEACA,WAAU;gEACV,aAAY;;;;;;0EAEd,8OAAC;gEACC,SAAS,IAAM,oBAAoB;gEACnC,WAAU;0EAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;;;;;;;uDAhCP;;;;;;;;;;0DAsCd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,aAAY;wDACZ,WAAU;;;;;;kEAEZ,8OAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAC/C,aAAY;wDACZ,WAAU;wDACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;kEAE1C,8OAAC;wDACC,SAAS;wDACT,WAAU;kEAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAMtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC,mIAAA,CAAA,UAAa;gDACZ,QAAQ,KAAK,EAAE;gDACf,eAAe,KAAK,MAAM;gDAC1B,gBAAgB;;;;;;;;;;;;;;;;;;0CAMtB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAE1C,8OAAC;gEAAK,WAAU;0EAAiB,KAAK,YAAY;;;;;;;;;;;;kEAEpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;kFACtB,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAE1C,8OAAC;gEAAK,WAAU;;oEAAiB,KAAK,cAAc;oEAAC;;;;;;;;;;;;;kEAEvD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAE1C,8OAAC;gEAAK,WAAU;0EAAiB,KAAK,QAAQ,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;kDAM3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAoC;;;;;;;0DAGvD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,OAAO,KAAK,aAAa,CAAC,QAAQ,IAAI;gEACtC,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC9D,aAAY;gEACZ,WAAU;;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,OAAO,KAAK,aAAa,CAAC,QAAQ,IAAI;gEACtC,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC9D,aAAY;gEACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxB,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}