{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { Menu, X, Phone, MapPin } from 'lucide-react';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleNavigation = (href: string) => {\n    if (href.startsWith('#')) {\n      // Якорная ссылка - переходим на главную страницу если не на ней\n      if (pathname !== '/') {\n        router.push('/' + href);\n      } else {\n        // Если уже на главной странице, просто скроллим\n        const element = document.querySelector(href);\n        if (element) {\n          element.scrollIntoView({ behavior: 'smooth' });\n        }\n      }\n    } else {\n      // Обычная ссылка\n      router.push(href);\n    }\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <div className=\"text-2xl font-bold text-altius-blue\">\n              🏸 Altius\n            </div>\n            <div className=\"ml-2 text-sm text-gray-600 hidden sm:block\">\n              Кишинев\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <button\n              onClick={() => handleNavigation('/')}\n              className=\"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer\"\n            >\n              Главная\n            </button>\n            <button\n              onClick={() => handleNavigation('/about')}\n              className=\"text-gray-700 hover:text-altius-lime transition-colors cursor-pointer\"\n            >\n              О нас\n            </button>\n            <button\n              onClick={() => handleNavigation('#halls')}\n              className=\"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer\"\n            >\n              Залы\n            </button>\n            <button\n              onClick={() => handleNavigation('/services')}\n              className=\"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer\"\n            >\n              Услуги\n            </button>\n            <button\n              onClick={() => handleNavigation('/contact')}\n              className=\"text-gray-700 hover:text-altius-lime transition-colors cursor-pointer\"\n            >\n              Контакты\n            </button>\n            <button\n              onClick={() => handleNavigation('/blog')}\n              className=\"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer\"\n            >\n              Блог\n            </button>\n            <Link href=\"/admin\" className=\"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer\">\n              Админ\n            </Link>\n          </nav>\n\n          {/* Contact Info */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Phone className=\"w-4 h-4 mr-1\" />\n              +373 XX XXX XXX\n            </div>\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <MapPin className=\"w-4 h-4 mr-1\" />\n              Кишинев\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden cursor-pointer\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {isMenuOpen ? (\n              <X className=\"w-6 h-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"w-6 h-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <button\n                onClick={() => handleNavigation('/')}\n                className=\"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer\"\n              >\n                Главная\n              </button>\n              <button\n                onClick={() => handleNavigation('/about')}\n                className=\"text-gray-700 hover:text-altius-lime transition-colors text-left cursor-pointer\"\n              >\n                О нас\n              </button>\n              <button\n                onClick={() => handleNavigation('#halls')}\n                className=\"text-gray-700 hover:text-altius-orange transition-colors text-left cursor-pointer\"\n              >\n                Залы\n              </button>\n              <button\n                onClick={() => handleNavigation('/services')}\n                className=\"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer\"\n              >\n                Услуги\n              </button>\n              <button\n                onClick={() => handleNavigation('/contact')}\n                className=\"text-gray-700 hover:text-altius-lime transition-colors text-left cursor-pointer\"\n              >\n                Контакты\n              </button>\n              <button\n                onClick={() => handleNavigation('/blog')}\n                className=\"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer\"\n              >\n                Блог\n              </button>\n              <Link href=\"/admin\" className=\"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer\">\n                Админ\n              </Link>\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center text-sm text-gray-600 mb-2\">\n                  <Phone className=\"w-4 h-4 mr-1\" />\n                  +373 XX XXX XXX\n                </div>\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <MapPin className=\"w-4 h-4 mr-1\" />\n                  Кишинев\n                </div>\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,mBAAmB,CAAC;QACxB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,gEAAgE;YAChE,IAAI,aAAa,KAAK;gBACpB,OAAO,IAAI,CAAC,MAAM;YACpB,OAAO;gBACL,gDAAgD;gBAChD,MAAM,UAAU,SAAS,aAAa,CAAC;gBACvC,IAAI,SAAS;oBACX,QAAQ,cAAc,CAAC;wBAAE,UAAU;oBAAS;gBAC9C;YACF;QACF,OAAO;YACL,iBAAiB;YACjB,OAAO,IAAI,CAAC;QACd;QACA,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CAAsC;;;;;;8CAGrD,8OAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA0E;;;;;;;;;;;;sCAM1G,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE7B,2BACC,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAA0E;;;;;;0CAGxG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Footer.tsx"], "sourcesContent": ["import { Phone, MapPin, Clock, Mail } from 'lucide-react';\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {/* Company Info */}\n          <div>\n            <div className=\"text-2xl font-bold text-altius-lime mb-4\">\n              🏸 Altius\n            </div>\n            <p className=\"text-gray-300 mb-4\">\n              Современный бадминтонный клуб в Кишиневе с профессиональными кортами\n              и удобной системой бронирования.\n            </p>\n            <div className=\"flex items-center text-gray-300 mb-2\">\n              <MapPin className=\"w-4 h-4 mr-2\" />\n              Кишинев, Молдова\n            </div>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Контакты</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center text-gray-300\">\n                <Phone className=\"w-4 h-4 mr-2\" />\n                +373 XX XXX XXX\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <Mail className=\"w-4 h-4 mr-2\" />\n                <EMAIL>\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <MapPin className=\"w-4 h-4 mr-2\" />\n                ул. Примерная, 123, Кишинев\n              </div>\n            </div>\n          </div>\n\n          {/* Working Hours */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Режим работы</h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-gray-300\">\n                <Clock className=\"w-4 h-4 mr-2\" />\n                <div>\n                  <div>Пн-Пт: 06:00 - 23:00</div>\n                  <div>Сб-Вс: 08:00 - 22:00</div>\n                </div>\n              </div>\n            </div>\n            \n            {/* Halls Info */}\n            <div className=\"mt-6\">\n              <h4 className=\"font-semibold mb-2\">Наши залы:</h4>\n              <div className=\"text-sm text-gray-300 space-y-1\">\n                <div>Зал 1: 3 корта</div>\n                <div>Зал 2: 7 кортов</div>\n                <div>Зал 3: 7 кортов</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n          <p>&copy; 2024 Altius Кишинев. Все права защищены.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAA2C;;;;;;8CAG1D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOzC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;kEAAI;;;;;;kEACL,8OAAC;kEAAI;;;;;;;;;;;;;;;;;;;;;;;8CAMX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\n// Get environment variables\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';\n\n// Check if Supabase is properly configured\nconst isSupabaseConfigured =\n  supabaseUrl &&\n  supabaseAnonKey &&\n  supabaseUrl.includes('supabase.co') &&\n  supabaseAnonKey.length > 100; // JWT tokens are long\n\n// Log configuration status for debugging\nconsole.log('Supabase Configuration:', {\n  url: supabaseUrl ? `${supabaseUrl.substring(0, 30)}...` : 'NOT SET',\n  keyLength: supabaseAnonKey.length,\n  isConfigured: isSupabaseConfigured\n});\n\n// Always use the provided values, even if they might be invalid\n// This way we get proper error messages instead of dummy URLs\nexport const supabase = createClient(\n  supabaseUrl || 'https://placeholder.supabase.co',\n  supabaseAnonKey || 'placeholder-key'\n);\n\n// Helper function to check if Supabase is available\nexport const isSupabaseAvailable = () => isSupabaseConfigured;\n\n// Database types\nexport interface Booking {\n  id: string;\n  name: string;\n  phone: string;\n  email?: string;\n  hall_id: number;\n  court: number;\n  date: string;\n  time: string;\n  status: 'pending' | 'confirmed' | 'cancelled';\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Hall {\n  id: number;\n  name: string;\n  courts_count: number;\n  price_per_hour: number;\n  description: string;\n  features: string[];\n  created_at: string;\n}\n\n// Booking functions\nexport const bookingService = {\n  // Get all bookings\n  async getBookings() {\n    const { data, error } = await supabase\n      .from('bookings')\n      .select('*')\n      .order('date', { ascending: true });\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Get bookings for a specific date and hall\n  async getBookingsByDateAndHall(date: string, hallId?: number) {\n    let query = supabase\n      .from('bookings')\n      .select('*')\n      .eq('date', date);\n    \n    if (hallId) {\n      query = query.eq('hall_id', hallId);\n    }\n    \n    const { data, error } = await query;\n    if (error) throw error;\n    return data;\n  },\n\n  // Create a new booking\n  async createBooking(booking: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) {\n    const { data, error } = await supabase\n      .from('bookings')\n      .insert([booking])\n      .select()\n      .single();\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Update booking status\n  async updateBookingStatus(id: string, status: 'pending' | 'confirmed' | 'cancelled') {\n    const { data, error } = await supabase\n      .from('bookings')\n      .update({ status, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single();\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Delete a booking\n  async deleteBooking(id: string) {\n    const { error } = await supabase\n      .from('bookings')\n      .delete()\n      .eq('id', id);\n    \n    if (error) throw error;\n  },\n\n  // Check if a slot is available\n  async isSlotAvailable(hallId: number, court: number, date: string, time: string) {\n    const { data, error } = await supabase\n      .from('bookings')\n      .select('id')\n      .eq('hall_id', hallId)\n      .eq('court', court)\n      .eq('date', date)\n      .eq('time', time)\n      .neq('status', 'cancelled');\n    \n    if (error) throw error;\n    return data.length === 0;\n  }\n};\n\n// Hall functions\nexport const hallService = {\n  // Get all halls\n  async getHalls() {\n    const { data, error } = await supabase\n      .from('halls')\n      .select('*')\n      .order('id', { ascending: true });\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Get a specific hall\n  async getHall(id: number) {\n    const { data, error } = await supabase\n      .from('halls')\n      .select('*')\n      .eq('id', id)\n      .single();\n    \n    if (error) throw error;\n    return data;\n  }\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,4BAA4B;AAC5B,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AAErE,2CAA2C;AAC3C,MAAM,uBACJ,eACA,mBACA,YAAY,QAAQ,CAAC,kBACrB,gBAAgB,MAAM,GAAG,KAAK,sBAAsB;AAEtD,yCAAyC;AACzC,QAAQ,GAAG,CAAC,2BAA2B;IACrC,KAAK,uCAAc,GAAG,YAAY,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC;IACvD,WAAW,gBAAgB,MAAM;IACjC,cAAc;AAChB;AAIO,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACjC,eAAe,mCACf,mBAAmB;AAId,MAAM,sBAAsB,IAAM;AA4BlC,MAAM,iBAAiB;IAC5B,mBAAmB;IACnB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK;QAEnC,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,4CAA4C;IAC5C,MAAM,0BAAyB,IAAY,EAAE,MAAe;QAC1D,IAAI,QAAQ,SACT,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ;QAEd,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,WAAW;QAC9B;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAC9B,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,uBAAuB;IACvB,MAAM,eAAc,OAA0D;QAC5E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAC;SAAQ,EAChB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,wBAAwB;IACxB,MAAM,qBAAoB,EAAU,EAAE,MAA6C;QACjF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAE;YAAQ,YAAY,IAAI,OAAO,WAAW;QAAG,GACtD,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,mBAAmB;IACnB,MAAM,eAAc,EAAU;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,+BAA+B;IAC/B,MAAM,iBAAgB,MAAc,EAAE,KAAa,EAAE,IAAY,EAAE,IAAY;QAC7E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,SAAS,OACZ,EAAE,CAAC,QAAQ,MACX,EAAE,CAAC,QAAQ,MACX,GAAG,CAAC,UAAU;QAEjB,IAAI,OAAO,MAAM;QACjB,OAAO,KAAK,MAAM,KAAK;IACzB;AACF;AAGO,MAAM,cAAc;IACzB,gBAAgB;IAChB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,MAAM;YAAE,WAAW;QAAK;QAEjC,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,SAAQ,EAAU;QACtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 857, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/PostImageUploader.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { supabase } from '@/lib/supabase';\nimport { Upload, X, Image as ImageIcon } from 'lucide-react';\n\ninterface PostImageUploaderProps {\n  currentImage: string;\n  onImageUpdate: (imageUrl: string) => void;\n}\n\nexport default function PostImageUploader({ currentImage, onImageUpdate }: PostImageUploaderProps) {\n  const [uploading, setUploading] = useState(false);\n  const [dragOver, setDragOver] = useState(false);\n\n  const uploadImage = async (file: File) => {\n    if (!file) return;\n\n    // Проверяем тип файла\n    if (!file.type.startsWith('image/')) {\n      alert('Файл должен быть изображением');\n      return;\n    }\n\n    // Проверяем размер файла (максимум 5MB)\n    if (file.size > 5 * 1024 * 1024) {\n      alert('Файл слишком большой. Максимальный размер: 5MB');\n      return;\n    }\n\n    setUploading(true);\n    try {\n      const fileExt = file.name.split('.').pop();\n      const fileName = `posts/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;\n\n      const { error: uploadError } = await supabase.storage\n        .from('post-images')\n        .upload(fileName, file, {\n          cacheControl: '3600',\n          upsert: false\n        });\n\n      if (uploadError) {\n        console.error('Upload error:', uploadError);\n        alert(`Ошибка загрузки: ${uploadError.message}`);\n        return;\n      }\n\n      const { data } = supabase.storage\n        .from('post-images')\n        .getPublicUrl(fileName);\n\n      onImageUpdate(data.publicUrl);\n      alert('Изображение успешно загружено!');\n    } catch (error) {\n      console.error('Error uploading image:', error);\n      alert('Произошла ошибка при загрузке изображения');\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setDragOver(false);\n    \n    const files = Array.from(e.dataTransfer.files);\n    if (files.length > 0) {\n      uploadImage(files[0]);\n    }\n  };\n\n  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (files && files.length > 0) {\n      uploadImage(files[0]);\n    }\n  };\n\n  const removeImage = () => {\n    onImageUpdate('');\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Current Image */}\n      {currentImage && (\n        <div className=\"relative\">\n          <img\n            src={currentImage}\n            alt=\"Featured image\"\n            className=\"w-full h-48 object-cover rounded-lg\"\n            onError={(e) => {\n              e.currentTarget.style.display = 'none';\n            }}\n          />\n          <button\n            onClick={removeImage}\n            className=\"absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors\"\n            title=\"Удалить изображение\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        </div>\n      )}\n\n      {/* Upload Area */}\n      <div\n        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${\n          dragOver\n            ? 'border-altius-blue bg-blue-50'\n            : 'border-gray-300 hover:border-gray-400'\n        } ${uploading ? 'opacity-50 pointer-events-none' : ''}`}\n        onDrop={handleDrop}\n        onDragOver={(e) => {\n          e.preventDefault();\n          setDragOver(true);\n        }}\n        onDragLeave={() => setDragOver(false)}\n      >\n        {uploading ? (\n          <div className=\"flex flex-col items-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-altius-blue mb-2\"></div>\n            <p className=\"text-gray-600\">Загрузка изображения...</p>\n          </div>\n        ) : (\n          <div className=\"flex flex-col items-center\">\n            <ImageIcon className=\"w-12 h-12 text-gray-400 mb-4\" />\n            <p className=\"text-gray-600 mb-2\">\n              Перетащите изображение сюда или\n            </p>\n            <label className=\"cursor-pointer bg-altius-blue text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center\">\n              <Upload className=\"w-4 h-4 mr-2\" />\n              Выберите файл\n              <input\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleFileSelect}\n                className=\"hidden\"\n              />\n            </label>\n            <p className=\"text-sm text-gray-500 mt-2\">\n              PNG, JPG, GIF до 5MB\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* URL Input */}\n      <div className=\"text-center text-gray-500\">\n        <span className=\"text-sm\">или</span>\n      </div>\n      \n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          URL изображения\n        </label>\n        <input\n          type=\"url\"\n          value={currentImage}\n          onChange={(e) => onImageUpdate(e.target.value)}\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n          placeholder=\"https://example.com/image.jpg\"\n        />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAWe,SAAS,kBAAkB,EAAE,YAAY,EAAE,aAAa,EAA0B;IAC/F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,cAAc,OAAO;QACzB,IAAI,CAAC,MAAM;QAEX,sBAAsB;QACtB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,MAAM;YACN;QACF;QAEA,wCAAwC;QACxC,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,MAAM;YACN;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;YACxC,MAAM,WAAW,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,EAAE,SAAS;YAE5F,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAClD,IAAI,CAAC,eACL,MAAM,CAAC,UAAU,MAAM;gBACtB,cAAc;gBACd,QAAQ;YACV;YAEF,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,iBAAiB;gBAC/B,MAAM,CAAC,iBAAiB,EAAE,YAAY,OAAO,EAAE;gBAC/C;YACF;YAEA,MAAM,EAAE,IAAI,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAC9B,IAAI,CAAC,eACL,YAAY,CAAC;YAEhB,cAAc,KAAK,SAAS;YAC5B,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,YAAY;QAEZ,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;QAC7C,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,YAAY,KAAK,CAAC,EAAE;QACtB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,YAAY,KAAK,CAAC,EAAE;QACtB;IACF;IAEA,MAAM,cAAc;QAClB,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,8BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAK;wBACL,KAAI;wBACJ,WAAU;wBACV,SAAS,CAAC;4BACR,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;wBAClC;;;;;;kCAEF,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAMnB,8OAAC;gBACC,WAAW,CAAC,oEAAoE,EAC9E,WACI,kCACA,wCACL,CAAC,EAAE,YAAY,mCAAmC,IAAI;gBACvD,QAAQ;gBACR,YAAY,CAAC;oBACX,EAAE,cAAc;oBAChB,YAAY;gBACd;gBACA,aAAa,IAAM,YAAY;0BAE9B,0BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;yCAG/B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAA,CAAA,QAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAM,WAAU;;8CACf,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;8CAEnC,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,UAAU;oCACV,WAAU;;;;;;;;;;;;sCAGd,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;0BAQhD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;0BAG5B,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,WAAU;wBACV,aAAY;;;;;;;;;;;;;;;;;;AAKtB", "debugId": null}}, {"offset": {"line": 1116, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/app/admin/posts/new/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport PostImageUploader from '@/components/PostImageUploader';\nimport { supabase } from '@/lib/supabase';\nimport { PostFormData } from '@/types';\nimport { \n  ArrowLeft,\n  Save,\n  Eye,\n  Calendar,\n  MapPin,\n  Tag,\n  Image as ImageIcon,\n  Type,\n  FileText,\n  User\n} from 'lucide-react';\n\nexport default function NewPostPage() {\n  const router = useRouter();\n  const [saving, setSaving] = useState(false);\n  const [formData, setFormData] = useState<PostFormData>({\n    title: '',\n    slug: '',\n    excerpt: '',\n    content: '',\n    featured_image: '',\n    category: 'post',\n    status: 'draft',\n    author_name: 'Altius Admin',\n    tags: [],\n    event_date: '',\n    event_location: '',\n    meta_description: ''\n  });\n\n  const generateSlug = (title: string) => {\n    return title\n      .toLowerCase()\n      .replace(/[а-я]/g, (char) => {\n        const map: { [key: string]: string } = {\n          'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo',\n          'ж': 'zh', 'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm',\n          'н': 'n', 'о': 'o', 'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u',\n          'ф': 'f', 'х': 'h', 'ц': 'ts', 'ч': 'ch', 'ш': 'sh', 'щ': 'sch',\n          'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya'\n        };\n        return map[char] || char;\n      })\n      .replace(/[^a-z0-9]+/g, '-')\n      .replace(/^-+|-+$/g, '');\n  };\n\n  const handleTitleChange = (title: string) => {\n    setFormData(prev => ({\n      ...prev,\n      title,\n      slug: generateSlug(title)\n    }));\n  };\n\n  const handleTagsChange = (tagsString: string) => {\n    const tags = tagsString.split(',').map(tag => tag.trim()).filter(tag => tag);\n    setFormData(prev => ({ ...prev, tags }));\n  };\n\n  const handleSave = async (status: 'draft' | 'published') => {\n    if (!formData.title.trim() || !formData.content.trim()) {\n      alert('Заполните обязательные поля: заголовок и содержание');\n      return;\n    }\n\n    setSaving(true);\n    try {\n      const postData = {\n        ...formData,\n        status,\n        slug: formData.slug || generateSlug(formData.title),\n        event_date: formData.event_date || null,\n        event_location: formData.event_location || null,\n        meta_description: formData.meta_description || formData.excerpt\n      };\n\n      const { data, error } = await supabase\n        .from('posts')\n        .insert([postData])\n        .select()\n        .single();\n\n      if (error) {\n        console.error('Error creating post:', error);\n        alert('Ошибка при создании поста: ' + error.message);\n        return;\n      }\n\n      console.log('Post created successfully:', data);\n      alert(`Пост ${status === 'published' ? 'опубликован' : 'сохранен как черновик'}!`);\n      router.push('/admin/posts');\n    } catch (error) {\n      console.error('Error creating post:', error);\n      alert('Произошла ошибка при создании поста');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-8\">\n          <div className=\"flex items-center\">\n            <Link\n              href=\"/admin/posts\"\n              className=\"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors\"\n            >\n              <ArrowLeft className=\"w-5 h-5\" />\n            </Link>\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Создать пост</h1>\n              <p className=\"text-gray-600 mt-1\">Новая запись в блоге или событие</p>\n            </div>\n          </div>\n          \n          <div className=\"flex space-x-3\">\n            <button\n              onClick={() => handleSave('draft')}\n              disabled={saving}\n              className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 flex items-center\"\n            >\n              <Save className=\"w-4 h-4 mr-2\" />\n              Сохранить черновик\n            </button>\n            <button\n              onClick={() => handleSave('published')}\n              disabled={saving}\n              className=\"px-4 py-2 bg-altius-blue text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center\"\n            >\n              <Eye className=\"w-4 h-4 mr-2\" />\n              Опубликовать\n            </button>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            {/* Title */}\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                <Type className=\"w-4 h-4 inline mr-2\" />\n                Заголовок *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.title}\n                onChange={(e) => handleTitleChange(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                placeholder=\"Введите заголовок поста\"\n              />\n            </div>\n\n            {/* Slug */}\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                URL (slug)\n              </label>\n              <input\n                type=\"text\"\n                value={formData.slug}\n                onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                placeholder=\"url-адрес-поста\"\n              />\n              <p className=\"text-sm text-gray-500 mt-1\">\n                URL: /blog/{formData.slug || 'url-адрес-поста'}\n              </p>\n            </div>\n\n            {/* Excerpt */}\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                <FileText className=\"w-4 h-4 inline mr-2\" />\n                Краткое описание\n              </label>\n              <textarea\n                value={formData.excerpt}\n                onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                placeholder=\"Краткое описание для превью\"\n              />\n            </div>\n\n            {/* Content */}\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Содержание *\n              </label>\n              <textarea\n                value={formData.content}\n                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}\n                rows={15}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-altius-blue focus:border-transparent font-mono text-sm\"\n                placeholder=\"Содержание поста (поддерживается HTML)\"\n              />\n              <p className=\"text-sm text-gray-500 mt-2\">\n                Поддерживается HTML разметка: &lt;h2&gt;, &lt;p&gt;, &lt;ul&gt;, &lt;li&gt;, &lt;strong&gt;, &lt;em&gt;\n              </p>\n            </div>\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-6\">\n            {/* Category */}\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Категория\n              </label>\n              <select\n                value={formData.category}\n                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value as 'post' | 'event' }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n              >\n                <option value=\"post\">Новость</option>\n                <option value=\"event\">Событие</option>\n              </select>\n            </div>\n\n            {/* Event Details */}\n            {formData.category === 'event' && (\n              <div className=\"bg-white rounded-lg shadow-md p-6\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Детали события</h3>\n                \n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      <Calendar className=\"w-4 h-4 inline mr-2\" />\n                      Дата и время\n                    </label>\n                    <input\n                      type=\"datetime-local\"\n                      value={formData.event_date}\n                      onChange={(e) => setFormData(prev => ({ ...prev, event_date: e.target.value }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      <MapPin className=\"w-4 h-4 inline mr-2\" />\n                      Место проведения\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.event_location}\n                      onChange={(e) => setFormData(prev => ({ ...prev, event_location: e.target.value }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                      placeholder=\"Зал №1, Altius\"\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Featured Image */}\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-4\">\n                <ImageIcon className=\"w-4 h-4 inline mr-2\" />\n                Изображение поста\n              </label>\n              <PostImageUploader\n                currentImage={formData.featured_image}\n                onImageUpdate={(imageUrl) => setFormData(prev => ({ ...prev, featured_image: imageUrl }))}\n              />\n            </div>\n\n            {/* Tags */}\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                <Tag className=\"w-4 h-4 inline mr-2\" />\n                Теги\n              </label>\n              <input\n                type=\"text\"\n                value={formData.tags.join(', ')}\n                onChange={(e) => handleTagsChange(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                placeholder=\"тег1, тег2, тег3\"\n              />\n              <p className=\"text-sm text-gray-500 mt-1\">\n                Разделяйте теги запятыми\n              </p>\n            </div>\n\n            {/* Author */}\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                <User className=\"w-4 h-4 inline mr-2\" />\n                Автор\n              </label>\n              <input\n                type=\"text\"\n                value={formData.author_name}\n                onChange={(e) => setFormData(prev => ({ ...prev, author_name: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                placeholder=\"Имя автора\"\n              />\n            </div>\n\n            {/* Meta Description */}\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                SEO описание\n              </label>\n              <textarea\n                value={formData.meta_description}\n                onChange={(e) => setFormData(prev => ({ ...prev, meta_description: e.target.value }))}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-altius-blue focus:border-transparent\"\n                placeholder=\"Описание для поисковых систем\"\n              />\n            </div>\n          </div>\n        </div>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;AAuBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QACrD,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS;QACT,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,aAAa;QACb,MAAM,EAAE;QACR,YAAY;QACZ,gBAAgB;QAChB,kBAAkB;IACpB;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,UAAU,CAAC;YAClB,MAAM,MAAiC;gBACrC,KAAK;gBAAK,KAAK;gBAAK,KAAK;gBAAK,KAAK;gBAAK,KAAK;gBAAK,KAAK;gBAAK,KAAK;gBACjE,KAAK;gBAAM,KAAK;gBAAK,KAAK;gBAAK,KAAK;gBAAK,KAAK;gBAAK,KAAK;gBAAK,KAAK;gBAClE,KAAK;gBAAK,KAAK;gBAAK,KAAK;gBAAK,KAAK;gBAAK,KAAK;gBAAK,KAAK;gBAAK,KAAK;gBACjE,KAAK;gBAAK,KAAK;gBAAK,KAAK;gBAAM,KAAK;gBAAM,KAAK;gBAAM,KAAK;gBAC1D,KAAK;gBAAI,KAAK;gBAAK,KAAK;gBAAI,KAAK;gBAAK,KAAK;gBAAM,KAAK;YACxD;YACA,OAAO,GAAG,CAAC,KAAK,IAAI;QACtB,GACC,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP;gBACA,MAAM,aAAa;YACrB,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,WAAW,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM,CAAC,CAAA,MAAO;QACxE,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAK,CAAC;IACxC;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YACtD,MAAM;YACN;QACF;QAEA,UAAU;QACV,IAAI;YACF,MAAM,WAAW;gBACf,GAAG,QAAQ;gBACX;gBACA,MAAM,SAAS,IAAI,IAAI,aAAa,SAAS,KAAK;gBAClD,YAAY,SAAS,UAAU,IAAI;gBACnC,gBAAgB,SAAS,cAAc,IAAI;gBAC3C,kBAAkB,SAAS,gBAAgB,IAAI,SAAS,OAAO;YACjE;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;gBAAC;aAAS,EACjB,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,MAAM,gCAAgC,MAAM,OAAO;gBACnD;YACF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,MAAM,CAAC,KAAK,EAAE,WAAW,cAAc,gBAAgB,wBAAwB,CAAC,CAAC;YACjF,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR,SAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;0CAItC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,WAAW;wCAC1B,UAAU;wCACV,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,8OAAC;wCACC,SAAS,IAAM,WAAW;wCAC1B,UAAU;wCACV,WAAU;;0DAEV,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;kCAMtC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG1C,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDACjD,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACvE,WAAU;gDACV,aAAY;;;;;;0DAEd,8OAAC;gDAAE,WAAU;;oDAA6B;oDAC5B,SAAS,IAAI,IAAI;;;;;;;;;;;;;kDAKjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG9C,8OAAC;gDACC,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC1E,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC1E,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;0DAEd,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAO9C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAqB,CAAC;gDAC/F,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;;;;;;;oCAKzB,SAAS,QAAQ,KAAK,yBACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DAEvD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;kFACf,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAwB;;;;;;;0EAG9C,8OAAC;gEACC,MAAK;gEACL,OAAO,SAAS,UAAU;gEAC1B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEAC7E,WAAU;;;;;;;;;;;;kEAId,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;kFACf,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAwB;;;;;;;0EAG5C,8OAAC;gEACC,MAAK;gEACL,OAAO,SAAS,cAAc;gEAC9B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEACjF,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;kDAQtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC,oMAAA,CAAA,QAAS;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG/C,8OAAC,uIAAA,CAAA,UAAiB;gDAChB,cAAc,SAAS,cAAc;gDACrC,eAAe,CAAC,WAAa,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,gBAAgB;wDAAS,CAAC;;;;;;;;;;;;kDAK3F,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAGzC,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC;gDAC1B,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAChD,WAAU;gDACV,aAAY;;;;;;0DAEd,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAM5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG1C,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC9E,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,SAAS,gBAAgB;gDAChC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACnF,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtB,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}