{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Menu, X, Phone, MapPin } from 'lucide-react';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl font-bold text-blue-600\">\n              🏸 BadmintonClub\n            </div>\n            <div className=\"ml-2 text-sm text-gray-600 hidden sm:block\">\n              Кишинев\n            </div>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <a href=\"#home\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Главная\n            </a>\n            <a href=\"#halls\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Залы\n            </a>\n            <a href=\"#booking\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Бронирование\n            </a>\n            <a href=\"#contact\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Контакты\n            </a>\n            <a href=\"/admin\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Админ\n            </a>\n          </nav>\n\n          {/* Contact Info */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Phone className=\"w-4 h-4 mr-1\" />\n              +373 XX XXX XXX\n            </div>\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <MapPin className=\"w-4 h-4 mr-1\" />\n              Кишинев\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {isMenuOpen ? (\n              <X className=\"w-6 h-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"w-6 h-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <a href=\"#home\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Главная\n              </a>\n              <a href=\"#halls\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Залы\n              </a>\n              <a href=\"#booking\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Бронирование\n              </a>\n              <a href=\"#contact\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Контакты\n              </a>\n              <a href=\"/admin\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Админ\n              </a>\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center text-sm text-gray-600 mb-2\">\n                  <Phone className=\"w-4 h-4 mr-1\" />\n                  +373 XX XXX XXX\n                </div>\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <MapPin className=\"w-4 h-4 mr-1\" />\n                  Кишинев\n                </div>\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAmC;;;;;;8CAGlD,8OAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAQ,WAAU;8CAAsD;;;;;;8CAGhF,8OAAC;oCAAE,MAAK;oCAAS,WAAU;8CAAsD;;;;;;8CAGjF,8OAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAsD;;;;;;8CAGnF,8OAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAsD;;;;;;8CAGnF,8OAAC;oCAAE,MAAK;oCAAS,WAAU;8CAAsD;;;;;;;;;;;;sCAMnF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE7B,2BACC,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,MAAK;gCAAQ,WAAU;0CAAsD;;;;;;0CAGhF,8OAAC;gCAAE,MAAK;gCAAS,WAAU;0CAAsD;;;;;;0CAGjF,8OAAC;gCAAE,MAAK;gCAAW,WAAU;0CAAsD;;;;;;0CAGnF,8OAAC;gCAAE,MAAK;gCAAW,WAAU;0CAAsD;;;;;;0CAGnF,8OAAC;gCAAE,MAAK;gCAAS,WAAU;0CAAsD;;;;;;0CAGjF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD", "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Footer.tsx"], "sourcesContent": ["import { Phone, MapPin, Clock, Mail } from 'lucide-react';\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {/* Company Info */}\n          <div>\n            <div className=\"text-2xl font-bold text-blue-400 mb-4\">\n              🏸 BadmintonClub\n            </div>\n            <p className=\"text-gray-300 mb-4\">\n              Современный бадминтонный клуб в Кишиневе с профессиональными кортами \n              и удобной системой бронирования.\n            </p>\n            <div className=\"flex items-center text-gray-300 mb-2\">\n              <MapPin className=\"w-4 h-4 mr-2\" />\n              Кишинев, Молдова\n            </div>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Контакты</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center text-gray-300\">\n                <Phone className=\"w-4 h-4 mr-2\" />\n                +373 XX XXX XXX\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <Mail className=\"w-4 h-4 mr-2\" />\n                <EMAIL>\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <MapPin className=\"w-4 h-4 mr-2\" />\n                ул. Примерная, 123, Кишинев\n              </div>\n            </div>\n          </div>\n\n          {/* Working Hours */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Режим работы</h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-gray-300\">\n                <Clock className=\"w-4 h-4 mr-2\" />\n                <div>\n                  <div>Пн-Пт: 06:00 - 23:00</div>\n                  <div>Сб-Вс: 08:00 - 22:00</div>\n                </div>\n              </div>\n            </div>\n            \n            {/* Halls Info */}\n            <div className=\"mt-6\">\n              <h4 className=\"font-semibold mb-2\">Наши залы:</h4>\n              <div className=\"text-sm text-gray-300 space-y-1\">\n                <div>Зал 1: 3 корта</div>\n                <div>Зал 2: 7 кортов</div>\n                <div>Зал 3: 7 кортов</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n          <p>&copy; 2024 BadmintonClub Кишинев. Все права защищены.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;8CAGvD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOzC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;kEAAI;;;;;;kEACL,8OAAC;kEAAI;;;;;;;;;;;;;;;;;;;;;;;8CAMX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/BookingCalendar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { format, addDays, startOfWeek, isSameDay, isToday } from 'date-fns';\nimport { ru } from 'date-fns/locale';\nimport { ChevronLeft, ChevronRight, Clock } from 'lucide-react';\n\ninterface TimeSlot {\n  time: string;\n  available: boolean;\n  court?: number;\n}\n\ninterface BookingCalendarProps {\n  hallId: number;\n  onSlotSelect: (date: Date, time: string, court: number) => void;\n}\n\nexport default function BookingCalendar({ hallId, onSlotSelect }: BookingCalendarProps) {\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [currentWeek, setCurrentWeek] = useState(startOfWeek(new Date(), { weekStartsOn: 1 }));\n\n  // Генерируем временные слоты с 6:00 до 23:00\n  const timeSlots: string[] = [];\n  for (let hour = 6; hour <= 22; hour++) {\n    timeSlots.push(`${hour.toString().padStart(2, '0')}:00`);\n    timeSlots.push(`${hour.toString().padStart(2, '0')}:30`);\n  }\n\n  // Получаем количество кортов для зала\n  const getCourtCount = (hallId: number) => {\n    switch (hallId) {\n      case 1: return 3;\n      case 2: return 7;\n      case 3: return 7;\n      default: return 3;\n    }\n  };\n\n  const courtCount = getCourtCount(hallId);\n\n  // Генерируем дни недели\n  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(currentWeek, i));\n\n  // Симуляция занятых слотов (в реальном приложении это будет из API)\n  const isSlotBooked = (date: Date, time: string, court: number) => {\n    // Случайная логика для демонстрации\n    const dateStr = format(date, 'yyyy-MM-dd');\n    const key = `${dateStr}-${time}-${court}`;\n    return Math.random() > 0.7; // 30% слотов заняты\n  };\n\n  const goToPreviousWeek = () => {\n    setCurrentWeek(addDays(currentWeek, -7));\n  };\n\n  const goToNextWeek = () => {\n    setCurrentWeek(addDays(currentWeek, 7));\n  };\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg p-6\">\n      <div className=\"mb-6\">\n        <h3 className=\"text-xl font-bold text-gray-900 mb-2\">\n          Выберите дату и время\n        </h3>\n        <p className=\"text-gray-600\">\n          Зал {hallId} • {courtCount} кортов\n        </p>\n      </div>\n\n      {/* Week Navigation */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <button\n          onClick={goToPreviousWeek}\n          className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n        >\n          <ChevronLeft className=\"w-5 h-5\" />\n        </button>\n        \n        <div className=\"text-lg font-semibold\">\n          {format(currentWeek, 'MMMM yyyy', { locale: ru })}\n        </div>\n        \n        <button\n          onClick={goToNextWeek}\n          className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n        >\n          <ChevronRight className=\"w-5 h-5\" />\n        </button>\n      </div>\n\n      {/* Days of Week */}\n      <div className=\"grid grid-cols-7 gap-2 mb-4\">\n        {weekDays.map((day, index) => (\n          <button\n            key={index}\n            onClick={() => setSelectedDate(day)}\n            className={`p-3 text-center rounded-lg transition-colors ${\n              isSameDay(day, selectedDate)\n                ? 'bg-blue-600 text-white'\n                : isToday(day)\n                ? 'bg-blue-100 text-blue-600'\n                : 'hover:bg-gray-100'\n            }`}\n          >\n            <div className=\"text-xs text-gray-500 mb-1\">\n              {format(day, 'EEE', { locale: ru })}\n            </div>\n            <div className=\"font-semibold\">\n              {format(day, 'd')}\n            </div>\n          </button>\n        ))}\n      </div>\n\n      {/* Time Slots */}\n      <div className=\"max-h-96 overflow-y-auto\">\n        <div className=\"space-y-2\">\n          {timeSlots.map((time) => (\n            <div key={time} className=\"border rounded-lg p-3\">\n              <div className=\"flex items-center mb-2\">\n                <Clock className=\"w-4 h-4 mr-2 text-gray-500\" />\n                <span className=\"font-medium\">{time}</span>\n              </div>\n              \n              <div className=\"grid grid-cols-3 gap-2\">\n                {Array.from({ length: courtCount }, (_, courtIndex) => {\n                  const court = courtIndex + 1;\n                  const isBooked = isSlotBooked(selectedDate, time, court);\n                  \n                  return (\n                    <button\n                      key={court}\n                      onClick={() => !isBooked && onSlotSelect(selectedDate, time, court)}\n                      disabled={isBooked}\n                      className={`p-2 text-sm rounded transition-colors ${\n                        isBooked\n                          ? 'bg-red-100 text-red-600 cursor-not-allowed'\n                          : 'bg-green-100 text-green-600 hover:bg-green-200'\n                      }`}\n                    >\n                      Корт {court}\n                      {isBooked && (\n                        <div className=\"text-xs\">Занят</div>\n                      )}\n                    </button>\n                  );\n                })}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AALA;;;;;;AAkBe,SAAS,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAwB;IACpF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,EAAE,IAAI,QAAQ;QAAE,cAAc;IAAE;IAEzF,6CAA6C;IAC7C,MAAM,YAAsB,EAAE;IAC9B,IAAK,IAAI,OAAO,GAAG,QAAQ,IAAI,OAAQ;QACrC,UAAU,IAAI,CAAC,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;QACvD,UAAU,IAAI,CAAC,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;IACzD;IAEA,sCAAsC;IACtC,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,cAAc;IAEjC,wBAAwB;IACxB,MAAM,WAAW,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAE,GAAG,CAAC,GAAG,IAAM,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAE1E,oEAAoE;IACpE,MAAM,eAAe,CAAC,MAAY,MAAc;QAC9C,oCAAoC;QACpC,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QAC7B,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE,KAAK,CAAC,EAAE,OAAO;QACzC,OAAO,KAAK,MAAM,KAAK,KAAK,oBAAoB;IAClD;IAEA,MAAM,mBAAmB;QACvB,eAAe,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,aAAa,CAAC;IACvC;IAEA,MAAM,eAAe;QACnB,eAAe,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IACtC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;kCAGrD,8OAAC;wBAAE,WAAU;;4BAAgB;4BACtB;4BAAO;4BAAI;4BAAW;;;;;;;;;;;;;0BAK/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAGzB,8OAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,aAAa,aAAa;4BAAE,QAAQ,2IAAA,CAAA,KAAE;wBAAC;;;;;;kCAGjD,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAK5B,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,8OAAC;wBAEC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,6CAA6C,EACvD,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,KAAK,gBACX,2BACA,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,OACR,8BACA,qBACJ;;0CAEF,8OAAC;gCAAI,WAAU;0CACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,OAAO;oCAAE,QAAQ,2IAAA,CAAA,KAAE;gCAAC;;;;;;0CAEnC,8OAAC;gCAAI,WAAU;0CACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK;;;;;;;uBAdV;;;;;;;;;;0BAqBX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;4BAAe,WAAU;;8CACxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAGjC,8OAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAW,GAAG,CAAC,GAAG;wCACtC,MAAM,QAAQ,aAAa;wCAC3B,MAAM,WAAW,aAAa,cAAc,MAAM;wCAElD,qBACE,8OAAC;4CAEC,SAAS,IAAM,CAAC,YAAY,aAAa,cAAc,MAAM;4CAC7D,UAAU;4CACV,WAAW,CAAC,sCAAsC,EAChD,WACI,+CACA,kDACJ;;gDACH;gDACO;gDACL,0BACC,8OAAC;oDAAI,WAAU;8DAAU;;;;;;;2CAXtB;;;;;oCAeX;;;;;;;2BA5BM;;;;;;;;;;;;;;;;;;;;;AAoCtB", "debugId": null}}, {"offset": {"line": 889, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/BookingForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { format } from 'date-fns';\nimport { ru } from 'date-fns/locale';\nimport { User, Phone, Calendar, Clock, MapPin, CreditCard, QrCode } from 'lucide-react';\n\nconst bookingSchema = z.object({\n  name: z.string().min(2, 'Имя должно содержать минимум 2 символа'),\n  phone: z.string().min(8, 'Введите корректный номер телефона'),\n  email: z.string().email('Введите корректный email').optional().or(z.literal('')),\n});\n\ntype BookingFormData = z.infer<typeof bookingSchema>;\n\ninterface BookingFormProps {\n  hallId: number;\n  date: Date;\n  time: string;\n  court: number;\n  onSubmit: (data: BookingFormData & { hallId: number; date: Date; time: string; court: number }) => void;\n  onCancel: () => void;\n}\n\nexport default function BookingForm({ hallId, date, time, court, onSubmit, onCancel }: BookingFormProps) {\n  const [showPayment, setShowPayment] = useState(false);\n  const [paymentMethod, setPaymentMethod] = useState<'qr' | 'transfer'>('qr');\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting }\n  } = useForm<BookingFormData>({\n    resolver: zodResolver(bookingSchema)\n  });\n\n  const pricePerHour = 150; // лей за час\n\n  const handleFormSubmit = (data: BookingFormData) => {\n    setShowPayment(true);\n  };\n\n  const handlePaymentConfirm = () => {\n    const formData = new FormData(document.querySelector('form') as HTMLFormElement);\n    const data = {\n      name: formData.get('name') as string,\n      phone: formData.get('phone') as string,\n      email: formData.get('email') as string,\n      hallId,\n      date,\n      time,\n      court\n    };\n    onSubmit(data);\n  };\n\n  if (showPayment) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-lg p-6\">\n        <h3 className=\"text-xl font-bold text-gray-900 mb-6\">Оплата бронирования</h3>\n        \n        {/* Booking Summary */}\n        <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n          <h4 className=\"font-semibold mb-2\">Детали бронирования:</h4>\n          <div className=\"space-y-1 text-sm text-gray-600\">\n            <div>Зал {hallId}, Корт {court}</div>\n            <div>{format(date, 'dd MMMM yyyy', { locale: ru })}</div>\n            <div>{time}</div>\n            <div className=\"font-semibold text-lg text-blue-600 mt-2\">\n              Стоимость: {pricePerHour} лей\n            </div>\n          </div>\n        </div>\n\n        {/* Payment Method Selection */}\n        <div className=\"mb-6\">\n          <h4 className=\"font-semibold mb-3\">Способ оплаты:</h4>\n          <div className=\"grid grid-cols-2 gap-3\">\n            <button\n              onClick={() => setPaymentMethod('qr')}\n              className={`p-3 border rounded-lg flex items-center justify-center ${\n                paymentMethod === 'qr' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'\n              }`}\n            >\n              <QrCode className=\"w-5 h-5 mr-2\" />\n              QR-код\n            </button>\n            <button\n              onClick={() => setPaymentMethod('transfer')}\n              className={`p-3 border rounded-lg flex items-center justify-center ${\n                paymentMethod === 'transfer' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'\n              }`}\n            >\n              <CreditCard className=\"w-5 h-5 mr-2\" />\n              Перевод\n            </button>\n          </div>\n        </div>\n\n        {/* Payment Details */}\n        {paymentMethod === 'qr' && (\n          <div className=\"text-center mb-6\">\n            <div className=\"w-48 h-48 bg-gray-200 rounded-lg mx-auto mb-4 flex items-center justify-center\">\n              <QrCode className=\"w-16 h-16 text-gray-400\" />\n            </div>\n            <p className=\"text-sm text-gray-600\">\n              Отсканируйте QR-код для оплаты через банковское приложение\n            </p>\n          </div>\n        )}\n\n        {paymentMethod === 'transfer' && (\n          <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n            <h4 className=\"font-semibold mb-3\">Реквизиты для перевода:</h4>\n            <div className=\"space-y-2 text-sm\">\n              <div><strong>Получатель:</strong> BadmintonClub SRL</div>\n              <div><strong>Банк:</strong> Moldova Agroindbank</div>\n              <div><strong>IBAN:</strong> ************************</div>\n              <div><strong>Сумма:</strong> {pricePerHour} лей</div>\n              <div><strong>Назначение:</strong> Бронирование зал {hallId}, корт {court}, {format(date, 'dd.MM.yyyy')} {time}</div>\n            </div>\n          </div>\n        )}\n\n        {/* Action Buttons */}\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={onCancel}\n            className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            Отмена\n          </button>\n          <button\n            onClick={handlePaymentConfirm}\n            className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Подтвердить оплату\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg p-6\">\n      <h3 className=\"text-xl font-bold text-gray-900 mb-6\">Форма бронирования</h3>\n      \n      {/* Booking Details */}\n      <div className=\"bg-blue-50 rounded-lg p-4 mb-6\">\n        <div className=\"flex items-center text-blue-800 mb-2\">\n          <MapPin className=\"w-4 h-4 mr-2\" />\n          <span className=\"font-semibold\">Зал {hallId}, Корт {court}</span>\n        </div>\n        <div className=\"flex items-center text-blue-700 mb-1\">\n          <Calendar className=\"w-4 h-4 mr-2\" />\n          <span>{format(date, 'dd MMMM yyyy', { locale: ru })}</span>\n        </div>\n        <div className=\"flex items-center text-blue-700\">\n          <Clock className=\"w-4 h-4 mr-2\" />\n          <span>{time}</span>\n        </div>\n      </div>\n\n      <form onSubmit={handleSubmit(handleFormSubmit)} className=\"space-y-4\">\n        {/* Name Field */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Имя *\n          </label>\n          <div className=\"relative\">\n            <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n            <input\n              {...register('name')}\n              type=\"text\"\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"Введите ваше имя\"\n            />\n          </div>\n          {errors.name && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.name.message}</p>\n          )}\n        </div>\n\n        {/* Phone Field */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Телефон *\n          </label>\n          <div className=\"relative\">\n            <Phone className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n            <input\n              {...register('phone')}\n              type=\"tel\"\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"+373 XX XXX XXX\"\n            />\n          </div>\n          {errors.phone && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.phone.message}</p>\n          )}\n        </div>\n\n        {/* Email Field (Optional) */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Email (необязательно)\n          </label>\n          <input\n            {...register('email')}\n            type=\"email\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            placeholder=\"<EMAIL>\"\n          />\n          {errors.email && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n          )}\n        </div>\n\n        {/* Price Info */}\n        <div className=\"bg-gray-50 rounded-lg p-3\">\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600\">Стоимость (1 час):</span>\n            <span className=\"text-lg font-bold text-blue-600\">{pricePerHour} лей</span>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex space-x-3 pt-4\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            Отмена\n          </button>\n          <button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\"\n          >\n            {isSubmitting ? 'Обработка...' : 'Продолжить к оплате'}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAUA,MAAM,gBAAgB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,4BAA4B,QAAQ,GAAG,EAAE,CAAC,6KAAA,CAAA,IAAC,CAAC,OAAO,CAAC;AAC9E;AAae,SAAS,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAoB;IACrG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAEtE,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACpC,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,eAAe,KAAK,aAAa;IAEvC,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,MAAM,uBAAuB;QAC3B,MAAM,WAAW,IAAI,SAAS,SAAS,aAAa,CAAC;QACrD,MAAM,OAAO;YACX,MAAM,SAAS,GAAG,CAAC;YACnB,OAAO,SAAS,GAAG,CAAC;YACpB,OAAO,SAAS,GAAG,CAAC;YACpB;YACA;YACA;YACA;QACF;QACA,SAAS;IACX;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAuC;;;;;;8BAGrD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAI;wCAAK;wCAAO;wCAAQ;;;;;;;8CACzB,8OAAC;8CAAK,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,gBAAgB;wCAAE,QAAQ,2IAAA,CAAA,KAAE;oCAAC;;;;;;8CAChD,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAI,WAAU;;wCAA2C;wCAC5C;wCAAa;;;;;;;;;;;;;;;;;;;8BAM/B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAW,CAAC,uDAAuD,EACjE,kBAAkB,OAAO,+BAA+B,mBACxD;;sDAEF,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAW,CAAC,uDAAuD,EACjE,kBAAkB,aAAa,+BAA+B,mBAC9D;;sDAEF,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;gBAO5C,kBAAkB,sBACjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;gBAMxC,kBAAkB,4BACjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDAAI,8OAAC;sDAAO;;;;;;wCAAoB;;;;;;;8CACjC,8OAAC;;sDAAI,8OAAC;sDAAO;;;;;;wCAAc;;;;;;;8CAC3B,8OAAC;;sDAAI,8OAAC;sDAAO;;;;;;wCAAc;;;;;;;8CAC3B,8OAAC;;sDAAI,8OAAC;sDAAO;;;;;;wCAAe;wCAAE;wCAAa;;;;;;;8CAC3C,8OAAC;;sDAAI,8OAAC;sDAAO;;;;;;wCAAoB;wCAAmB;wCAAO;wCAAQ;wCAAM;wCAAG,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;wCAAc;wCAAE;;;;;;;;;;;;;;;;;;;8BAM/G,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAuC;;;;;;0BAGrD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAK,WAAU;;oCAAgB;oCAAK;oCAAO;oCAAQ;;;;;;;;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;0CAAM,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,gBAAgB;oCAAE,QAAQ,2IAAA,CAAA,KAAE;gCAAC;;;;;;;;;;;;kCAEnD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;0CAAM;;;;;;;;;;;;;;;;;;0BAIX,8OAAC;gBAAK,UAAU,aAAa;gBAAmB,WAAU;;kCAExD,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCACE,GAAG,SAAS,OAAO;wCACpB,MAAK;wCACL,WAAU;wCACV,aAAY;;;;;;;;;;;;4BAGf,OAAO,IAAI,kBACV,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;kCAKjE,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCACE,GAAG,SAAS,QAAQ;wCACrB,MAAK;wCACL,WAAU;wCACV,aAAY;;;;;;;;;;;;4BAGf,OAAO,KAAK,kBACX,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAKlE,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACE,GAAG,SAAS,QAAQ;gCACrB,MAAK;gCACL,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,KAAK,kBACX,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAKlE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAK,WAAU;;wCAAmC;wCAAa;;;;;;;;;;;;;;;;;;kCAKpE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,eAAe,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 1581, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/MediaGallery.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ChevronLeft, ChevronRight, X, Play, Image as ImageIcon } from 'lucide-react';\n\ninterface MediaGalleryProps {\n  images: string[];\n  videos: string[];\n  title: string;\n}\n\nexport default function MediaGallery({ images, videos, title }: MediaGalleryProps) {\n  const [selectedImageIndex, setSelectedImageIndex] = useState(0);\n  const [isImageModalOpen, setIsImageModalOpen] = useState(false);\n  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);\n  const [selectedVideoIndex, setSelectedVideoIndex] = useState(0);\n  const [activeTab, setActiveTab] = useState<'images' | 'videos'>('images');\n\n  const nextImage = () => {\n    setSelectedImageIndex((prev) => (prev + 1) % images.length);\n  };\n\n  const prevImage = () => {\n    setSelectedImageIndex((prev) => (prev - 1 + images.length) % images.length);\n  };\n\n  const openImageModal = (index: number) => {\n    setSelectedImageIndex(index);\n    setIsImageModalOpen(true);\n  };\n\n  const openVideoModal = (index: number) => {\n    setSelectedVideoIndex(index);\n    setIsVideoModalOpen(true);\n  };\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg overflow-hidden\">\n      {/* Tabs */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"flex\">\n          <button\n            onClick={() => setActiveTab('images')}\n            className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${\n              activeTab === 'images'\n                ? 'border-blue-500 text-blue-600'\n                : 'border-transparent text-gray-500 hover:text-gray-700'\n            }`}\n          >\n            <ImageIcon className=\"w-4 h-4 inline mr-2\" />\n            Фотографии ({images.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('videos')}\n            className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${\n              activeTab === 'videos'\n                ? 'border-blue-500 text-blue-600'\n                : 'border-transparent text-gray-500 hover:text-gray-700'\n            }`}\n          >\n            <Play className=\"w-4 h-4 inline mr-2\" />\n            Видео ({videos.length})\n          </button>\n        </nav>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-6\">\n        {activeTab === 'images' && (\n          <div>\n            {/* Main Image */}\n            <div className=\"relative h-96 mb-4 rounded-lg overflow-hidden\">\n              <img\n                src={images[selectedImageIndex]}\n                alt={`${title} - фото ${selectedImageIndex + 1}`}\n                className=\"w-full h-full object-cover cursor-pointer\"\n                onClick={() => openImageModal(selectedImageIndex)}\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"></div>\n              \n              {images.length > 1 && (\n                <>\n                  <button\n                    onClick={prevImage}\n                    className=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 transition-colors\"\n                  >\n                    <ChevronLeft className=\"w-5 h-5\" />\n                  </button>\n                  <button\n                    onClick={nextImage}\n                    className=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 transition-colors\"\n                  >\n                    <ChevronRight className=\"w-5 h-5\" />\n                  </button>\n                </>\n              )}\n              \n              <div className=\"absolute bottom-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm\">\n                {selectedImageIndex + 1} / {images.length}\n              </div>\n            </div>\n\n            {/* Thumbnail Gallery */}\n            <div className=\"flex space-x-2 overflow-x-auto pb-2\">\n              {images.map((image, index) => (\n                <button\n                  key={index}\n                  onClick={() => setSelectedImageIndex(index)}\n                  className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors ${\n                    selectedImageIndex === index ? 'border-blue-500' : 'border-gray-200'\n                  }`}\n                >\n                  <img\n                    src={image}\n                    alt={`Миниатюра ${index + 1}`}\n                    className=\"w-full h-full object-cover\"\n                  />\n                </button>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'videos' && (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {videos.map((video, index) => (\n              <div\n                key={index}\n                className=\"relative bg-gray-200 rounded-lg overflow-hidden cursor-pointer group\"\n                onClick={() => openVideoModal(index)}\n              >\n                <div className=\"aspect-video flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <Play className=\"w-12 h-12 text-blue-600 group-hover:text-blue-700 transition-colors mx-auto mb-2\" />\n                    <p className=\"text-sm text-gray-600\">Видео {index + 1}</p>\n                  </div>\n                </div>\n                <div className=\"absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors\"></div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Image Modal */}\n      {isImageModalOpen && (\n        <div className=\"fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4\">\n          <div className=\"relative max-w-6xl max-h-full w-full\">\n            <button\n              onClick={() => setIsImageModalOpen(false)}\n              className=\"absolute top-4 right-4 text-white hover:text-gray-300 z-10 bg-black/50 rounded-full p-2\"\n            >\n              <X className=\"w-6 h-6\" />\n            </button>\n            \n            <div className=\"relative\">\n              <img\n                src={images[selectedImageIndex]}\n                alt={`${title} - фото ${selectedImageIndex + 1}`}\n                className=\"max-w-full max-h-[80vh] object-contain mx-auto\"\n              />\n              \n              {images.length > 1 && (\n                <>\n                  <button\n                    onClick={prevImage}\n                    className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 bg-black/50 rounded-full p-2\"\n                  >\n                    <ChevronLeft className=\"w-8 h-8\" />\n                  </button>\n                  <button\n                    onClick={nextImage}\n                    className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 bg-black/50 rounded-full p-2\"\n                  >\n                    <ChevronRight className=\"w-8 h-8\" />\n                  </button>\n                </>\n              )}\n            </div>\n            \n            <div className=\"text-center mt-4\">\n              <p className=\"text-white text-lg\">{title}</p>\n              <p className=\"text-gray-300\">Фото {selectedImageIndex + 1} из {images.length}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Video Modal */}\n      {isVideoModalOpen && (\n        <div className=\"fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4\">\n          <div className=\"relative max-w-4xl max-h-full w-full\">\n            <button\n              onClick={() => setIsVideoModalOpen(false)}\n              className=\"absolute top-4 right-4 text-white hover:text-gray-300 z-10 bg-black/50 rounded-full p-2\"\n            >\n              <X className=\"w-6 h-6\" />\n            </button>\n            \n            <div className=\"bg-gray-800 rounded-lg p-8 text-center\">\n              <Play className=\"w-16 h-16 text-white mx-auto mb-4\" />\n              <h3 className=\"text-white text-xl mb-2\">Видео плеер</h3>\n              <p className=\"text-gray-400 mb-4\">\n                Здесь будет воспроизводиться видео: {videos[selectedVideoIndex]}\n              </p>\n              <div className=\"bg-gray-700 rounded-lg p-4 aspect-video flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <Play className=\"w-12 h-12 text-gray-400 mx-auto mb-2\" />\n                  <p className=\"text-gray-400\">Видео {selectedVideoIndex + 1}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAWe,SAAS,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAqB;IAC/E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAEhE,MAAM,YAAY;QAChB,sBAAsB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;IAC5D;IAEA,MAAM,YAAY;QAChB,sBAAsB,CAAC,OAAS,CAAC,OAAO,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM;IAC5E;IAEA,MAAM,iBAAiB,CAAC;QACtB,sBAAsB;QACtB,oBAAoB;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,sBAAsB;QACtB,oBAAoB;IACtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,2DAA2D,EACrE,cAAc,WACV,kCACA,wDACJ;;8CAEF,8OAAC,oMAAA,CAAA,QAAS;oCAAC,WAAU;;;;;;gCAAwB;gCAChC,OAAO,MAAM;gCAAC;;;;;;;sCAE7B,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,2DAA2D,EACrE,cAAc,WACV,kCACA,wDACJ;;8CAEF,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAwB;gCAChC,OAAO,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAM5B,8OAAC;gBAAI,WAAU;;oBACZ,cAAc,0BACb,8OAAC;;0CAEC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,KAAK,MAAM,CAAC,mBAAmB;wCAC/B,KAAK,GAAG,MAAM,QAAQ,EAAE,qBAAqB,GAAG;wCAChD,WAAU;wCACV,SAAS,IAAM,eAAe;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;;;;;;oCAEd,OAAO,MAAM,GAAG,mBACf;;0DACE,8OAAC;gDACC,SAAS;gDACT,WAAU;0DAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;0DAEzB,8OAAC;gDACC,SAAS;gDACT,WAAU;0DAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;kDAK9B,8OAAC;wCAAI,WAAU;;4CACZ,qBAAqB;4CAAE;4CAAI,OAAO,MAAM;;;;;;;;;;;;;0CAK7C,8OAAC;gCAAI,WAAU;0CACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;wCAEC,SAAS,IAAM,sBAAsB;wCACrC,WAAW,CAAC,8EAA8E,EACxF,uBAAuB,QAAQ,oBAAoB,mBACnD;kDAEF,cAAA,8OAAC;4CACC,KAAK;4CACL,KAAK,CAAC,UAAU,EAAE,QAAQ,GAAG;4CAC7B,WAAU;;;;;;uCATP;;;;;;;;;;;;;;;;oBAiBd,cAAc,0BACb,8OAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;gCAEC,WAAU;gCACV,SAAS,IAAM,eAAe;;kDAE9B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAE,WAAU;;wDAAwB;wDAAO,QAAQ;;;;;;;;;;;;;;;;;;kDAGxD,8OAAC;wCAAI,WAAU;;;;;;;+BAVV;;;;;;;;;;;;;;;;YAkBd,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,oBAAoB;4BACnC,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAGf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,KAAK,MAAM,CAAC,mBAAmB;oCAC/B,KAAK,GAAG,MAAM,QAAQ,EAAE,qBAAqB,GAAG;oCAChD,WAAU;;;;;;gCAGX,OAAO,MAAM,GAAG,mBACf;;sDACE,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAEzB,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;sCAMhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,8OAAC;oCAAE,WAAU;;wCAAgB;wCAAM,qBAAqB;wCAAE;wCAAK,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;YAOnF,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,oBAAoB;4BACnC,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAGf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;;wCAAqB;wCACK,MAAM,CAAC,mBAAmB;;;;;;;8CAEjE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAE,WAAU;;oDAAgB;oDAAO,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3E", "debugId": null}}, {"offset": {"line": 2071, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/app/halls/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useParams, useRouter } from 'next/navigation';\nimport Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport BookingCalendar from '@/components/BookingCalendar';\nimport BookingForm from '@/components/BookingForm';\nimport MediaGallery from '@/components/MediaGallery';\nimport {\n  ArrowLeft,\n  Users,\n  MapPin,\n  Clock,\n  Star,\n  Calendar\n} from 'lucide-react';\n\ninterface HallData {\n  id: number;\n  name: string;\n  courts: number;\n  description: string;\n  detailedDescription: string;\n  features: string[];\n  pricePerHour: number;\n  images: string[];\n  videos: string[];\n  specifications: {\n    area: string;\n    height: string;\n    flooring: string;\n    lighting: string;\n    ventilation: string;\n  };\n  amenities: string[];\n  workingHours: {\n    weekdays: string;\n    weekends: string;\n  };\n}\n\nconst hallsData: Record<number, HallData> = {\n  1: {\n    id: 1,\n    name: 'Зал 1',\n    courts: 3,\n    description: 'Уютный зал с профессиональными кортами для игры в бадминтон',\n    detailedDescription: 'Зал 1 - это идеальное место для начинающих игроков и любителей бадминтона. Компактный и уютный зал оборудован тремя профессиональными кортами с качественным покрытием. Отличное освещение и система кондиционирования обеспечивают комфортные условия для игры в любое время года.',\n    features: [\n      'Профессиональное покрытие',\n      'Отличное освещение',\n      'Кондиционирование воздуха',\n      'Раздевалки с душем'\n    ],\n    pricePerHour: 150,\n    images: [\n      '/api/placeholder/800/600?text=Зал+1+Общий+вид',\n      '/api/placeholder/800/600?text=Зал+1+Корт+1',\n      '/api/placeholder/800/600?text=Зал+1+Корт+2',\n      '/api/placeholder/800/600?text=Зал+1+Раздевалка',\n      '/api/placeholder/800/600?text=Зал+1+Освещение'\n    ],\n    videos: [\n      '/api/placeholder/video?text=Обзор+Зала+1',\n      '/api/placeholder/video?text=Игра+в+Зале+1'\n    ],\n    specifications: {\n      area: '400 м²',\n      height: '9 м',\n      flooring: 'Профессиональное покрытие Taraflex',\n      lighting: 'LED освещение 1000 лк',\n      ventilation: 'Принудительная вентиляция'\n    },\n    amenities: [\n      'Раздевалки с индивидуальными шкафчиками',\n      'Душевые кабины',\n      'Зона отдыха',\n      'Питьевые фонтанчики',\n      'Wi-Fi',\n      'Парковка'\n    ],\n    workingHours: {\n      weekdays: '06:00 - 23:00',\n      weekends: '08:00 - 22:00'\n    }\n  },\n  2: {\n    id: 2,\n    name: 'Зал 2',\n    courts: 7,\n    description: 'Большой зал с семью кортами для турниров и тренировок',\n    detailedDescription: 'Зал 2 - наш главный турнирный зал, оборудованный семью профессиональными кортами. Здесь проводятся официальные соревнования и тренировки спортсменов высокого уровня. Зал оснащен трибунами для зрителей и современной звуковой системой.',\n    features: [\n      'Турнирные корты',\n      'Трибуны для зрителей',\n      'Профессиональная разметка',\n      'Система вентиляции',\n      'Звуковая система'\n    ],\n    pricePerHour: 180,\n    images: [\n      '/api/placeholder/800/600?text=Зал+2+Турнирный+вид',\n      '/api/placeholder/800/600?text=Зал+2+Трибуны',\n      '/api/placeholder/800/600?text=Зал+2+Корты',\n      '/api/placeholder/800/600?text=Зал+2+Звуковая+система',\n      '/api/placeholder/800/600?text=Зал+2+Освещение'\n    ],\n    videos: [\n      '/api/placeholder/video?text=Турнир+в+Зале+2',\n      '/api/placeholder/video?text=Тренировка+в+Зале+2'\n    ],\n    specifications: {\n      area: '1200 м²',\n      height: '12 м',\n      flooring: 'Турнирное покрытие BWF стандарт',\n      lighting: 'Профессиональное LED 1500 лк',\n      ventilation: 'Климат-контроль с рекуперацией'\n    },\n    amenities: [\n      'VIP раздевалки',\n      'Душевые с подогревом пола',\n      'Зона для разминки',\n      'Кафе',\n      'Трибуны на 200 мест',\n      'Профессиональная звуковая система',\n      'Табло',\n      'Wi-Fi',\n      'Охраняемая парковка'\n    ],\n    workingHours: {\n      weekdays: '06:00 - 23:00',\n      weekends: '08:00 - 22:00'\n    }\n  },\n  3: {\n    id: 3,\n    name: 'Зал 3',\n    courts: 7,\n    description: 'Современный зал с новейшим оборудованием',\n    detailedDescription: 'Зал 3 - самый современный зал клуба с новейшими технологиями. Семь кортов оборудованы инновационным LED освещением и системой климат-контроля. VIP раздевалки и зона отдыха создают атмосферу премиум-класса.',\n    features: [\n      'Новейшее покрытие',\n      'LED освещение',\n      'Климат-контроль',\n      'VIP раздевалки',\n      'Зона отдыха'\n    ],\n    pricePerHour: 200,\n    images: [\n      '/api/placeholder/800/600?text=Зал+3+Премиум+вид',\n      '/api/placeholder/800/600?text=Зал+3+LED+освещение',\n      '/api/placeholder/800/600?text=Зал+3+VIP+раздевалка',\n      '/api/placeholder/800/600?text=Зал+3+Зона+отдыха',\n      '/api/placeholder/800/600?text=Зал+3+Климат+контроль'\n    ],\n    videos: [\n      '/api/placeholder/video?text=Обзор+Зала+3+Premium',\n      '/api/placeholder/video?text=Технологии+Зала+3'\n    ],\n    specifications: {\n      area: '1100 м²',\n      height: '11 м',\n      flooring: 'Инновационное покрытие Gerflor',\n      lighting: 'Умное LED освещение 1200 лк',\n      ventilation: 'Интеллектуальный климат-контроль'\n    },\n    amenities: [\n      'VIP раздевалки с сауной',\n      'Премиум душевые',\n      'Лаунж-зона',\n      'Мини-бар',\n      'Массажные кресла',\n      'Система очистки воздуха',\n      'Умное освещение',\n      'Premium Wi-Fi',\n      'Valet парковка'\n    ],\n    workingHours: {\n      weekdays: '06:00 - 23:00',\n      weekends: '08:00 - 22:00'\n    }\n  }\n};\n\nexport default function HallPage() {\n  const params = useParams();\n  const router = useRouter();\n  const hallId = parseInt(params.id as string);\n  const hall = hallsData[hallId];\n\n  const [showBooking, setShowBooking] = useState(false);\n  const [selectedSlot, setSelectedSlot] = useState<{\n    date: Date;\n    time: string;\n    court: number;\n  } | null>(null);\n\n  if (!hall) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Зал не найден</h1>\n          <button\n            onClick={() => router.push('/')}\n            className=\"text-blue-600 hover:text-blue-700\"\n          >\n            Вернуться на главную\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const handleSlotSelect = (date: Date, time: string, court: number) => {\n    setSelectedSlot({ date, time, court });\n  };\n\n  const handleBookingSubmit = (data: any) => {\n    console.log('Booking submitted:', data);\n    setSelectedSlot(null);\n    setShowBooking(false);\n    // Здесь будет отправка данных на сервер\n  };\n\n  const handleBookingCancel = () => {\n    setSelectedSlot(null);\n    setShowBooking(false);\n  };\n\n\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      {/* Hero Section */}\n      <section className=\"relative h-96 bg-gradient-to-br from-blue-600 to-blue-800\">\n        <div className=\"absolute inset-0 bg-black/20\"></div>\n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center\">\n          <div className=\"text-white\">\n            <button\n              onClick={() => router.push('/')}\n              className=\"flex items-center text-blue-200 hover:text-white mb-4 transition-colors\"\n            >\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\n              Назад к залам\n            </button>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-4\">{hall.name}</h1>\n            <p className=\"text-xl md:text-2xl text-blue-100 mb-6\">\n              {hall.description}\n            </p>\n            <div className=\"flex items-center space-x-6 text-blue-100\">\n              <div className=\"flex items-center\">\n                <Users className=\"w-5 h-5 mr-2\" />\n                {hall.courts} кортов\n              </div>\n              <div className=\"flex items-center\">\n                <MapPin className=\"w-5 h-5 mr-2\" />\n                Кишинев\n              </div>\n              <div className=\"text-2xl font-bold text-white\">\n                {hall.pricePerHour} лей/час\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Main Content */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Left Column - Images and Info */}\n          <div className=\"lg:col-span-2\">\n            {/* Media Gallery */}\n            <div className=\"mb-8\">\n              <MediaGallery\n                images={hall.images}\n                videos={hall.videos}\n                title={hall.name}\n              />\n            </div>\n\n            {/* Detailed Description */}\n            <div className=\"bg-white rounded-xl shadow-lg p-6 mb-8\">\n              <h3 className=\"text-xl font-bold text-gray-900 mb-4\">О зале</h3>\n              <p className=\"text-gray-600 leading-relaxed mb-6\">\n                {hall.detailedDescription}\n              </p>\n\n              {/* Features */}\n              <div className=\"mb-6\">\n                <h4 className=\"font-semibold text-gray-900 mb-3\">Особенности:</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2\">\n                  {hall.features.map((feature, index) => (\n                    <div key={index} className=\"flex items-center\">\n                      <Star className=\"w-4 h-4 text-blue-500 mr-2\" />\n                      <span className=\"text-gray-600\">{feature}</span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Specifications */}\n              <div>\n                <h4 className=\"font-semibold text-gray-900 mb-3\">Технические характеристики:</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <span className=\"text-sm text-gray-500\">Площадь:</span>\n                    <div className=\"font-medium\">{hall.specifications.area}</div>\n                  </div>\n                  <div>\n                    <span className=\"text-sm text-gray-500\">Высота потолков:</span>\n                    <div className=\"font-medium\">{hall.specifications.height}</div>\n                  </div>\n                  <div>\n                    <span className=\"text-sm text-gray-500\">Покрытие:</span>\n                    <div className=\"font-medium\">{hall.specifications.flooring}</div>\n                  </div>\n                  <div>\n                    <span className=\"text-sm text-gray-500\">Освещение:</span>\n                    <div className=\"font-medium\">{hall.specifications.lighting}</div>\n                  </div>\n                  <div className=\"md:col-span-2\">\n                    <span className=\"text-sm text-gray-500\">Вентиляция:</span>\n                    <div className=\"font-medium\">{hall.specifications.ventilation}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Column - Booking and Info */}\n          <div className=\"lg:col-span-1\">\n            {/* Booking Section */}\n            {!showBooking && !selectedSlot && (\n              <div className=\"bg-white rounded-xl shadow-lg p-6 mb-6 sticky top-6\">\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Бронирование</h3>\n                <div className=\"text-center mb-4\">\n                  <div className=\"text-3xl font-bold text-blue-600 mb-2\">\n                    {hall.pricePerHour} лей\n                  </div>\n                  <div className=\"text-gray-600\">за час</div>\n                </div>\n                <button\n                  onClick={() => setShowBooking(true)}\n                  className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center\"\n                >\n                  <Calendar className=\"w-4 h-4 mr-2\" />\n                  Забронировать\n                </button>\n              </div>\n            )}\n\n            {/* Booking Calendar */}\n            {showBooking && !selectedSlot && (\n              <div className=\"mb-6\">\n                <BookingCalendar\n                  hallId={hall.id}\n                  onSlotSelect={handleSlotSelect}\n                />\n                <div className=\"mt-4\">\n                  <button\n                    onClick={() => setShowBooking(false)}\n                    className=\"w-full text-gray-600 hover:text-gray-800 py-2\"\n                  >\n                    Отмена\n                  </button>\n                </div>\n              </div>\n            )}\n\n            {/* Booking Form */}\n            {selectedSlot && (\n              <div className=\"mb-6\">\n                <BookingForm\n                  hallId={hall.id}\n                  date={selectedSlot.date}\n                  time={selectedSlot.time}\n                  court={selectedSlot.court}\n                  onSubmit={handleBookingSubmit}\n                  onCancel={handleBookingCancel}\n                />\n              </div>\n            )}\n\n            {/* Working Hours */}\n            <div className=\"bg-white rounded-xl shadow-lg p-6 mb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Режим работы</h3>\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center\">\n                  <Clock className=\"w-4 h-4 text-gray-400 mr-2\" />\n                  <div>\n                    <div className=\"font-medium\">Пн-Пт:</div>\n                    <div className=\"text-gray-600\">{hall.workingHours.weekdays}</div>\n                  </div>\n                </div>\n                <div className=\"flex items-center\">\n                  <Clock className=\"w-4 h-4 text-gray-400 mr-2\" />\n                  <div>\n                    <div className=\"font-medium\">Сб-Вс:</div>\n                    <div className=\"text-gray-600\">{hall.workingHours.weekends}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Amenities */}\n            <div className=\"bg-white rounded-xl shadow-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Удобства</h3>\n              <div className=\"space-y-2\">\n                {hall.amenities.map((amenity, index) => (\n                  <div key={index} className=\"flex items-start\">\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0\"></div>\n                    <span className=\"text-gray-600\">{amenity}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AA0CA,MAAM,YAAsC;IAC1C,GAAG;QACD,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,qBAAqB;QACrB,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,cAAc;QACd,QAAQ;YACN;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;YACN;YACA;SACD;QACD,gBAAgB;YACd,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;YACV,aAAa;QACf;QACA,WAAW;YACT;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ,UAAU;YACV,UAAU;QACZ;IACF;IACA,GAAG;QACD,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,qBAAqB;QACrB,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;QACd,QAAQ;YACN;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;YACN;YACA;SACD;QACD,gBAAgB;YACd,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;YACV,aAAa;QACf;QACA,WAAW;YACT;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ,UAAU;YACV,UAAU;QACZ;IACF;IACA,GAAG;QACD,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,qBAAqB;QACrB,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;QACd,QAAQ;YACN;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;YACN;YACA;SACD;QACD,gBAAgB;YACd,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;YACV,aAAa;QACf;QACA,WAAW;YACT;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ,UAAU;YACV,UAAU;QACZ;IACF;AACF;AAEe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,SAAS,OAAO,EAAE;IACjC,MAAM,OAAO,SAAS,CAAC,OAAO;IAE9B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIrC;IAEV,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,MAAM,mBAAmB,CAAC,MAAY,MAAc;QAClD,gBAAgB;YAAE;YAAM;YAAM;QAAM;IACtC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,sBAAsB;QAClC,gBAAgB;QAChB,eAAe;IACf,wCAAwC;IAC1C;IAEA,MAAM,sBAAsB;QAC1B,gBAAgB;QAChB,eAAe;IACjB;IAIA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAGP,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGxC,8OAAC;oCAAG,WAAU;8CAAuC,KAAK,IAAI;;;;;;8CAC9D,8OAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;8CAEnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAChB,KAAK,MAAM;gDAAC;;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGrC,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,YAAY;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7B,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,UAAY;wCACX,QAAQ,KAAK,MAAM;wCACnB,QAAQ,KAAK,MAAM;wCACnB,OAAO,KAAK,IAAI;;;;;;;;;;;8CAKpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAE,WAAU;sDACV,KAAK,mBAAmB;;;;;;sDAI3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,8OAAC;4DAAgB,WAAU;;8EACzB,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;oEAAK,WAAU;8EAAiB;;;;;;;2DAFzB;;;;;;;;;;;;;;;;sDAShB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAI,WAAU;8EAAe,KAAK,cAAc,CAAC,IAAI;;;;;;;;;;;;sEAExD,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAI,WAAU;8EAAe,KAAK,cAAc,CAAC,MAAM;;;;;;;;;;;;sEAE1D,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAI,WAAU;8EAAe,KAAK,cAAc,CAAC,QAAQ;;;;;;;;;;;;sEAE5D,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAI,WAAU;8EAAe,KAAK,cAAc,CAAC,QAAQ;;;;;;;;;;;;sEAE5D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAI,WAAU;8EAAe,KAAK,cAAc,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQvE,8OAAC;4BAAI,WAAU;;gCAEZ,CAAC,eAAe,CAAC,8BAChB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,YAAY;wDAAC;;;;;;;8DAErB,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;sDAEjC,8OAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;gCAO1C,eAAe,CAAC,8BACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,qIAAA,CAAA,UAAe;4CACd,QAAQ,KAAK,EAAE;4CACf,cAAc;;;;;;sDAEhB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;0DACX;;;;;;;;;;;;;;;;;gCAQN,8BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,UAAW;wCACV,QAAQ,KAAK,EAAE;wCACf,MAAM,aAAa,IAAI;wCACvB,MAAM,aAAa,IAAI;wCACvB,OAAO,aAAa,KAAK;wCACzB,UAAU;wCACV,UAAU;;;;;;;;;;;8CAMhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAc;;;;;;8EAC7B,8OAAC;oEAAI,WAAU;8EAAiB,KAAK,YAAY,CAAC,QAAQ;;;;;;;;;;;;;;;;;;8DAG9D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAc;;;;;;8EAC7B,8OAAC;oEAAI,WAAU;8EAAiB,KAAK,YAAY,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOlE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;sDACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC5B,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAiB;;;;;;;mDAFzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAatB,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}