{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { Menu, X, Phone, MapPin } from 'lucide-react';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleNavigation = (href: string) => {\n    if (href.startsWith('#')) {\n      // Якорная ссылка - переходим на главную страницу если не на ней\n      if (pathname !== '/') {\n        router.push('/' + href);\n      } else {\n        // Если уже на главной странице, просто скроллим\n        const element = document.querySelector(href);\n        if (element) {\n          element.scrollIntoView({ behavior: 'smooth' });\n        }\n      }\n    } else {\n      // Обычная ссылка\n      router.push(href);\n    }\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <div className=\"text-2xl font-bold text-blue-600\">\n              🏸 BadmintonClub\n            </div>\n            <div className=\"ml-2 text-sm text-gray-600 hidden sm:block\">\n              Кишинев\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <button\n              onClick={() => handleNavigation('/')}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Главная\n            </button>\n            <button\n              onClick={() => handleNavigation('/about')}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              О нас\n            </button>\n            <button\n              onClick={() => handleNavigation('#halls')}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Залы\n            </button>\n            <button\n              onClick={() => handleNavigation('/services')}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Услуги\n            </button>\n            <button\n              onClick={() => handleNavigation('/contact')}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Контакты\n            </button>\n            <Link href=\"/admin\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n              Админ\n            </Link>\n          </nav>\n\n          {/* Contact Info */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Phone className=\"w-4 h-4 mr-1\" />\n              +373 XX XXX XXX\n            </div>\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <MapPin className=\"w-4 h-4 mr-1\" />\n              Кишинев\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {isMenuOpen ? (\n              <X className=\"w-6 h-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"w-6 h-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <button\n                onClick={() => handleNavigation('/')}\n                className=\"text-gray-700 hover:text-blue-600 transition-colors text-left\"\n              >\n                Главная\n              </button>\n              <button\n                onClick={() => handleNavigation('/about')}\n                className=\"text-gray-700 hover:text-blue-600 transition-colors text-left\"\n              >\n                О нас\n              </button>\n              <button\n                onClick={() => handleNavigation('#halls')}\n                className=\"text-gray-700 hover:text-blue-600 transition-colors text-left\"\n              >\n                Залы\n              </button>\n              <button\n                onClick={() => handleNavigation('/services')}\n                className=\"text-gray-700 hover:text-blue-600 transition-colors text-left\"\n              >\n                Услуги\n              </button>\n              <button\n                onClick={() => handleNavigation('/contact')}\n                className=\"text-gray-700 hover:text-blue-600 transition-colors text-left\"\n              >\n                Контакты\n              </button>\n              <Link href=\"/admin\" className=\"text-gray-700 hover:text-blue-600 transition-colors\">\n                Админ\n              </Link>\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center text-sm text-gray-600 mb-2\">\n                  <Phone className=\"w-4 h-4 mr-1\" />\n                  +373 XX XXX XXX\n                </div>\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <MapPin className=\"w-4 h-4 mr-1\" />\n                  Кишинев\n                </div>\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,mBAAmB,CAAC;QACxB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,gEAAgE;YAChE,IAAI,aAAa,KAAK;gBACpB,OAAO,IAAI,CAAC,MAAM;YACpB,OAAO;gBACL,gDAAgD;gBAChD,MAAM,UAAU,SAAS,aAAa,CAAC;gBACvC,IAAI,SAAS;oBACX,QAAQ,cAAc,CAAC;wBAAE,UAAU;oBAAS;gBAC9C;YACF;QACF,OAAO;YACL,iBAAiB;YACjB,OAAO,IAAI,CAAC;QACd;QACA,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CAAmC;;;;;;8CAGlD,8OAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAsD;;;;;;;;;;;;sCAMtF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE7B,2BACC,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAAsD;;;;;;0CAGpF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Footer.tsx"], "sourcesContent": ["import { Phone, MapPin, Clock, Mail } from 'lucide-react';\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {/* Company Info */}\n          <div>\n            <div className=\"text-2xl font-bold text-blue-400 mb-4\">\n              🏸 BadmintonClub\n            </div>\n            <p className=\"text-gray-300 mb-4\">\n              Современный бадминтонный клуб в Кишиневе с профессиональными кортами \n              и удобной системой бронирования.\n            </p>\n            <div className=\"flex items-center text-gray-300 mb-2\">\n              <MapPin className=\"w-4 h-4 mr-2\" />\n              Кишинев, Молдова\n            </div>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Контакты</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center text-gray-300\">\n                <Phone className=\"w-4 h-4 mr-2\" />\n                +373 XX XXX XXX\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <Mail className=\"w-4 h-4 mr-2\" />\n                <EMAIL>\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <MapPin className=\"w-4 h-4 mr-2\" />\n                ул. Примерная, 123, Кишинев\n              </div>\n            </div>\n          </div>\n\n          {/* Working Hours */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Режим работы</h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-gray-300\">\n                <Clock className=\"w-4 h-4 mr-2\" />\n                <div>\n                  <div>Пн-Пт: 06:00 - 23:00</div>\n                  <div>Сб-Вс: 08:00 - 22:00</div>\n                </div>\n              </div>\n            </div>\n            \n            {/* Halls Info */}\n            <div className=\"mt-6\">\n              <h4 className=\"font-semibold mb-2\">Наши залы:</h4>\n              <div className=\"text-sm text-gray-300 space-y-1\">\n                <div>Зал 1: 3 корта</div>\n                <div>Зал 2: 7 кортов</div>\n                <div>Зал 3: 7 кортов</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n          <p>&copy; 2024 BadmintonClub Кишинев. Все права защищены.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAwC;;;;;;8CAGvD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOzC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;kEAAI;;;;;;kEACL,8OAAC;kEAAI;;;;;;;;;;;;;;;;;;;;;;;8CAMX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/app/about/page.tsx"], "sourcesContent": ["'use client';\n\nimport Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport { Users, Award, Clock, Target, Heart, Star } from 'lucide-react';\n\nexport default function AboutPage() {\n  const teamMembers = [\n    {\n      name: 'Александр Петров',\n      position: 'Директор клуба',\n      experience: '15 лет в бадминтоне',\n      description: 'Мастер спорта по бадминтону, основатель клуба',\n      image: '/api/placeholder/300/300?text=Александр+Петров'\n    },\n    {\n      name: 'Мария Иванова',\n      position: 'Главный тренер',\n      experience: '12 лет тренерского стажа',\n      description: 'Кандидат в мастера спорта, специалист по работе с детьми',\n      image: '/api/placeholder/300/300?text=Мария+Иванова'\n    },\n    {\n      name: 'Д<PERSON><PERSON><PERSON><PERSON><PERSON>идоров',\n      position: 'Тренер',\n      experience: '8 лет в спорте',\n      description: 'Специалист по технической подготовке игроков',\n      image: '/api/placeholder/300/300?text=Дмитрий+Сидоров'\n    }\n  ];\n\n  const achievements = [\n    {\n      icon: Award,\n      title: '500+ довольных клиентов',\n      description: 'За 5 лет работы мы обслужили более 500 постоянных клиентов'\n    },\n    {\n      icon: Users,\n      title: '17 профессиональных кортов',\n      description: '3 современных зала с кортами международного стандарта'\n    },\n    {\n      icon: Clock,\n      title: '16 часов работы в день',\n      description: 'Работаем с 6:00 до 23:00 для вашего удобства'\n    },\n    {\n      icon: Target,\n      title: '50+ турниров проведено',\n      description: 'Организуем турниры различного уровня для всех возрастов'\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-600 to-blue-800 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              О нашем клубе\n            </h1>\n            <p className=\"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto\">\n              BadmintonClub Кишинев - ведущий бадминтонный клуб Молдовы, \n              где профессионализм встречается с комфортом\n            </p>\n          </div>\n        </div>\n      </section>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        {/* Our Story */}\n        <section className=\"mb-16\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            <div>\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-6\">Наша история</h2>\n              <div className=\"space-y-4 text-gray-600\">\n                <p>\n                  BadmintonClub Кишинев был основан в 2019 году группой энтузиастов бадминтона, \n                  которые мечтали создать современный спортивный центр европейского уровня в столице Молдовы.\n                </p>\n                <p>\n                  За пять лет работы мы выросли от небольшого зала с тремя кортами до крупнейшего \n                  бадминтонного комплекса в стране с 17 профессиональными кортами в трех залах.\n                </p>\n                <p>\n                  Сегодня наш клуб является домашней ареной для сборной команды Молдовы по бадминтону \n                  и местом проведения международных турниров.\n                </p>\n              </div>\n            </div>\n            <div className=\"relative\">\n              <div className=\"bg-gradient-to-br from-blue-500 to-blue-700 rounded-xl p-8 text-white\">\n                <div className=\"text-center\">\n                  <div className=\"text-5xl mb-4\">🏸</div>\n                  <h3 className=\"text-2xl font-bold mb-4\">5 лет успеха</h3>\n                  <p className=\"text-blue-100\">\n                    Мы гордимся тем, что стали частью спортивной жизни Кишинева \n                    и помогли сотням людей полюбить бадминтон\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Mission & Values */}\n        <section className=\"mb-16\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Наша миссия и ценности</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Мы стремимся популяризировать бадминтон в Молдове и создавать условия \n              для развития спорта на всех уровнях\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"bg-white rounded-xl shadow-lg p-6 text-center\">\n              <Heart className=\"w-12 h-12 text-blue-600 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-semibold mb-3\">Любовь к спорту</h3>\n              <p className=\"text-gray-600\">\n                Мы искренне любим бадминтон и хотим поделиться этой страстью с каждым посетителем\n              </p>\n            </div>\n            \n            <div className=\"bg-white rounded-xl shadow-lg p-6 text-center\">\n              <Star className=\"w-12 h-12 text-blue-600 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-semibold mb-3\">Качество</h3>\n              <p className=\"text-gray-600\">\n                Только лучшее оборудование, покрытия и условия для комфортной игры\n              </p>\n            </div>\n            \n            <div className=\"bg-white rounded-xl shadow-lg p-6 text-center\">\n              <Users className=\"w-12 h-12 text-blue-600 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-semibold mb-3\">Сообщество</h3>\n              <p className=\"text-gray-600\">\n                Мы создаем дружественную атмосферу, где каждый чувствует себя частью большой семьи\n              </p>\n            </div>\n          </div>\n        </section>\n\n        {/* Achievements */}\n        <section className=\"mb-16\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Наши достижения</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              За годы работы мы достигли значительных результатов и продолжаем развиваться\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {achievements.map((achievement, index) => (\n              <div key={index} className=\"bg-white rounded-xl shadow-lg p-6 text-center\">\n                <achievement.icon className=\"w-10 h-10 text-blue-600 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold mb-2\">{achievement.title}</h3>\n                <p className=\"text-gray-600 text-sm\">{achievement.description}</p>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* Team */}\n        <section className=\"mb-16\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Наша команда</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Профессиональные тренеры и администраторы, которые сделают ваше пребывание \n              в клубе максимально комфортным\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {teamMembers.map((member, index) => (\n              <div key={index} className=\"bg-white rounded-xl shadow-lg overflow-hidden\">\n                <div className=\"aspect-square bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center\">\n                  <div className=\"text-center text-white\">\n                    <div className=\"text-6xl mb-4\">👤</div>\n                    <div className=\"text-xl font-semibold\">{member.name}</div>\n                  </div>\n                </div>\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-semibold mb-2\">{member.name}</h3>\n                  <p className=\"text-blue-600 font-medium mb-2\">{member.position}</p>\n                  <p className=\"text-gray-600 text-sm mb-3\">{member.experience}</p>\n                  <p className=\"text-gray-600 text-sm\">{member.description}</p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* Call to Action */}\n        <section className=\"bg-blue-600 rounded-xl text-white p-8 text-center\">\n          <h2 className=\"text-3xl font-bold mb-4\">Присоединяйтесь к нам!</h2>\n          <p className=\"text-blue-100 mb-6 max-w-2xl mx-auto\">\n            Станьте частью нашего спортивного сообщества. Забронируйте корт \n            и почувствуйте разницу игры в профессиональных условиях.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button \n              onClick={() => window.location.href = '/#halls'}\n              className=\"bg-white text-blue-600 font-semibold py-3 px-8 rounded-lg hover:bg-blue-50 transition-colors\"\n            >\n              Забронировать корт\n            </button>\n            <button \n              onClick={() => window.location.href = '/contact'}\n              className=\"border-2 border-white text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:text-blue-600 transition-colors\"\n            >\n              Связаться с нами\n            </button>\n          </div>\n        </section>\n      </div>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,cAAc;QAClB;YACE,MAAM;YACN,UAAU;YACV,YAAY;YACZ,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,UAAU;YACV,YAAY;YACZ,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,UAAU;YACV,YAAY;YACZ,aAAa;YACb,OAAO;QACT;KACD;IAED,MAAM,eAAe;QACnB;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAGP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAAsD;;;;;;;;;;;;;;;;;;;;;;0BAQzE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAE;;;;;;8DAIH,8OAAC;8DAAE;;;;;;8DAIH,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;8CAMP,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWvC,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;0CAMjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAK/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAK/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;kCAQnC,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;0CAKjD,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC,YAAY,IAAI;gDAAC,WAAU;;;;;;0DAC5B,8OAAC;gDAAG,WAAU;0DAA8B,YAAY,KAAK;;;;;;0DAC7D,8OAAC;gDAAE,WAAU;0DAAyB,YAAY,WAAW;;;;;;;uCAHrD;;;;;;;;;;;;;;;;kCAUhB,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;0CAMjD,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,8OAAC;4DAAI,WAAU;sEAAyB,OAAO,IAAI;;;;;;;;;;;;;;;;;0DAGvD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA8B,OAAO,IAAI;;;;;;kEACvD,8OAAC;wDAAE,WAAU;kEAAkC,OAAO,QAAQ;;;;;;kEAC9D,8OAAC;wDAAE,WAAU;kEAA8B,OAAO,UAAU;;;;;;kEAC5D,8OAAC;wDAAE,WAAU;kEAAyB,OAAO,WAAW;;;;;;;;;;;;;uCAXlD;;;;;;;;;;;;;;;;kCAmBhB,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wCACtC,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wCACtC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;0BAOP,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}