{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { Menu, X, Phone, MapPin } from 'lucide-react';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleNavigation = (href: string) => {\n    if (href.startsWith('#')) {\n      // Якорная ссылка - переходим на главную страницу если не на ней\n      if (pathname !== '/') {\n        router.push('/' + href);\n      } else {\n        // Если уже на главной странице, просто скроллим\n        const element = document.querySelector(href);\n        if (element) {\n          element.scrollIntoView({ behavior: 'smooth' });\n        }\n      }\n    } else {\n      // Обычная ссылка\n      router.push(href);\n    }\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <div className=\"text-2xl font-bold text-altius-blue\">\n              🏸 Altius\n            </div>\n            <div className=\"ml-2 text-sm text-gray-600 hidden sm:block\">\n              Кишинев\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <button\n              onClick={() => handleNavigation('/')}\n              className=\"text-gray-700 hover:text-altius-blue transition-colors\"\n            >\n              Главная\n            </button>\n            <button\n              onClick={() => handleNavigation('/about')}\n              className=\"text-gray-700 hover:text-altius-lime transition-colors\"\n            >\n              О нас\n            </button>\n            <button\n              onClick={() => handleNavigation('#halls')}\n              className=\"text-gray-700 hover:text-altius-orange transition-colors\"\n            >\n              Залы\n            </button>\n            <button\n              onClick={() => handleNavigation('/services')}\n              className=\"text-gray-700 hover:text-altius-blue transition-colors\"\n            >\n              Услуги\n            </button>\n            <button\n              onClick={() => handleNavigation('/contact')}\n              className=\"text-gray-700 hover:text-altius-lime transition-colors\"\n            >\n              Контакты\n            </button>\n            <Link href=\"/admin\" className=\"text-gray-700 hover:text-altius-orange transition-colors\">\n              Админ\n            </Link>\n          </nav>\n\n          {/* Contact Info */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Phone className=\"w-4 h-4 mr-1\" />\n              +373 XX XXX XXX\n            </div>\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <MapPin className=\"w-4 h-4 mr-1\" />\n              Кишинев\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {isMenuOpen ? (\n              <X className=\"w-6 h-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"w-6 h-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <button\n                onClick={() => handleNavigation('/')}\n                className=\"text-gray-700 hover:text-altius-blue transition-colors text-left\"\n              >\n                Главная\n              </button>\n              <button\n                onClick={() => handleNavigation('/about')}\n                className=\"text-gray-700 hover:text-altius-lime transition-colors text-left\"\n              >\n                О нас\n              </button>\n              <button\n                onClick={() => handleNavigation('#halls')}\n                className=\"text-gray-700 hover:text-altius-orange transition-colors text-left\"\n              >\n                Залы\n              </button>\n              <button\n                onClick={() => handleNavigation('/services')}\n                className=\"text-gray-700 hover:text-altius-blue transition-colors text-left\"\n              >\n                Услуги\n              </button>\n              <button\n                onClick={() => handleNavigation('/contact')}\n                className=\"text-gray-700 hover:text-altius-lime transition-colors text-left\"\n              >\n                Контакты\n              </button>\n              <Link href=\"/admin\" className=\"text-gray-700 hover:text-altius-orange transition-colors\">\n                Админ\n              </Link>\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center text-sm text-gray-600 mb-2\">\n                  <Phone className=\"w-4 h-4 mr-1\" />\n                  +373 XX XXX XXX\n                </div>\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <MapPin className=\"w-4 h-4 mr-1\" />\n                  Кишинев\n                </div>\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,mBAAmB,CAAC;QACxB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,gEAAgE;YAChE,IAAI,aAAa,KAAK;gBACpB,OAAO,IAAI,CAAC,MAAM;YACpB,OAAO;gBACL,gDAAgD;gBAChD,MAAM,UAAU,SAAS,aAAa,CAAC;gBACvC,IAAI,SAAS;oBACX,QAAQ,cAAc,CAAC;wBAAE,UAAU;oBAAS;gBAC9C;YACF;QACF,OAAO;YACL,iBAAiB;YACjB,OAAO,IAAI,CAAC;QACd;QACA,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CAAsC;;;;;;8CAGrD,8OAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA2D;;;;;;;;;;;;sCAM3F,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE7B,2BACC,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAA2D;;;;;;0CAGzF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Footer.tsx"], "sourcesContent": ["import { Phone, MapPin, Clock, Mail } from 'lucide-react';\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {/* Company Info */}\n          <div>\n            <div className=\"text-2xl font-bold text-altius-lime mb-4\">\n              🏸 Altius\n            </div>\n            <p className=\"text-gray-300 mb-4\">\n              Современный бадминтонный клуб в Кишиневе с профессиональными кортами\n              и удобной системой бронирования.\n            </p>\n            <div className=\"flex items-center text-gray-300 mb-2\">\n              <MapPin className=\"w-4 h-4 mr-2\" />\n              Кишинев, Молдова\n            </div>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Контакты</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center text-gray-300\">\n                <Phone className=\"w-4 h-4 mr-2\" />\n                +373 XX XXX XXX\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <Mail className=\"w-4 h-4 mr-2\" />\n                <EMAIL>\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <MapPin className=\"w-4 h-4 mr-2\" />\n                ул. Примерная, 123, Кишинев\n              </div>\n            </div>\n          </div>\n\n          {/* Working Hours */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Режим работы</h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-gray-300\">\n                <Clock className=\"w-4 h-4 mr-2\" />\n                <div>\n                  <div>Пн-Пт: 06:00 - 23:00</div>\n                  <div>Сб-Вс: 08:00 - 22:00</div>\n                </div>\n              </div>\n            </div>\n            \n            {/* Halls Info */}\n            <div className=\"mt-6\">\n              <h4 className=\"font-semibold mb-2\">Наши залы:</h4>\n              <div className=\"text-sm text-gray-300 space-y-1\">\n                <div>Зал 1: 3 корта</div>\n                <div>Зал 2: 7 кортов</div>\n                <div>Зал 3: 7 кортов</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n          <p>&copy; 2024 Altius Кишинев. Все права защищены.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAA2C;;;;;;8CAG1D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOzC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;kEAAI;;;;;;kEACL,8OAAC;kEAAI;;;;;;;;;;;;;;;;;;;;;;;8CAMX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/app/services/page.tsx"], "sourcesContent": ["'use client';\n\nimport Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport {\n  Calendar,\n  Users,\n  Trophy,\n  GraduationCap,\n  Star,\n  Dumbbell,\n  Coffee,\n  Car,\n  Wifi,\n  ShowerHead,\n  Shirt\n} from 'lucide-react';\n\nexport default function ServicesPage() {\n  const mainServices = [\n    {\n      icon: Calendar,\n      title: 'Аренда кортов',\n      description: 'Почасовая аренда профессиональных кортов для игры в бадминтон',\n      features: [\n        'Корты международного стандарта',\n        'Профессиональное покрытие',\n        'Отличное освещение',\n        'Климат-контроль'\n      ],\n      price: 'от 150 лей/час',\n      popular: true\n    },\n    {\n      icon: GraduationCap,\n      title: 'Тренировки',\n      description: 'Индивидуальные и групповые тренировки с профессиональными тренерами',\n      features: [\n        'Индивидуальные занятия',\n        'Групповые тренировки',\n        'Детские секции',\n        'Программы для начинающих'\n      ],\n      price: 'от 300 лей/занятие',\n      popular: false\n    },\n    {\n      icon: Trophy,\n      title: 'Турниры',\n      description: 'Организация и проведение турниров различного уровня',\n      features: [\n        'Любительские турниры',\n        'Профессиональные соревнования',\n        'Корпоративные турниры',\n        'Детские соревнования'\n      ],\n      price: 'по запросу',\n      popular: false\n    },\n    {\n      icon: Users,\n      title: 'Корпоративные мероприятия',\n      description: 'Организация спортивных мероприятий для компаний',\n      features: [\n        'Тимбилдинг мероприятия',\n        'Корпоративные турниры',\n        'Аренда залов для мероприятий',\n        'Кейтеринг услуги'\n      ],\n      price: 'от 2000 лей/мероприятие',\n      popular: false\n    }\n  ];\n\n  const additionalServices = [\n    {\n      icon: Dumbbell,\n      title: 'Фитнес-зона',\n      description: 'Тренажерный зал для общей физической подготовки'\n    },\n    {\n      icon: Coffee,\n      title: 'Кафе',\n      description: 'Зона отдыха с напитками и легкими закусками'\n    },\n    {\n      icon: Car,\n      title: 'Парковка',\n      description: 'Бесплатная охраняемая парковка для посетителей'\n    },\n    {\n      icon: Wifi,\n      title: 'Wi-Fi',\n      description: 'Бесплатный высокоскоростной интернет'\n    },\n    {\n      icon: ShowerHead,\n      title: 'Душевые',\n      description: 'Современные душевые кабины с горячей водой'\n    },\n    {\n      icon: Shirt,\n      title: 'Прокат инвентаря',\n      description: 'Ракетки, воланы и спортивная форма в аренду'\n    }\n  ];\n\n  const pricingPlans = [\n    {\n      name: 'Разовое посещение',\n      price: '150-200',\n      period: 'лей/час',\n      description: 'Идеально для редких игр',\n      features: [\n        'Аренда корта на час',\n        'Доступ к раздевалкам',\n        'Бесплатная парковка',\n        'Wi-Fi'\n      ],\n      popular: false\n    },\n    {\n      name: 'Абонемент на месяц',\n      price: '1200',\n      period: 'лей/месяц',\n      description: 'Для регулярных тренировок',\n      features: [\n        '10 часов игры',\n        'Скидка 20% на дополнительные часы',\n        'Приоритетное бронирование',\n        'Доступ к фитнес-зоне'\n      ],\n      popular: true\n    },\n    {\n      name: 'VIP абонемент',\n      price: '2500',\n      period: 'лей/месяц',\n      description: 'Максимальный комфорт',\n      features: [\n        'Безлимитная игра',\n        'VIP раздевалки',\n        'Персональный тренер (2 занятия)',\n        'Бесплатный прокат инвентаря'\n      ],\n      popular: false\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-600 to-blue-800 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              Наши услуги\n            </h1>\n            <p className=\"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto\">\n              Полный спектр услуг для любителей бадминтона - \n              от аренды кортов до профессиональных тренировок\n            </p>\n          </div>\n        </div>\n      </section>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        {/* Main Services */}\n        <section className=\"mb-16\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Основные услуги</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Мы предлагаем широкий спектр услуг для игроков любого уровня\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n            {mainServices.map((service, index) => (\n              <div \n                key={index} \n                className={`bg-white rounded-xl shadow-lg p-6 relative ${\n                  service.popular ? 'ring-2 ring-blue-500' : ''\n                }`}\n              >\n                {service.popular && (\n                  <div className=\"absolute -top-3 left-6\">\n                    <span className=\"bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium\">\n                      Популярно\n                    </span>\n                  </div>\n                )}\n                \n                <div className=\"flex items-start mb-4\">\n                  <service.icon className=\"w-8 h-8 text-blue-600 mr-4 mt-1\" />\n                  <div>\n                    <h3 className=\"text-xl font-semibold mb-2\">{service.title}</h3>\n                    <p className=\"text-gray-600 mb-4\">{service.description}</p>\n                  </div>\n                </div>\n                \n                <ul className=\"space-y-2 mb-6\">\n                  {service.features.map((feature, featureIndex) => (\n                    <li key={featureIndex} className=\"flex items-center text-sm text-gray-600\">\n                      <Star className=\"w-4 h-4 text-blue-500 mr-2\" />\n                      {feature}\n                    </li>\n                  ))}\n                </ul>\n                \n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-2xl font-bold text-blue-600\">{service.price}</span>\n                  <button className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors\">\n                    Подробнее\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* Additional Services */}\n        <section className=\"mb-16\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Дополнительные услуги</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Мы заботимся о вашем комфорте и предлагаем множество дополнительных удобств\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {additionalServices.map((service, index) => (\n              <div key={index} className=\"bg-white rounded-xl shadow-lg p-6 text-center\">\n                <service.icon className=\"w-10 h-10 text-blue-600 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold mb-2\">{service.title}</h3>\n                <p className=\"text-gray-600 text-sm\">{service.description}</p>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* Pricing Plans */}\n        <section className=\"mb-16\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Тарифные планы</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Выберите подходящий тариф для регулярных занятий бадминтоном\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {pricingPlans.map((plan, index) => (\n              <div \n                key={index} \n                className={`bg-white rounded-xl shadow-lg p-6 relative ${\n                  plan.popular ? 'ring-2 ring-blue-500 scale-105' : ''\n                }`}\n              >\n                {plan.popular && (\n                  <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n                    <span className=\"bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium\">\n                      Рекомендуем\n                    </span>\n                  </div>\n                )}\n                \n                <div className=\"text-center mb-6\">\n                  <h3 className=\"text-xl font-semibold mb-2\">{plan.name}</h3>\n                  <div className=\"text-3xl font-bold text-blue-600 mb-1\">\n                    {plan.price}\n                  </div>\n                  <div className=\"text-gray-600 text-sm\">{plan.period}</div>\n                  <p className=\"text-gray-600 text-sm mt-2\">{plan.description}</p>\n                </div>\n                \n                <ul className=\"space-y-3 mb-6\">\n                  {plan.features.map((feature, featureIndex) => (\n                    <li key={featureIndex} className=\"flex items-center text-sm\">\n                      <Star className=\"w-4 h-4 text-blue-500 mr-2\" />\n                      {feature}\n                    </li>\n                  ))}\n                </ul>\n                \n                <button \n                  className={`w-full py-3 px-4 rounded-lg font-semibold transition-colors ${\n                    plan.popular \n                      ? 'bg-blue-600 hover:bg-blue-700 text-white' \n                      : 'bg-gray-100 hover:bg-gray-200 text-gray-700'\n                  }`}\n                >\n                  Выбрать план\n                </button>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* Contact CTA */}\n        <section className=\"bg-blue-600 rounded-xl text-white p-8 text-center\">\n          <h2 className=\"text-3xl font-bold mb-4\">Нужна консультация?</h2>\n          <p className=\"text-blue-100 mb-6 max-w-2xl mx-auto\">\n            Наши специалисты помогут выбрать подходящий тариф и ответят на все вопросы\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button \n              onClick={() => window.location.href = '/contact'}\n              className=\"bg-white text-blue-600 font-semibold py-3 px-8 rounded-lg hover:bg-blue-50 transition-colors\"\n            >\n              Связаться с нами\n            </button>\n            <button \n              onClick={() => window.location.href = '/#halls'}\n              className=\"border-2 border-white text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:text-blue-600 transition-colors\"\n            >\n              Забронировать корт\n            </button>\n          </div>\n        </section>\n      </div>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAkBe,SAAS;IACtB,MAAM,eAAe;QACnB;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,OAAO;YACP,SAAS;QACX;KACD;IAED,MAAM,qBAAqB;QACzB;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,eAAe;QACnB;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,SAAS;QACX;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAGP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAAsD;;;;;;;;;;;;;;;;;;;;;;0BAQzE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;0CAKjD,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,8OAAC;wCAEC,WAAW,CAAC,2CAA2C,EACrD,QAAQ,OAAO,GAAG,yBAAyB,IAC3C;;4CAED,QAAQ,OAAO,kBACd,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAoE;;;;;;;;;;;0DAMxF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;kEACxB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B,QAAQ,KAAK;;;;;;0EACzD,8OAAC;gEAAE,WAAU;0EAAsB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;0DAI1D,8OAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;wDAAsB,WAAU;;0EAC/B,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACf;;uDAFM;;;;;;;;;;0DAOb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAoC,QAAQ,KAAK;;;;;;kEACjE,8OAAC;wDAAO,WAAU;kEAAkF;;;;;;;;;;;;;uCAhCjG;;;;;;;;;;;;;;;;kCA0Cb,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;0CAKjD,8OAAC;gCAAI,WAAU;0CACZ,mBAAmB,GAAG,CAAC,CAAC,SAAS,sBAChC,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC,QAAQ,IAAI;gDAAC,WAAU;;;;;;0DACxB,8OAAC;gDAAG,WAAU;0DAA8B,QAAQ,KAAK;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAyB,QAAQ,WAAW;;;;;;;uCAHjD;;;;;;;;;;;;;;;;kCAUhB,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;0CAKjD,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC;wCAEC,WAAW,CAAC,2CAA2C,EACrD,KAAK,OAAO,GAAG,mCAAmC,IAClD;;4CAED,KAAK,OAAO,kBACX,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAoE;;;;;;;;;;;0DAMxF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA8B,KAAK,IAAI;;;;;;kEACrD,8OAAC;wDAAI,WAAU;kEACZ,KAAK,KAAK;;;;;;kEAEb,8OAAC;wDAAI,WAAU;kEAAyB,KAAK,MAAM;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAA8B,KAAK,WAAW;;;;;;;;;;;;0DAG7D,8OAAC;gDAAG,WAAU;0DACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,8OAAC;wDAAsB,WAAU;;0EAC/B,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACf;;uDAFM;;;;;;;;;;0DAOb,8OAAC;gDACC,WAAW,CAAC,4DAA4D,EACtE,KAAK,OAAO,GACR,6CACA,+CACJ;0DACH;;;;;;;uCArCI;;;;;;;;;;;;;;;;kCA8Cb,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wCACtC,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wCACtC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;0BAOP,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}