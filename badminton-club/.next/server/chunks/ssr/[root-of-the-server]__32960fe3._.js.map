{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { Menu, X, Phone, MapPin } from 'lucide-react';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const handleNavigation = (href: string) => {\n    if (href.startsWith('#')) {\n      // Якорная ссылка - переходим на главную страницу если не на ней\n      if (pathname !== '/') {\n        router.push('/' + href);\n      } else {\n        // Если уже на главной странице, просто скроллим\n        const element = document.querySelector(href);\n        if (element) {\n          element.scrollIntoView({ behavior: 'smooth' });\n        }\n      }\n    } else {\n      // Обычная ссылка\n      router.push(href);\n    }\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <div className=\"text-2xl font-bold text-altius-blue\">\n              🏸 Altius\n            </div>\n            <div className=\"ml-2 text-sm text-gray-600 hidden sm:block\">\n              Кишинев\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <button\n              onClick={() => handleNavigation('/')}\n              className=\"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer\"\n            >\n              Главная\n            </button>\n            <button\n              onClick={() => handleNavigation('/about')}\n              className=\"text-gray-700 hover:text-altius-lime transition-colors cursor-pointer\"\n            >\n              О нас\n            </button>\n            <button\n              onClick={() => handleNavigation('#halls')}\n              className=\"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer\"\n            >\n              Залы\n            </button>\n            <button\n              onClick={() => handleNavigation('/services')}\n              className=\"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer\"\n            >\n              Услуги\n            </button>\n            <button\n              onClick={() => handleNavigation('/contact')}\n              className=\"text-gray-700 hover:text-altius-lime transition-colors cursor-pointer\"\n            >\n              Контакты\n            </button>\n            <button\n              onClick={() => handleNavigation('/blog')}\n              className=\"text-gray-700 hover:text-altius-blue transition-colors cursor-pointer\"\n            >\n              Блог\n            </button>\n            <Link href=\"/admin\" className=\"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer\">\n              Админ\n            </Link>\n          </nav>\n\n          {/* Contact Info */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <Phone className=\"w-4 h-4 mr-1\" />\n              +373 XX XXX XXX\n            </div>\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <MapPin className=\"w-4 h-4 mr-1\" />\n              Кишинев\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden cursor-pointer\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            {isMenuOpen ? (\n              <X className=\"w-6 h-6 text-gray-700\" />\n            ) : (\n              <Menu className=\"w-6 h-6 text-gray-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <button\n                onClick={() => handleNavigation('/')}\n                className=\"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer\"\n              >\n                Главная\n              </button>\n              <button\n                onClick={() => handleNavigation('/about')}\n                className=\"text-gray-700 hover:text-altius-lime transition-colors text-left cursor-pointer\"\n              >\n                О нас\n              </button>\n              <button\n                onClick={() => handleNavigation('#halls')}\n                className=\"text-gray-700 hover:text-altius-orange transition-colors text-left cursor-pointer\"\n              >\n                Залы\n              </button>\n              <button\n                onClick={() => handleNavigation('/services')}\n                className=\"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer\"\n              >\n                Услуги\n              </button>\n              <button\n                onClick={() => handleNavigation('/contact')}\n                className=\"text-gray-700 hover:text-altius-lime transition-colors text-left cursor-pointer\"\n              >\n                Контакты\n              </button>\n              <button\n                onClick={() => handleNavigation('/blog')}\n                className=\"text-gray-700 hover:text-altius-blue transition-colors text-left cursor-pointer\"\n              >\n                Блог\n              </button>\n              <Link href=\"/admin\" className=\"text-gray-700 hover:text-altius-orange transition-colors cursor-pointer\">\n                Админ\n              </Link>\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"flex items-center text-sm text-gray-600 mb-2\">\n                  <Phone className=\"w-4 h-4 mr-1\" />\n                  +373 XX XXX XXX\n                </div>\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <MapPin className=\"w-4 h-4 mr-1\" />\n                  Кишинев\n                </div>\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,mBAAmB,CAAC;QACxB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,gEAAgE;YAChE,IAAI,aAAa,KAAK;gBACpB,OAAO,IAAI,CAAC,MAAM;YACpB,OAAO;gBACL,gDAAgD;gBAChD,MAAM,UAAU,SAAS,aAAa,CAAC;gBACvC,IAAI,SAAS;oBACX,QAAQ,cAAc,CAAC;wBAAE,UAAU;oBAAS;gBAC9C;YACF;QACF,OAAO;YACL,iBAAiB;YACjB,OAAO,IAAI,CAAC;QACd;QACA,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CAAsC;;;;;;8CAGrD,8OAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA0E;;;;;;;;;;;;sCAM1G,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE7B,2BACC,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAA0E;;;;;;0CAGxG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/components/Footer.tsx"], "sourcesContent": ["import { Phone, MapPin, Clock, Mail } from 'lucide-react';\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {/* Company Info */}\n          <div>\n            <div className=\"text-2xl font-bold text-altius-lime mb-4\">\n              🏸 Altius\n            </div>\n            <p className=\"text-gray-300 mb-4\">\n              Современный бадминтонный клуб в Кишиневе с профессиональными кортами\n              и удобной системой бронирования.\n            </p>\n            <div className=\"flex items-center text-gray-300 mb-2\">\n              <MapPin className=\"w-4 h-4 mr-2\" />\n              Кишинев, Молдова\n            </div>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Контакты</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center text-gray-300\">\n                <Phone className=\"w-4 h-4 mr-2\" />\n                +373 XX XXX XXX\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <Mail className=\"w-4 h-4 mr-2\" />\n                <EMAIL>\n              </div>\n              <div className=\"flex items-center text-gray-300\">\n                <MapPin className=\"w-4 h-4 mr-2\" />\n                ул. Примерная, 123, Кишинев\n              </div>\n            </div>\n          </div>\n\n          {/* Working Hours */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Режим работы</h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-gray-300\">\n                <Clock className=\"w-4 h-4 mr-2\" />\n                <div>\n                  <div>Пн-Пт: 06:00 - 23:00</div>\n                  <div>Сб-Вс: 08:00 - 22:00</div>\n                </div>\n              </div>\n            </div>\n            \n            {/* Halls Info */}\n            <div className=\"mt-6\">\n              <h4 className=\"font-semibold mb-2\">Наши залы:</h4>\n              <div className=\"text-sm text-gray-300 space-y-1\">\n                <div>Зал 1: 3 корта</div>\n                <div>Зал 2: 7 кортов</div>\n                <div>Зал 3: 7 кортов</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n          <p>&copy; 2024 Altius Кишинев. Все права защищены.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAA2C;;;;;;8CAG1D,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMvC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOzC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;kEAAI;;;;;;kEACL,8OAAC;kEAAI;;;;;;;;;;;;;;;;;;;;;;;8CAMX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\n// Get environment variables\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';\n\n// Check if Supabase is properly configured\nconst isSupabaseConfigured =\n  supabaseUrl &&\n  supabaseAnonKey &&\n  supabaseUrl.includes('supabase.co') &&\n  supabaseAnonKey.length > 100; // JWT tokens are long\n\n// Log configuration status for debugging\nconsole.log('Supabase Configuration:', {\n  url: supabaseUrl ? `${supabaseUrl.substring(0, 30)}...` : 'NOT SET',\n  keyLength: supabaseAnonKey.length,\n  isConfigured: isSupabaseConfigured\n});\n\n// Always use the provided values, even if they might be invalid\n// This way we get proper error messages instead of dummy URLs\nexport const supabase = createClient(\n  supabaseUrl || 'https://placeholder.supabase.co',\n  supabaseAnonKey || 'placeholder-key'\n);\n\n// Helper function to check if Supabase is available\nexport const isSupabaseAvailable = () => isSupabaseConfigured;\n\n// Database types\nexport interface Booking {\n  id: string;\n  name: string;\n  phone: string;\n  email?: string;\n  hall_id: number;\n  court: number;\n  date: string;\n  time: string;\n  status: 'pending' | 'confirmed' | 'cancelled';\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Hall {\n  id: number;\n  name: string;\n  courts_count: number;\n  price_per_hour: number;\n  description: string;\n  features: string[];\n  created_at: string;\n}\n\n// Booking functions\nexport const bookingService = {\n  // Get all bookings\n  async getBookings() {\n    const { data, error } = await supabase\n      .from('bookings')\n      .select('*')\n      .order('date', { ascending: true });\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Get bookings for a specific date and hall\n  async getBookingsByDateAndHall(date: string, hallId?: number) {\n    let query = supabase\n      .from('bookings')\n      .select('*')\n      .eq('date', date);\n    \n    if (hallId) {\n      query = query.eq('hall_id', hallId);\n    }\n    \n    const { data, error } = await query;\n    if (error) throw error;\n    return data;\n  },\n\n  // Create a new booking\n  async createBooking(booking: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) {\n    const { data, error } = await supabase\n      .from('bookings')\n      .insert([booking])\n      .select()\n      .single();\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Update booking status\n  async updateBookingStatus(id: string, status: 'pending' | 'confirmed' | 'cancelled') {\n    const { data, error } = await supabase\n      .from('bookings')\n      .update({ status, updated_at: new Date().toISOString() })\n      .eq('id', id)\n      .select()\n      .single();\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Delete a booking\n  async deleteBooking(id: string) {\n    const { error } = await supabase\n      .from('bookings')\n      .delete()\n      .eq('id', id);\n    \n    if (error) throw error;\n  },\n\n  // Check if a slot is available\n  async isSlotAvailable(hallId: number, court: number, date: string, time: string) {\n    const { data, error } = await supabase\n      .from('bookings')\n      .select('id')\n      .eq('hall_id', hallId)\n      .eq('court', court)\n      .eq('date', date)\n      .eq('time', time)\n      .neq('status', 'cancelled');\n    \n    if (error) throw error;\n    return data.length === 0;\n  }\n};\n\n// Hall functions\nexport const hallService = {\n  // Get all halls\n  async getHalls() {\n    const { data, error } = await supabase\n      .from('halls')\n      .select('*')\n      .order('id', { ascending: true });\n    \n    if (error) throw error;\n    return data;\n  },\n\n  // Get a specific hall\n  async getHall(id: number) {\n    const { data, error } = await supabase\n      .from('halls')\n      .select('*')\n      .eq('id', id)\n      .single();\n    \n    if (error) throw error;\n    return data;\n  }\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,4BAA4B;AAC5B,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AAErE,2CAA2C;AAC3C,MAAM,uBACJ,eACA,mBACA,YAAY,QAAQ,CAAC,kBACrB,gBAAgB,MAAM,GAAG,KAAK,sBAAsB;AAEtD,yCAAyC;AACzC,QAAQ,GAAG,CAAC,2BAA2B;IACrC,KAAK,uCAAc,GAAG,YAAY,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC;IACvD,WAAW,gBAAgB,MAAM;IACjC,cAAc;AAChB;AAIO,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACjC,eAAe,mCACf,mBAAmB;AAId,MAAM,sBAAsB,IAAM;AA4BlC,MAAM,iBAAiB;IAC5B,mBAAmB;IACnB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK;QAEnC,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,4CAA4C;IAC5C,MAAM,0BAAyB,IAAY,EAAE,MAAe;QAC1D,IAAI,QAAQ,SACT,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ;QAEd,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,WAAW;QAC9B;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAC9B,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,uBAAuB;IACvB,MAAM,eAAc,OAA0D;QAC5E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAC;SAAQ,EAChB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,wBAAwB;IACxB,MAAM,qBAAoB,EAAU,EAAE,MAA6C;QACjF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YAAE;YAAQ,YAAY,IAAI,OAAO,WAAW;QAAG,GACtD,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,mBAAmB;IACnB,MAAM,eAAc,EAAU;QAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,+BAA+B;IAC/B,MAAM,iBAAgB,MAAc,EAAE,KAAa,EAAE,IAAY,EAAE,IAAY;QAC7E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,SAAS,OACZ,EAAE,CAAC,QAAQ,MACX,EAAE,CAAC,QAAQ,MACX,GAAG,CAAC,UAAU;QAEjB,IAAI,OAAO,MAAM;QACjB,OAAO,KAAK,MAAM,KAAK;IACzB;AACF;AAGO,MAAM,cAAc;IACzB,gBAAgB;IAChB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,MAAM;YAAE,WAAW;QAAK;QAEjC,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,SAAQ,EAAU;QACtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 857, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Altius/badminton-club/src/app/blog/%5Bslug%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useParams, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport { supabase } from '@/lib/supabase';\nimport { Post } from '@/types';\nimport { \n  Calendar, \n  MapPin, \n  User, \n  Tag, \n  Clock, \n  Eye, \n  ArrowLeft,\n  Share2\n} from 'lucide-react';\n\nexport default function BlogPostPage() {\n  const params = useParams();\n  const router = useRouter();\n  const slug = params.slug as string;\n  \n  const [post, setPost] = useState<Post | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [relatedPosts, setRelatedPosts] = useState<Post[]>([]);\n\n  useEffect(() => {\n    if (slug) {\n      fetchPost();\n    }\n  }, [slug]);\n\n  const fetchPost = async () => {\n    console.log('Loading post with slug:', slug);\n    setLoading(true);\n\n    try {\n      // Fetch the post\n      const { data, error } = await supabase\n        .from('posts')\n        .select('*')\n        .eq('slug', slug)\n        .eq('status', 'published')\n        .single();\n\n      if (error) {\n        console.error('Supabase error:', error);\n        throw error;\n      }\n\n      if (data) {\n        console.log('Loaded post from Supabase:', data.title);\n        setPost(data);\n        \n        // Increment view count\n        await supabase\n          .from('posts')\n          .update({ views_count: data.views_count + 1 })\n          .eq('id', data.id);\n\n        // Fetch related posts\n        fetchRelatedPosts(data);\n      } else {\n        console.error('Post not found with slug:', slug);\n        router.push('/blog');\n      }\n    } catch (error) {\n      console.error('Error loading post:', error);\n      router.push('/blog');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchRelatedPosts = async (currentPost: Post) => {\n    try {\n      const { data, error } = await supabase\n        .from('posts')\n        .select('id, title, slug, excerpt, featured_image, category, created_at')\n        .eq('status', 'published')\n        .eq('category', currentPost.category)\n        .neq('id', currentPost.id)\n        .order('created_at', { ascending: false })\n        .limit(3);\n\n      if (error) {\n        console.error('Error fetching related posts:', error);\n        return;\n      }\n\n      if (data) {\n        setRelatedPosts(data);\n      }\n    } catch (error) {\n      console.error('Error fetching related posts:', error);\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('ru-RU', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const formatEventDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('ru-RU', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const sharePost = () => {\n    if (navigator.share && post) {\n      navigator.share({\n        title: post.title,\n        text: post.excerpt || '',\n        url: window.location.href,\n      });\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href);\n      alert('Ссылка скопирована в буфер обмена!');\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"text-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-altius-blue mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Загрузка поста...</p>\n          </div>\n        </main>\n        <Footer />\n      </div>\n    );\n  }\n\n  if (!post) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Header />\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"text-center py-12\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Пост не найден</h1>\n            <Link\n              href=\"/blog\"\n              className=\"text-altius-blue hover:text-blue-700\"\n            >\n              Вернуться к блогу\n            </Link>\n          </div>\n        </main>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Back Button */}\n        <div className=\"mb-8\">\n          <Link\n            href=\"/blog\"\n            className=\"inline-flex items-center text-altius-blue hover:text-blue-700 transition-colors\"\n          >\n            <ArrowLeft className=\"w-4 h-4 mr-2\" />\n            Назад к блогу\n          </Link>\n        </div>\n\n        {/* Article Layout */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n          {/* Main Content */}\n          <article className=\"lg:col-span-3 bg-white rounded-xl shadow-lg overflow-hidden\">\n            {/* Featured Image */}\n            {post.featured_image && (\n              <div className=\"aspect-video overflow-hidden\">\n                <img\n                  src={post.featured_image}\n                  alt={post.title}\n                  className=\"w-full h-full object-cover\"\n                />\n              </div>\n            )}\n\n            <div className=\"p-8 lg:p-12\">\n            {/* Title */}\n            <h1 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-8\">\n              {post.title}\n            </h1>\n\n            {/* Content */}\n            <div\n              className=\"prose prose-lg prose-blue max-w-none\"\n              dangerouslySetInnerHTML={{ __html: post.content }}\n            />\n\n            </div>\n          </article>\n\n          {/* Sidebar */}\n          <aside className=\"lg:col-span-1 space-y-6\">\n            {/* Post Info */}\n            <div className=\"bg-white rounded-xl shadow-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Информация</h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <span\n                    className={`px-3 py-1 rounded-full text-sm font-medium ${\n                      post.category === 'event'\n                        ? 'bg-altius-orange text-white'\n                        : 'bg-altius-lime text-white'\n                    }`}\n                  >\n                    {post.category === 'event' ? 'Событие' : 'Новость'}\n                  </span>\n                  <button\n                    onClick={sharePost}\n                    className=\"flex items-center text-gray-500 hover:text-altius-blue transition-colors\"\n                  >\n                    <Share2 className=\"w-5 h-5\" />\n                  </button>\n                </div>\n\n                <div className=\"flex items-center text-gray-500 text-sm\">\n                  <Eye className=\"w-4 h-4 mr-2\" />\n                  <span>{post.views_count + 1} просмотров</span>\n                </div>\n\n                <div className=\"flex items-center text-gray-500 text-sm\">\n                  <User className=\"w-4 h-4 mr-2\" />\n                  <span>{post.author_name}</span>\n                </div>\n\n                <div className=\"flex items-center text-gray-500 text-sm\">\n                  <Clock className=\"w-4 h-4 mr-2\" />\n                  <span>{formatDate(post.created_at)}</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Event Details in Sidebar */}\n            {post.category === 'event' && (\n              <div className=\"bg-altius-orange/10 border border-altius-orange/20 rounded-xl p-6\">\n                <h3 className=\"text-lg font-semibold text-altius-orange mb-4\">\n                  Детали события\n                </h3>\n                <div className=\"space-y-3\">\n                  {post.event_date && (\n                    <div className=\"flex items-start text-gray-700\">\n                      <Calendar className=\"w-5 h-5 mr-3 text-altius-orange mt-0.5\" />\n                      <div>\n                        <span className=\"font-medium block\">Дата и время:</span>\n                        <span className=\"text-sm\">{formatEventDate(post.event_date)}</span>\n                      </div>\n                    </div>\n                  )}\n                  {post.event_location && (\n                    <div className=\"flex items-start text-gray-700\">\n                      <MapPin className=\"w-5 h-5 mr-3 text-altius-orange mt-0.5\" />\n                      <div>\n                        <span className=\"font-medium block\">Место:</span>\n                        <span className=\"text-sm\">{post.event_location}</span>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n\n            {/* Tags in Sidebar */}\n            {post.tags && post.tags.length > 0 && (\n              <div className=\"bg-white rounded-xl shadow-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Теги</h3>\n                <div className=\"flex flex-wrap gap-2\">\n                  {post.tags.map((tag, index) => (\n                    <span\n                      key={index}\n                      className=\"inline-flex items-center px-3 py-1 rounded-md bg-gray-100 text-gray-700 text-sm\"\n                    >\n                      <Tag className=\"w-3 h-3 mr-1\" />\n                      {tag}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            )}\n          </aside>\n        </div>\n\n        {/* Related Posts */}\n        {relatedPosts.length > 0 && (\n          <section className=\"mt-12\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-8\">\n              Похожие {post.category === 'event' ? 'события' : 'новости'}\n            </h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              {relatedPosts.map((relatedPost) => (\n                <article\n                  key={relatedPost.id}\n                  className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\"\n                >\n                  {relatedPost.featured_image && (\n                    <div className=\"aspect-video overflow-hidden\">\n                      <img\n                        src={relatedPost.featured_image}\n                        alt={relatedPost.title}\n                        className=\"w-full h-full object-cover hover:scale-105 transition-transform duration-300\"\n                      />\n                    </div>\n                  )}\n                  <div className=\"p-4\">\n                    <span\n                      className={`inline-block px-2 py-1 rounded-full text-xs font-medium mb-2 ${\n                        relatedPost.category === 'event'\n                          ? 'bg-altius-orange text-white'\n                          : 'bg-altius-lime text-white'\n                      }`}\n                    >\n                      {relatedPost.category === 'event' ? 'Событие' : 'Новость'}\n                    </span>\n                    <h3 className=\"font-semibold text-gray-900 mb-2 line-clamp-2\">\n                      <Link\n                        href={`/blog/${relatedPost.slug}`}\n                        className=\"hover:text-altius-blue transition-colors\"\n                      >\n                        {relatedPost.title}\n                      </Link>\n                    </h3>\n                    {relatedPost.excerpt && (\n                      <p className=\"text-gray-600 text-sm line-clamp-2 mb-3\">\n                        {relatedPost.excerpt}\n                      </p>\n                    )}\n                    <div className=\"text-xs text-gray-500\">\n                      {formatDate(relatedPost.created_at)}\n                    </div>\n                  </div>\n                </article>\n              ))}\n            </div>\n          </section>\n        )}\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;AAoBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,OAAO,OAAO,IAAI;IAExB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,YAAY;QAChB,QAAQ,GAAG,CAAC,2BAA2B;QACvC,WAAW;QAEX,IAAI;YACF,iBAAiB;YACjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ,MACX,EAAE,CAAC,UAAU,aACb,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,mBAAmB;gBACjC,MAAM;YACR;YAEA,IAAI,MAAM;gBACR,QAAQ,GAAG,CAAC,8BAA8B,KAAK,KAAK;gBACpD,QAAQ;gBAER,uBAAuB;gBACvB,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,SACL,MAAM,CAAC;oBAAE,aAAa,KAAK,WAAW,GAAG;gBAAE,GAC3C,EAAE,CAAC,MAAM,KAAK,EAAE;gBAEnB,sBAAsB;gBACtB,kBAAkB;YACpB,OAAO;gBACL,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO,IAAI,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,kEACP,EAAE,CAAC,UAAU,aACb,EAAE,CAAC,YAAY,YAAY,QAAQ,EACnC,GAAG,CAAC,MAAM,YAAY,EAAE,EACxB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM,GACvC,KAAK,CAAC;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C;YACF;YAEA,IAAI,MAAM;gBACR,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,YAAY;QAChB,IAAI,UAAU,KAAK,IAAI,MAAM;YAC3B,UAAU,KAAK,CAAC;gBACd,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,OAAO,IAAI;gBACtB,KAAK,OAAO,QAAQ,CAAC,IAAI;YAC3B;QACF,OAAO;YACL,8BAA8B;YAC9B,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI;YAClD,MAAM;QACR;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;8BAGjC,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;IAGb;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAKL,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;IAGb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAM1C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAQ,WAAU;;oCAEhB,KAAK,cAAc,kBAClB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,KAAK,KAAK,cAAc;4CACxB,KAAK,KAAK,KAAK;4CACf,WAAU;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;;0DAEf,8OAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAIb,8OAAC;gDACC,WAAU;gDACV,yBAAyB;oDAAE,QAAQ,KAAK,OAAO;gDAAC;;;;;;;;;;;;;;;;;;0CAOpD,8OAAC;gCAAM,WAAU;;kDAEf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,WAAW,CAAC,2CAA2C,EACrD,KAAK,QAAQ,KAAK,UACd,gCACA,6BACJ;0EAED,KAAK,QAAQ,KAAK,UAAU,YAAY;;;;;;0EAE3C,8OAAC;gEACC,SAAS;gEACT,WAAU;0EAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAItB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;0EACf,8OAAC;;oEAAM,KAAK,WAAW,GAAG;oEAAE;;;;;;;;;;;;;kEAG9B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAM,KAAK,WAAW;;;;;;;;;;;;kEAGzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAM,WAAW,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;oCAMtC,KAAK,QAAQ,KAAK,yBACjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAgD;;;;;;0DAG9D,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,UAAU,kBACd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAoB;;;;;;kFACpC,8OAAC;wEAAK,WAAU;kFAAW,gBAAgB,KAAK,UAAU;;;;;;;;;;;;;;;;;;oDAI/D,KAAK,cAAc,kBAClB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAoB;;;;;;kFACpC,8OAAC;wEAAK,WAAU;kFAAW,KAAK,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCASzD,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC;wDAEC,WAAU;;0EAEV,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DACd;;uDAJI;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAclB,aAAa,MAAM,GAAG,mBACrB,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAG,WAAU;;oCAAwC;oCAC3C,KAAK,QAAQ,KAAK,UAAU,YAAY;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC;wCAEC,WAAU;;4CAET,YAAY,cAAc,kBACzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,KAAK,YAAY,cAAc;oDAC/B,KAAK,YAAY,KAAK;oDACtB,WAAU;;;;;;;;;;;0DAIhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,WAAW,CAAC,6DAA6D,EACvE,YAAY,QAAQ,KAAK,UACrB,gCACA,6BACJ;kEAED,YAAY,QAAQ,KAAK,UAAU,YAAY;;;;;;kEAElD,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,MAAM,EAAE,YAAY,IAAI,EAAE;4DACjC,WAAU;sEAET,YAAY,KAAK;;;;;;;;;;;oDAGrB,YAAY,OAAO,kBAClB,8OAAC;wDAAE,WAAU;kEACV,YAAY,OAAO;;;;;;kEAGxB,8OAAC;wDAAI,WAAU;kEACZ,WAAW,YAAY,UAAU;;;;;;;;;;;;;uCApCjC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;0BA8C/B,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}