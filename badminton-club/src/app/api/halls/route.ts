import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET() {
  try {
    const { data, error } = await supabase
      .from('halls')
      .select('*')
      .order('id');

    if (error) {
      console.error('Error fetching halls:', error);
      return NextResponse.json({ error: 'Failed to fetch halls' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const { data, error } = await supabase
      .from('halls')
      .insert([body])
      .select()
      .single();

    if (error) {
      console.error('Error creating hall:', error);
      return NextResponse.json({ error: 'Failed to create hall' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
