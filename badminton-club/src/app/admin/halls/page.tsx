'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { supabase } from '@/lib/supabase';
import {
  Edit,
  Eye,
  EyeOff,
  ArrowRight,
  Building,
  Users,
  DollarSign
} from 'lucide-react';

interface Hall {
  id: number;
  name: string;
  courts_count: number;
  price_per_hour: number;
  description: string;
  detailed_description: string;
  features: string[];
  images: string[];
  videos: string[];
  specifications: Record<string, string>;
  amenities: string[];
  working_hours: Record<string, string>;
  is_active: boolean;
}

export default function AdminHallsPage() {
  const [halls, setHalls] = useState<Hall[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    fetchHalls();
  }, []);

  const fetchHalls = async () => {
    try {
      const { data, error } = await supabase
        .from('halls')
        .select('*')
        .order('id');

      if (error) throw error;
      setHalls(data || []);
    } catch (error) {
      console.error('Error fetching halls:', error);
      // Fallback data if database is not available
      setHalls([
        {
          id: 1,
          name: 'Зал 1',
          courts_count: 3,
          price_per_hour: 150,
          description: 'Уютный зал с профессиональными кортами для игры в бадминтон',
          detailed_description: 'Зал 1 - это идеальное место для начинающих игроков и любителей бадминтона. Компактный и уютный зал оборудован тремя профессиональными кортами с качественным покрытием.',
          features: ['Профессиональное покрытие', 'Отличное освещение', 'Кондиционирование воздуха', 'Раздевалки с душем'],
          images: [],
          videos: [],
          specifications: {
            area: '300 м²',
            height: '9 м',
            flooring: 'Профессиональное покрытие Yonex'
          },
          amenities: ['Раздевалки', 'Душевые', 'Парковка', 'Wi-Fi'],
          working_hours: {
            weekdays: '06:00 - 23:00',
            weekends: '08:00 - 22:00'
          },
          is_active: true
        },
        {
          id: 2,
          name: 'Зал 2',
          courts_count: 7,
          price_per_hour: 180,
          description: 'Большой зал с семью кортами для турниров и тренировок',
          detailed_description: 'Зал 2 - наш самый большой зал, предназначенный для проведения турниров и серьезных тренировок. Семь профессиональных кортов с трибунами для зрителей.',
          features: ['Турнирные корты', 'Трибуны для зрителей', 'Профессиональная разметка'],
          images: [],
          videos: [],
          specifications: {
            area: '700 м²',
            height: '12 м',
            flooring: 'Турнирное покрытие BWF'
          },
          amenities: ['VIP раздевалки', 'Душевые', 'Трибуны', 'Парковка'],
          working_hours: {
            weekdays: '06:00 - 23:00',
            weekends: '08:00 - 22:00'
          },
          is_active: true
        },
        {
          id: 3,
          name: 'Зал 3',
          courts_count: 7,
          price_per_hour: 200,
          description: 'Современный зал с новейшим оборудованием',
          detailed_description: 'Зал 3 - наш новейший зал с самым современным оборудованием и технологиями. Семь кортов с инновационным покрытием и LED освещением.',
          features: ['Новейшее покрытие', 'LED освещение', 'Климат-контроль'],
          images: [],
          videos: [],
          specifications: {
            area: '700 м²',
            height: '12 м',
            flooring: 'Инновационное покрытие Victor'
          },
          amenities: ['VIP раздевалки', 'Премиум душевые', 'Зона отдыха'],
          working_hours: {
            weekdays: '06:00 - 23:00',
            weekends: '08:00 - 22:00'
          },
          is_active: true
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const navigateToHallEdit = (hallId: number) => {
    router.push(`/admin/halls/${hallId}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-altius-blue mx-auto"></div>
            <p className="mt-4 text-gray-600">Загрузка залов...</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Управление залами
          </h1>
          <p className="text-gray-600">
            Редактируйте информацию о залах, загружайте фотографии и управляйте контентом
          </p>
        </div>

        <div className="space-y-6">
          {halls.map((hall) => (
            <div key={hall.id} className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <h2 className="text-xl font-bold text-gray-900">
                      {editingHall?.id === hall.id ? (
                        <input
                          type="text"
                          value={editingHall.name}
                          onChange={(e) => setEditingHall({
                            ...editingHall,
                            name: e.target.value
                          })}
                          className="border border-gray-300 rounded-lg px-3 py-1 text-xl font-bold"
                        />
                      ) : (
                        hall.name
                      )}
                    </h2>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      hall.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {hall.is_active ? 'Активен' : 'Неактивен'}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => toggleHallStatus(hall.id, hall.is_active)}
                      className={`p-2 rounded-lg transition-colors ${
                        hall.is_active
                          ? 'text-red-600 hover:bg-red-50'
                          : 'text-green-600 hover:bg-green-50'
                      }`}
                      title={hall.is_active ? 'Деактивировать' : 'Активировать'}
                    >
                      {hall.is_active ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                    
                    {editingHall?.id === hall.id ? (
                      <>
                        <button
                          onClick={handleSave}
                          disabled={saving}
                          className="bg-altius-blue text-white px-4 py-2 rounded-lg hover:bg-altius-blue-dark transition-colors disabled:opacity-50"
                        >
                          <Save className="w-4 h-4 mr-2 inline" />
                          {saving ? 'Сохранение...' : 'Сохранить'}
                        </button>
                        <button
                          onClick={handleCancel}
                          className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
                        >
                          <X className="w-4 h-4 mr-2 inline" />
                          Отмена
                        </button>
                      </>
                    ) : (
                      <button
                        onClick={() => handleEdit(hall)}
                        className="bg-altius-lime text-white px-4 py-2 rounded-lg hover:bg-altius-lime-dark transition-colors"
                      >
                        <Edit className="w-4 h-4 mr-2 inline" />
                        Редактировать
                      </button>
                    )}
                  </div>
                </div>

                {/* Основная информация */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Количество кортов
                    </label>
                    {editingHall?.id === hall.id ? (
                      <input
                        type="number"
                        value={editingHall.courts_count}
                        onChange={(e) => setEditingHall({
                          ...editingHall,
                          courts_count: parseInt(e.target.value)
                        })}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2"
                      />
                    ) : (
                      <p className="text-gray-900">{hall.courts_count} кортов</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Цена за час (лей)
                    </label>
                    {editingHall?.id === hall.id ? (
                      <input
                        type="number"
                        value={editingHall.price_per_hour}
                        onChange={(e) => setEditingHall({
                          ...editingHall,
                          price_per_hour: parseInt(e.target.value)
                        })}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2"
                      />
                    ) : (
                      <p className="text-gray-900">{hall.price_per_hour} лей</p>
                    )}
                  </div>
                </div>

                {/* Описания */}
                <div className="space-y-4 mb-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Краткое описание
                    </label>
                    {editingHall?.id === hall.id ? (
                      <textarea
                        value={editingHall.description}
                        onChange={(e) => setEditingHall({
                          ...editingHall,
                          description: e.target.value
                        })}
                        rows={2}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2"
                      />
                    ) : (
                      <p className="text-gray-700">{hall.description}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Подробное описание
                    </label>
                    {editingHall?.id === hall.id ? (
                      <textarea
                        value={editingHall.detailed_description || ''}
                        onChange={(e) => setEditingHall({
                          ...editingHall,
                          detailed_description: e.target.value
                        })}
                        rows={4}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2"
                      />
                    ) : (
                      <p className="text-gray-700">{hall.detailed_description}</p>
                    )}
                  </div>
                </div>

                {/* Загрузка изображений */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Изображения зала
                  </label>
                  <div className="flex items-center space-x-4">
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={(e) => e.target.files && handleImageUpload(hall.id, e.target.files)}
                      className="hidden"
                      id={`images-${hall.id}`}
                    />
                    <label
                      htmlFor={`images-${hall.id}`}
                      className="bg-altius-orange text-white px-4 py-2 rounded-lg hover:bg-altius-orange-dark transition-colors cursor-pointer inline-flex items-center"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      {uploading ? 'Загрузка...' : 'Загрузить фото'}
                    </label>
                    <span className="text-sm text-gray-500">
                      {hall.images?.length || 0} изображений
                    </span>
                  </div>
                  
                  {/* Превью изображений */}
                  {hall.images && hall.images.length > 0 && (
                    <div className="mt-4 grid grid-cols-4 gap-2">
                      {hall.images.slice(0, 4).map((image, index) => (
                        <img
                          key={index}
                          src={image}
                          alt={`${hall.name} - ${index + 1}`}
                          className="w-full h-20 object-cover rounded-lg"
                        />
                      ))}
                      {hall.images.length > 4 && (
                        <div className="w-full h-20 bg-gray-100 rounded-lg flex items-center justify-center text-gray-500 text-sm">
                          +{hall.images.length - 4} еще
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </main>

      <Footer />
    </div>
  );
}
