# BadmintonClub Кишинев - Система бронирования

Современный веб-сайт для бадминтонного клуба в Кишиневе с системой онлайн бронирования кортов.

## Особенности

- 🏸 **3 зала с 17 кортами** - Зал 1 (3 корта), Зал 2 (7 кортов), Зал 3 (7 кортов)
- 📅 **Система бронирования** - Удобный календарь с отображением доступных/занятых слотов
- 💳 **Система оплаты** - QR-код и банковский перевод
- 👨‍💼 **Админ-панель** - Управление бронированиями и просмотр расписания
- 📱 **Адаптивный дизайн** - Корректное отображение на всех устройствах
- 🗄️ **База данных** - Интеграция с Supabase

## Технологии

- **Frontend**: Next.js 15, React 19, TypeScript
- **Стили**: Tailwind CSS
- **Иконки**: Lucide React
- **Формы**: React Hook Form + Zod
- **База данных**: Supabase (PostgreSQL)
- **Даты**: date-fns

## Установка и запуск

### 1. Клонирование и установка зависимостей

```bash
git clone <repository-url>
cd badminton-club
npm install
```

### 2. Настройка Supabase

1. Создайте проект в [Supabase](https://supabase.com)
2. Скопируйте `.env.local.example` в `.env.local`
3. Заполните переменные окружения:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

4. Выполните SQL скрипт из файла `supabase-schema.sql` в SQL Editor вашего Supabase проекта

### 3. Запуск проекта

```bash
npm run dev
```

Откройте [http://localhost:3000](http://localhost:3000) в браузере.

## Структура проекта

```
src/
├── app/
│   ├── admin/          # Админ-панель
│   ├── globals.css     # Глобальные стили
│   ├── layout.tsx      # Основной layout
│   └── page.tsx        # Главная страница
├── components/
│   ├── Header.tsx      # Шапка сайта
│   ├── Footer.tsx      # Подвал сайта
│   ├── HallCard.tsx    # Карточка зала
│   ├── BookingCalendar.tsx  # Календарь бронирования
│   └── BookingForm.tsx # Форма бронирования
└── lib/
    └── supabase.ts     # Конфигурация и API Supabase
```

## Функциональность

### Для клиентов

1. **Просмотр залов** - Информация о всех залах с фотографиями и характеристиками
2. **Бронирование** - Выбор зала, даты, времени и корта
3. **Форма заявки** - Ввод контактных данных
4. **Оплата** - QR-код или банковский перевод

### Для администраторов

1. **Панель управления** - `/admin`
2. **Просмотр всех бронирований** - Календарь и список
3. **Управление статусами** - Подтверждение/отмена бронирований
4. **Статистика** - Количество бронирований по статусам

## База данных

### Таблица `halls`
- `id` - ID зала
- `name` - Название зала
- `courts_count` - Количество кортов
- `price_per_hour` - Цена за час (в леях)
- `description` - Описание
- `features` - Особенности (JSON)

### Таблица `bookings`
- `id` - UUID бронирования
- `name` - Имя клиента
- `phone` - Телефон
- `email` - Email (опционально)
- `hall_id` - ID зала
- `court` - Номер корта
- `date` - Дата
- `time` - Время
- `status` - Статус (pending/confirmed/cancelled)

## Развертывание

### Vercel (рекомендуется)

1. Подключите репозиторий к Vercel
2. Добавьте переменные окружения в настройках проекта
3. Деплой произойдет автоматически

### Другие платформы

Проект совместим с любыми платформами, поддерживающими Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Кастомизация

### Изменение цен и информации о залах

Отредактируйте данные в файле `src/app/page.tsx` в массиве `halls` или обновите данные в Supabase.

### Изменение контактной информации

Обновите компоненты `Header.tsx` и `Footer.tsx`.

### Добавление новых функций

- Система уведомлений (email/SMS)
- Онлайн оплата
- Система скидок
- Бронирование на несколько часов
- Повторяющиеся бронирования

## Поддержка

Для вопросов и предложений создайте issue в репозитории.

## Лицензия

MIT License
