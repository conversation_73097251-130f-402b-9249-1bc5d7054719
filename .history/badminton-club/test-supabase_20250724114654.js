// Простой скрипт для тестирования подключения к Supabase
const { createClient } = require('@supabase/supabase-js');

// Загружаем переменные окружения
require('dotenv').config({ path: '.env.local' });

// Для тестирования используем прямые значения
const supabaseUrl = 'https://whdfkjsmyolbzlwtaoix.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndoZGZranNteW9sYnpsd3Rhb2l4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzNDUxODksImV4cCI6MjA2ODkyMTE4OX0.wodggEz_ElgvYVWPA4o3gmAg84AWezDmnRaHAkC2Dps';

console.log('🔗 Тестирование подключения к Supabase...');
console.log('URL:', supabaseUrl);
console.log('Key:', supabaseKey ? `${supabaseKey.substring(0, 20)}...` : 'НЕ НАЙДЕН');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Переменные окружения не настроены!');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  try {
    console.log('\n📊 Тестирование подключения к базе данных...');
    
    // Тест 1: Проверка подключения
    const { data, error } = await supabase
      .from('halls')
      .select('count(*)')
      .limit(1);
    
    if (error) {
      console.error('❌ Ошибка подключения:', error.message);
      return;
    }
    
    console.log('✅ Подключение к базе данных успешно!');
    
    // Тест 2: Получение данных о залах
    const { data: halls, error: hallsError } = await supabase
      .from('halls')
      .select('*')
      .limit(3);
    
    if (hallsError) {
      console.error('❌ Ошибка получения залов:', hallsError.message);
      return;
    }
    
    console.log(`✅ Найдено ${halls.length} залов в базе данных:`);
    halls.forEach(hall => {
      console.log(`   - ${hall.name} (${hall.courts_count} кортов, ${hall.price_per_hour} лей/час)`);
    });
    
    // Тест 3: Проверка Storage bucket
    const { data: buckets, error: storageError } = await supabase
      .storage
      .listBuckets();
    
    if (storageError) {
      console.error('❌ Ошибка Storage:', storageError.message);
    } else {
      const hallImagesBucket = buckets.find(b => b.name === 'hall-images');
      if (hallImagesBucket) {
        console.log('✅ Storage bucket "hall-images" настроен');
      } else {
        console.log('⚠️  Storage bucket "hall-images" не найден');
      }
    }
    
    console.log('\n🎉 Supabase успешно подключен и готов к работе!');
    
  } catch (error) {
    console.error('❌ Неожиданная ошибка:', error.message);
  }
}

testConnection();
