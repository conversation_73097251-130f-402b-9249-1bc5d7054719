# 🏸 Altius - Сайт Бадминтонного Клуба

Современный веб-сайт для бадминтонного клуба "Altius" в Кишиневе с полной системой управления контентом и административной панелью.

## 🎯 Описание Проекта

**Altius** - это профессиональный веб-сайт бадминтонного клуба, созданный с использованием современных технологий. Сайт предоставляет полную информацию о клубе, его услугах, залах и событиях, а также включает мощную административную панель для управления контентом.

## ✨ Основные Возможности

### 🏠 **Публичная Часть Сайта**

#### 📄 **Информационные Страницы**
- **Главная страница** - презентация клуба с активными залами
- **О клубе** - история, миссия и команда Altius
- **Услуги** - детальное описание предлагаемых услуг
- **Контакты** - адрес, телефоны, карта проезда

#### 🏟️ **Система Залов**
- **Каталог залов** - обзор всех доступных залов
- **Детальные страницы залов** с:
  - Профессиональными фотографиями
  - Техническими характеристиками
  - Списком удобств и оборудования
  - Ценами и расписанием работы
  - Галереей изображений с слайдером

#### 📝 **Блог и События**
- **Новости клуба** - актуальная информация
- **Анонсы событий** - турниры, мастер-классы, соревнования
- **Фильтрация контента** по категориям (новости/события)
- **Детальные страницы постов** с:
  - Полноэкранным просмотром изображений
  - Слайдерами для множественных фото
  - Информацией о событиях (дата, место)
  - Системой тегов
  - Счетчиком просмотров

### 🛠️ **Административная Панель**

#### 🏟️ **Управление Залами**
- **Создание и редактирование** залов
- **Загрузка изображений** с drag & drop функциональностью
- **Управление характеристиками** и удобствами
- **Настройка цен** и расписания
- **Активация/деактивация** залов

#### 📝 **Управление Контентом**
- **Создание постов и событий** через удобный редактор
- **Система галерей** с множественными изображениями
- **Слайдеры изображений** в постах
- **Управление статусами** (черновик/опубликовано)
- **SEO оптимизация** с мета-описаниями
- **Система тегов** для категоризации

#### 📊 **Аналитика и Статистика**
- **Счетчики просмотров** постов
- **Статистика залов** и бронирований
- **Обзор активности** в админ-панели

## 🛠️ Технологический Стек

### **Frontend**
- **Next.js 15** - React фреймворк с SSR/SSG
- **TypeScript** - типизированный JavaScript
- **Tailwind CSS** - utility-first CSS фреймворк
- **Lucide React** - современные иконки

### **Backend & База Данных**
- **Supabase** - Backend-as-a-Service платформа
- **PostgreSQL** - реляционная база данных
- **Row Level Security (RLS)** - безопасность на уровне строк
- **Real-time subscriptions** - обновления в реальном времени

### **Файловое Хранилище**
- **Supabase Storage** - облачное хранилище файлов
- **Автоматическая оптимизация** изображений
- **CDN доставка** контента

### **Деплой и Хостинг**
- **Vercel** - платформа для деплоя Next.js приложений
- **Автоматический CI/CD** из GitHub
- **Edge функции** для оптимальной производительности

## Установка и запуск

### 1. Клонирование и установка зависимостей

```bash
git clone <repository-url>
cd badminton-club
npm install
```

### 2. Настройка Supabase

1. Создайте проект в [Supabase](https://supabase.com)
2. Скопируйте `.env.local.example` в `.env.local`
3. Заполните переменные окружения:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

4. Выполните SQL скрипт из файла `supabase-schema.sql` в SQL Editor вашего Supabase проекта

### 3. Запуск проекта

```bash
npm run dev
```

Откройте [http://localhost:3000](http://localhost:3000) в браузере.

## Структура проекта

```
src/
├── app/
│   ├── admin/          # Админ-панель
│   ├── globals.css     # Глобальные стили
│   ├── layout.tsx      # Основной layout
│   └── page.tsx        # Главная страница
├── components/
│   ├── Header.tsx      # Шапка сайта
│   ├── Footer.tsx      # Подвал сайта
│   ├── HallCard.tsx    # Карточка зала
│   ├── BookingCalendar.tsx  # Календарь бронирования
│   └── BookingForm.tsx # Форма бронирования
└── lib/
    └── supabase.ts     # Конфигурация и API Supabase
```

## Функциональность

### Для клиентов

1. **Просмотр залов** - Информация о всех залах с фотографиями и характеристиками
2. **Бронирование** - Выбор зала, даты, времени и корта
3. **Форма заявки** - Ввод контактных данных
4. **Оплата** - QR-код или банковский перевод

### Для администраторов

1. **Панель управления** - `/admin`
2. **Просмотр всех бронирований** - Календарь и список
3. **Управление статусами** - Подтверждение/отмена бронирований
4. **Статистика** - Количество бронирований по статусам

## База данных

### Таблица `halls`
- `id` - ID зала
- `name` - Название зала
- `courts_count` - Количество кортов
- `price_per_hour` - Цена за час (в леях)
- `description` - Описание
- `features` - Особенности (JSON)

### Таблица `bookings`
- `id` - UUID бронирования
- `name` - Имя клиента
- `phone` - Телефон
- `email` - Email (опционально)
- `hall_id` - ID зала
- `court` - Номер корта
- `date` - Дата
- `time` - Время
- `status` - Статус (pending/confirmed/cancelled)

## Развертывание

### Vercel (рекомендуется)

1. Подключите репозиторий к Vercel
2. Добавьте переменные окружения в настройках проекта
3. Деплой произойдет автоматически

### Другие платформы

Проект совместим с любыми платформами, поддерживающими Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Кастомизация

### Изменение цен и информации о залах

Отредактируйте данные в файле `src/app/page.tsx` в массиве `halls` или обновите данные в Supabase.

### Изменение контактной информации

Обновите компоненты `Header.tsx` и `Footer.tsx`.

### Добавление новых функций

- Система уведомлений (email/SMS)
- Онлайн оплата
- Система скидок
- Бронирование на несколько часов
- Повторяющиеся бронирования

## Поддержка

Для вопросов и предложений создайте issue в репозитории.

## Лицензия

MIT License
