# 🏸 Altius - Сайт Бадминтонного Клуба

Современный веб-сайт для бадминтонного клуба "Altius" в Кишиневе с полной системой управления контентом и административной панелью.

## 🎯 Описание Проекта

**Altius** - это профессиональный веб-сайт бадминтонного клуба, созданный с использованием современных технологий. Сайт предоставляет полную информацию о клубе, его услугах, залах и событиях, а также включает мощную административную панель для управления контентом.

## ✨ Основные Возможности

### 🏠 **Публичная Часть Сайта**

#### 📄 **Информационные Страницы**
- **Главная страница** - презентация клуба с активными залами
- **О клубе** - история, миссия и команда Altius
- **Услуги** - детальное описание предлагаемых услуг
- **Контакты** - адрес, телефоны, карта проезда

#### 🏟️ **Система Залов**
- **Каталог залов** - обзор всех доступных залов
- **Детальные страницы залов** с:
  - Профессиональными фотографиями
  - Техническими характеристиками
  - Списком удобств и оборудования
  - Ценами и расписанием работы
  - Галереей изображений с слайдером

#### 📝 **Блог и События**
- **Новости клуба** - актуальная информация
- **Анонсы событий** - турниры, мастер-классы, соревнования
- **Фильтрация контента** по категориям (новости/события)
- **Детальные страницы постов** с:
  - Полноэкранным просмотром изображений
  - Слайдерами для множественных фото
  - Информацией о событиях (дата, место)
  - Системой тегов
  - Счетчиком просмотров

### 🛠️ **Административная Панель**

#### 🏟️ **Управление Залами**
- **Создание и редактирование** залов
- **Загрузка изображений** с drag & drop функциональностью
- **Управление характеристиками** и удобствами
- **Настройка цен** и расписания
- **Активация/деактивация** залов

#### 📝 **Управление Контентом**
- **Создание постов и событий** через удобный редактор
- **Система галерей** с множественными изображениями
- **Слайдеры изображений** в постах
- **Управление статусами** (черновик/опубликовано)
- **SEO оптимизация** с мета-описаниями
- **Система тегов** для категоризации

#### 📊 **Аналитика и Статистика**
- **Счетчики просмотров** постов
- **Статистика залов** и бронирований
- **Обзор активности** в админ-панели

## 🛠️ Технологический Стек

### **Frontend**
- **Next.js 15** - React фреймворк с SSR/SSG
- **TypeScript** - типизированный JavaScript
- **Tailwind CSS** - utility-first CSS фреймворк
- **Lucide React** - современные иконки

### **Backend & База Данных**
- **Supabase** - Backend-as-a-Service платформа
- **PostgreSQL** - реляционная база данных
- **Row Level Security (RLS)** - безопасность на уровне строк
- **Real-time subscriptions** - обновления в реальном времени

### **Файловое Хранилище**
- **Supabase Storage** - облачное хранилище файлов
- **Автоматическая оптимизация** изображений
- **CDN доставка** контента

### **Деплой и Хостинг**
- **Vercel** - платформа для деплоя Next.js приложений
- **Автоматический CI/CD** из GitHub
- **Edge функции** для оптимальной производительности

## 📁 Структура Проекта

```
badminton-club/
├── src/
│   ├── app/                    # App Router страницы
│   │   ├── admin/             # Административная панель
│   │   │   ├── halls/         # Управление залами
│   │   │   └── posts/         # Управление постами
│   │   ├── blog/              # Блог и события
│   │   ├── halls/             # Страницы залов
│   │   └── ...                # Другие страницы
│   ├── components/            # React компоненты
│   │   ├── Header.tsx         # Навигация
│   │   ├── Footer.tsx         # Подвал сайта
│   │   ├── MediaGallery.tsx   # Галерея изображений
│   │   ├── PostImageSlider.tsx # Слайдер для постов
│   │   └── ...                # Другие компоненты
│   ├── lib/                   # Утилиты и конфигурация
│   │   └── supabase.ts        # Клиент Supabase
│   └── types/                 # TypeScript типы
├── public/                    # Статические файлы
├── supabase-schema.sql        # Схема базы данных
└── ...                        # Конфигурационные файлы
```

## 🗄️ База Данных

### **Таблицы**

#### **halls** - Залы
- Информация о залах (название, описание, цены)
- Характеристики и удобства
- Галереи изображений и видео
- Статус активности

#### **posts** - Посты и События
- Заголовки и контент (поддержка HTML)
- Категории (новости/события)
- Галереи изображений для слайдеров
- SEO метаданные и теги
- Информация о событиях (дата, место)

#### **bookings** - Бронирования (готово к расширению)
- Система бронирования залов
- Пользовательские данные
- Временные слоты и статусы

### **Storage Buckets**
- **hall-images** - изображения залов
- **post-images** - изображения для постов и событий

## 🎨 Дизайн и UX

### **Цветовая Схема Altius**
- **Синий** (`altius-blue`) - основной цвет бренда
- **Лайм** (`altius-lime`) - акцентный цвет
- **Оранжевый** (`altius-orange`) - цвет событий

### **Responsive Дизайн**
- **Мобильная оптимизация** для всех устройств
- **Touch-friendly** интерфейс
- **Адаптивные изображения** и галереи

### **Пользовательский Опыт**
- **Быстрая навигация** с плавными переходами
- **Интуитивная админ-панель**
- **Drag & drop** загрузка файлов
- **Полноэкранные галереи** изображений

## 🚀 Установка и Запуск

### **Требования**
- Node.js 18+
- npm или yarn
- Аккаунт Supabase

### **Локальная Разработка**

1. **Клонирование репозитория**
```bash
git clone https://github.com/YR2607/altius-badminton-club.git
cd altius-badminton-club
```

2. **Установка зависимостей**
```bash
npm install
```

3. **Настройка переменных окружения**
```bash
cp .env.example .env.local
# Отредактируйте .env.local с вашими Supabase ключами
```

4. **Запуск в режиме разработки**
```bash
npm run dev
```

5. **Открыть в браузере**
```
http://localhost:3000
```

### **Продакшн Деплой**

Подробные инструкции в файле `DEPLOYMENT.md`

## Структура проекта

```
src/
├── app/
│   ├── admin/          # Админ-панель
│   ├── globals.css     # Глобальные стили
│   ├── layout.tsx      # Основной layout
│   └── page.tsx        # Главная страница
├── components/
│   ├── Header.tsx      # Шапка сайта
│   ├── Footer.tsx      # Подвал сайта
│   ├── HallCard.tsx    # Карточка зала
│   ├── BookingCalendar.tsx  # Календарь бронирования
│   └── BookingForm.tsx # Форма бронирования
└── lib/
    └── supabase.ts     # Конфигурация и API Supabase
```

## Функциональность

### Для клиентов

1. **Просмотр залов** - Информация о всех залах с фотографиями и характеристиками
2. **Бронирование** - Выбор зала, даты, времени и корта
3. **Форма заявки** - Ввод контактных данных
4. **Оплата** - QR-код или банковский перевод

### Для администраторов

1. **Панель управления** - `/admin`
2. **Просмотр всех бронирований** - Календарь и список
3. **Управление статусами** - Подтверждение/отмена бронирований
4. **Статистика** - Количество бронирований по статусам

## База данных

### Таблица `halls`
- `id` - ID зала
- `name` - Название зала
- `courts_count` - Количество кортов
- `price_per_hour` - Цена за час (в леях)
- `description` - Описание
- `features` - Особенности (JSON)

### Таблица `bookings`
- `id` - UUID бронирования
- `name` - Имя клиента
- `phone` - Телефон
- `email` - Email (опционально)
- `hall_id` - ID зала
- `court` - Номер корта
- `date` - Дата
- `time` - Время
- `status` - Статус (pending/confirmed/cancelled)

## Развертывание

### Vercel (рекомендуется)

1. Подключите репозиторий к Vercel
2. Добавьте переменные окружения в настройках проекта
3. Деплой произойдет автоматически

### Другие платформы

Проект совместим с любыми платформами, поддерживающими Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Кастомизация

### Изменение цен и информации о залах

Отредактируйте данные в файле `src/app/page.tsx` в массиве `halls` или обновите данные в Supabase.

### Изменение контактной информации

Обновите компоненты `Header.tsx` и `Footer.tsx`.

### Добавление новых функций

- Система уведомлений (email/SMS)
- Онлайн оплата
- Система скидок
- Бронирование на несколько часов
- Повторяющиеся бронирования

## Поддержка

Для вопросов и предложений создайте issue в репозитории.

## Лицензия

MIT License
