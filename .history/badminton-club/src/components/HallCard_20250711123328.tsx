import { Users, MapPin, Calendar } from 'lucide-react';

interface HallCardProps {
  id: number;
  name: string;
  courts: number;
  image: string;
  description: string;
  features: string[];
  pricePerHour: number;
  onBookClick: (hallId: number) => void;
}

export default function HallCard({
  id,
  name,
  courts,
  image,
  description,
  features,
  pricePerHour,
  onBookClick
}: HallCardProps) {
  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
      {/* Image */}
      <div className="relative h-64 bg-gradient-to-br from-blue-500 to-blue-700">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="text-6xl mb-2">🏸</div>
            <div className="text-xl font-semibold">{name}</div>
          </div>
        </div>
        <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
          <div className="flex items-center text-sm font-semibold text-gray-700">
            <Users className="w-4 h-4 mr-1" />
            {courts} кортов
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-2">{name}</h3>
        <p className="text-gray-600 mb-4">{description}</p>

        {/* Features */}
        <div className="mb-4">
          <h4 className="text-sm font-semibold text-gray-700 mb-2">Особенности:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            {features.map((feature, index) => (
              <li key={index} className="flex items-center">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></div>
                {feature}
              </li>
            ))}
          </ul>
        </div>

        {/* Price and Location */}
        <div className="flex items-center justify-between mb-4">
          <div className="text-lg font-bold text-blue-600">
            {pricePerHour} лей/час
          </div>
          <div className="flex items-center text-sm text-gray-500">
            <MapPin className="w-4 h-4 mr-1" />
            Кишинев
          </div>
        </div>

        {/* Book Button */}
        <button
          onClick={() => onBookClick(id)}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
        >
          <Calendar className="w-4 h-4 mr-2" />
          Забронировать
        </button>
      </div>
    </div>
  );
}
