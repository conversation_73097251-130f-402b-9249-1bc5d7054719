'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { Menu, X, Phone, MapPin } from 'lucide-react';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <div className="flex items-center">
            <div className="text-2xl font-bold text-blue-600">
              🏸 BadmintonClub
            </div>
            <div className="ml-2 text-sm text-gray-600 hidden sm:block">
              Кишинев
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <a href="#home" className="text-gray-700 hover:text-blue-600 transition-colors">
              Главная
            </a>
            <a href="#halls" className="text-gray-700 hover:text-blue-600 transition-colors">
              Залы
            </a>
            <a href="#booking" className="text-gray-700 hover:text-blue-600 transition-colors">
              Бронирование
            </a>
            <a href="#contact" className="text-gray-700 hover:text-blue-600 transition-colors">
              Контакты
            </a>
            <a href="/admin" className="text-gray-700 hover:text-blue-600 transition-colors">
              Админ
            </a>
          </nav>

          {/* Contact Info */}
          <div className="hidden lg:flex items-center space-x-4">
            <div className="flex items-center text-sm text-gray-600">
              <Phone className="w-4 h-4 mr-1" />
              +373 XX XXX XXX
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <MapPin className="w-4 h-4 mr-1" />
              Кишинев
            </div>
          </div>

          {/* Mobile menu button */}
          <button
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? (
              <X className="w-6 h-6 text-gray-700" />
            ) : (
              <Menu className="w-6 h-6 text-gray-700" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200">
            <nav className="flex flex-col space-y-4">
              <a href="#home" className="text-gray-700 hover:text-blue-600 transition-colors">
                Главная
              </a>
              <a href="#halls" className="text-gray-700 hover:text-blue-600 transition-colors">
                Залы
              </a>
              <a href="#booking" className="text-gray-700 hover:text-blue-600 transition-colors">
                Бронирование
              </a>
              <a href="#contact" className="text-gray-700 hover:text-blue-600 transition-colors">
                Контакты
              </a>
              <a href="/admin" className="text-gray-700 hover:text-blue-600 transition-colors">
                Админ
              </a>
              <div className="pt-4 border-t border-gray-200">
                <div className="flex items-center text-sm text-gray-600 mb-2">
                  <Phone className="w-4 h-4 mr-1" />
                  +373 XX XXX XXX
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <MapPin className="w-4 h-4 mr-1" />
                  Кишинев
                </div>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
